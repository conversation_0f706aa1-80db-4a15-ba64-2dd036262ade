# EMRrate-Management-System

电子病历评级文档管理系统

## 功能说明

### 文档转换功能
系统支持多种文档格式转换，便于文档的处理和展示：

1. **Word转HTML**: 将Word文档转换为HTML格式，支持在线预览
2. **Word转PDF**: 将Word文档转换为PDF格式
3. **Word转OFD**: 将Word文档转换为OFD(开放版式文档)格式

### 加密工具类
系统提供统一的加密解密工具类，便于数据安全管理：

1. **HashCryptoFactory**: 提供哈希加密工厂，支持多种哈希算法（SM3和MD5），用于密码等敏感信息的单向加密
2. **SM3Util**: 提供国密SM3哈希算法实现，用于密码等敏感信息的单向加密
3. **MD5Util**: 提供MD5哈希算法实现，向下兼容旧系统
4. **SymmetricCryptoFactory**: 提供对称加密工厂，支持多种对称加密算法（SM4和AES），用于数据的可逆加密和解密
5. **加密用途**: 主要用于数据库密码等敏感信息的加密存储和解密使用

### 数据源跨库关联查询功能
系统支持配置数据源是否需要跨库关联查询功能：

1. **功能说明**: 当数据源配置为需要跨库关联查询时，系统会自动配置Presto相关环境
2. **配置流程**: 
   - 在数据源中设置"是否需要跨库关联查询"为"是"
   - 系统自动在/home/<USER>/aes/pw.env文件中添加或修改数据源密码配置
   - 系统自动在/home/<USER>/catalog/目录下创建或修改对应的properties文件
   - 系统自动执行Docker相关命令重新构建和启动Presto容器
3. **支持的数据库类型**: MySQL、Oracle、SQL Server、PostgreSQL等
4. **全局配置项**: 
   - 在application.yml中可以配置是否开启自动配置跨库catalog功能
   - 默认关闭，需要手动开启才会执行自动配置
   ```yaml
   # 跨库查询配置
   cross-query:
     auto-catalog-config: false  # 是否开启自动配置跨库catalog，默认关闭
     presto-docker-path: /home/<USER>
     presto-env-password: Jykj1994@  # Presto环境密码，用于加密env.encrypted文件
   ```

### 配置加密算法
系统支持通过配置文件指定默认加密算法，可在application.yml或各环境配置文件中设置：

```yaml
# 加密算法配置
crypto:
  hash-algorithm: SM3  # 可选值: SM3, MD5 - 用于哈希加密（如密码哈希）
  symmetric-algorithm: SM4  # 可选值: SM4, AES - 用于对称加密（如数据加密）
```

### Word表格单元格合并功能
系统支持在生成Word文档时对表格进行单元格合并，特别是针对一致性报告中的类别列合并：

1. **DetailTableCategoryMergePolicy**: 专门用于table1的类别列合并
   - 自动识别相同系统（医院项目中"-"前的内容）
   - 将相同系统的行在"类别"列进行单元格合并
   - 前3列（类别、要求项目、医院项目）上下左右完全居中对齐，其他列左对齐
   - 保持表格格式和样式的一致性

3. **使用场景**:
   - 报告生成
   - table1：当医院项目包含多个相同系统的项目时，自动合并类别列，前3列居中显示
   - table2：统计数据表格，所有列居中显示，提高数据展示的专业性
   - 提高报告的可读性和专业性

4. **实现原理**:
   - 继承poi-tl的DynamicTableRenderPolicy
   - 在渲染表格时统计相同类别的行数
   - 使用Apache POI的VMerge功能实现垂直单元格合并
   - 使用ParagraphAlignment.CENTER设置文字水平居中对齐
   - 使用STVerticalJc.CENTER设置单元格垂直居中对齐
   - 使用ParagraphAlignment.LEFT设置文字左对齐

### 数据分组合并功能
系统支持对统计数据按系统进行分组合并，特别是针对一致性报告中的table2数据处理：

1. **mergeTable2DataBySystem**: 专门用于报告中table2的系统分组合并
   - 自动识别相同系统（医院项目中"-"前的内容）
   - 将相同系统的多个项目数据进行累加合并
   - 重新计算一致性比例，保持数据准确性

2. **使用场景**:
   - YZX一致性报告生成
   - 当同一系统包含多个检查项目时，自动合并统计数据
   - 简化报告结构，提高数据可读性

3. **合并规则**:
   - 按项目类型分组：将相同项目类型的不同系统数据合并到一行
   - 医院项目列：只显示项目类型名称，如"项目代码"、"检查项目"
   - 记录总数T列：显示该项目类型下所有系统的数据，格式为"系统名称 数量"，最后一行显示"合计 累加总数"
   - 有对照记录数C列：显示该项目类型下所有系统的数据，格式为"系统名称 数量"，最后一行显示"合计 累加匹配数"
   - 一致性比例：使用该项目类型的合计数据计算 C/T 的比例
   - 序号：按项目类型重新编号

### 使用方法

#### Word转OFD
```java
// 示例：将Word文档转换为OFD格式
try {
    String wordFilePath = "文件路径/文档.docx";
    String ofdOutputPath = "文件路径/文档.ofd";
    WordUtil.docxToOFD(wordFilePath, ofdOutputPath);
} catch (Exception e) {
    e.printStackTrace();
}
```

#### 哈希加密（单向加密）
```java
// 示例：使用HashCryptoFactory进行哈希加密
String content = "需要加密的内容";
// 使用默认哈希加密算法(配置文件中指定)加密
String encrypted = HashCryptoFactory.encrypt(content);

// 使用指定算法加密
CryptoUtil md5Crypto = HashCryptoFactory.getCrypto("MD5");
String md5Encrypted = md5Crypto.encrypt(content);

CryptoUtil sm3Crypto = HashCryptoFactory.getCrypto("SM3");
String sm3Encrypted = sm3Crypto.encrypt(content);
```

#### 对称加密和解密（可逆加密）
```java
// 示例：使用SymmetricCryptoFactory进行对称加密和解密
String content = "需要加密的内容";

// 使用默认对称加密算法(配置文件中指定)加密
String encrypted = SymmetricCryptoFactory.encrypt(content);
// 解密
String decrypted = SymmetricCryptoFactory.decrypt(encrypted);

// 使用指定算法加密解密
SymmetricCryptoUtil aesCrypto = SymmetricCryptoFactory.getCrypto("AES");
String aesEncrypted = aesCrypto.encrypt(content);
String aesDecrypted = aesCrypto.decrypt(aesEncrypted);

SymmetricCryptoUtil sm4Crypto = SymmetricCryptoFactory.getCrypto("SM4");
String sm4Encrypted = sm4Crypto.encrypt(content);
String sm4Decrypted = sm4Crypto.decrypt(sm4Encrypted);

// 判断是否是加密内容
boolean isEncrypted = SymmetricCryptoFactory.isEncrypted(someString);
```

#### Word表格类别列合并
```java
// 示例：生成支持类别列合并的表格
WordUtils wordUtils = new WordUtils();

// 准备表格数据，包含类别列
List<Map<String, Object>> table1Data = new ArrayList<>();
// 标题行
Map<String, Object> headerMap = new LinkedHashMap<>();
headerMap.put("zero", "类别");
headerMap.put("one", "要求项目");
headerMap.put("two", "医院项目");
headerMap.put("three", "数据库表与字段名");
headerMap.put("four", "数据字典表与字段名");
table1Data.add(headerMap);

// 数据行 - 相同系统的项目会在类别列自动合并
Map<String, Object> dataMap1 = new LinkedHashMap<>();
dataMap1.put("zero", "心电检查项目");
dataMap1.put("one", "心电-检查项目名称");
dataMap1.put("two", "心电-检查项目名称");
dataMap1.put("three", "dqm_dataquality_check_rule");
dataMap1.put("four", "dqm_dataquality_qstn_detail");
table1Data.add(dataMap1);

Map<String, Object> dataMap2 = new LinkedHashMap<>();
dataMap2.put("zero", "心电检查项目");  // 相同系统，会与上一行合并
dataMap2.put("one", "心电-检查项目编码");
dataMap2.put("two", "心电-检查项目编码");
dataMap2.put("three", "CHECK_ITEM_NAME");
dataMap2.put("four", "BEST_MERGED_RECORD_INDEX_S");
table1Data.add(dataMap2);

// 生成支持类别列合并的表格（前3列居中对齐）
wordUtils.generateTableWithCategoryMerge(table1Data, "输出文件路径/table1");

// 生成所有列居中对齐的表格
wordUtils.generateTableWithCenterAlign(table2Data, "输出文件路径/table2");
```

#### 数据分组合并处理
```java
// 示例：对table2数据按系统进行分组合并
// 原始数据（4行）
List<Map<String, Object>> originalTable2Data = new ArrayList<>();
// 标题行
Map<String, Object> headerMap = new LinkedHashMap<>();
headerMap.put("serialNum", "序号");
headerMap.put("one", "医院项目");
headerMap.put("two", "记录总数T");
headerMap.put("three", "有对照记录数C");
headerMap.put("four", "一致性比例");
originalTable2Data.add(headerMap);

// 数据行
Map<String, Object> row1 = new LinkedHashMap<>();
row1.put("serialNum", 1);
row1.put("one", "心电-检查项目名称");
row1.put("two", 15349L);
row1.put("three", 6910L);
row1.put("four", "0.450000");
originalTable2Data.add(row1);

Map<String, Object> row2 = new LinkedHashMap<>();
row2.put("serialNum", 2);
row2.put("one", "心电-检查项目编码");
row2.put("two", 22259L);
row2.put("three", 6910L);
row2.put("four", "0.310000");
originalTable2Data.add(row2);

Map<String, Object> row3 = new LinkedHashMap<>();
row3.put("serialNum", 3);
row3.put("one", "检查-检查项目名称");
row3.put("two", 15349L);
row3.put("three", 6910L);
row3.put("four", "0.450000");
originalTable2Data.add(row3);

Map<String, Object> row4 = new LinkedHashMap<>();
row4.put("serialNum", 4);
row4.put("one", "检查-检查项目编码");
row4.put("two", 22259L);
row4.put("three", 6910L);
row4.put("four", "0.310000");
originalTable2Data.add(row4);

// 调用合并方法（在YZXGenerateChapterWord中）
List<Map<String, Object>> mergedTable2Data = mergeTable2DataBySystem(originalTable2Data);

// 合并后的数据（2行）：
// 序号1:
//   医院项目列: "项目代码"
//   记录总数T列: "检查 15349\n心电 22259\n合计 37608"
//   有对照记录数C列: "检查 6910\n心电 6910\n合计 19820"
//   一致性比例: "0.527647"
// 序号2:
//   医院项目列: "检查项目"
//   记录总数T列: "检查 15349\n心电 22259\n合计 37608"
//   有对照记录数C列: "检查 6910\n心电 6910\n合计 19820"
//   一致性比例: "0.527647"
```