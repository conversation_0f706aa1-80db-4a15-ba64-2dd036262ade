package com.jykj.dqm.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.metadata.entity.CollectionTaskInfoDTO;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 元数据采集任务
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/24 14:21:26
 */
@Mapper
public interface MetaDataCollectionMapper extends BaseMapper<ScheduleJobInfo> {
    List<ScheduleJobInfo> getScheduleJobInfo(CollectionTaskInfoDTO collectionTaskInfoDto);
}
