package com.jykj.dqm.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.metadata.entity.MetadataDatasourceQueryDTO;
import com.jykj.dqm.quality.entity.RuleFirstPageQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 数据源管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/22 11:38:16
 */
@Mapper
public interface MetadataDatasourceMapper extends BaseMapper<MetadataDatasource> {
    List<MetadataDatasource> getDatasourceByParams(MetadataDatasourceQueryDTO metadataDatasource);

    List<Map<String, Object>> firstPageList(RuleFirstPageQueryDTO ruleFirstPageQueryDTO);

    List<Map<String, Object>> querySysAndDb();
}