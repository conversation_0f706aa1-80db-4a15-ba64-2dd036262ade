package com.jykj.dqm.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.metadata.entity.MetadataStructureChooseResult;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 库表结构选择结果
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/14 13:43:20
 */
@Mapper
public interface MetadataStructureChooseResultMapper extends BaseMapper<MetadataStructureChooseResult> {
    void saveBatchMy(List<MetadataStructureChooseResult> list);
}