package com.jykj.dqm.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.metadata.entity.MetadataTaskInstance;
import com.jykj.dqm.metadata.entity.MetadataTaskInstanceDTO;
import com.jykj.dqm.metadata.entity.MetadataTaskInstanceVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 元数据调度任务记录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 13:53:47
 */
@Mapper
public interface MetadataTaskInstanceMapper extends BaseMapper<MetadataTaskInstance> {
    List<MetadataTaskInstanceVO> queryTaskAllocationHisInfo(MetadataTaskInstanceDTO metadataTaskInstanceDTO);

    int updateTaskState();
}