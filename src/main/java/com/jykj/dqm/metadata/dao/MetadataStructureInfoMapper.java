package com.jykj.dqm.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.metadata.entity.MetadataStructureDTO;
import com.jykj.dqm.metadata.entity.MetadataStructureInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 元数据结构信息
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 16:17:20
 */
@Mapper
public interface MetadataStructureInfoMapper extends BaseMapper<MetadataStructureInfo> {

    List<MetadataStructureInfo> getAllStructuctureInfo(MetadataStructureDTO metadataStructureDTO);
}