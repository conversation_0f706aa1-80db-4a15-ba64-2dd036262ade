package com.jykj.dqm.metadata.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.metadata.dao.TaskGroupSubtasksMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.metadata.entity.MetadataDatasourceSubTaskInfoVO;
import com.jykj.dqm.metadata.entity.TaskGroupSubtasks;
import com.jykj.dqm.metadata.entity.TaskGroupSubtasksDTO;
import com.jykj.dqm.metadata.service.MetadataDatasourceService;
import com.jykj.dqm.metadata.service.TaskGroupSubtasksService;
import com.jykj.dqm.utils.MapperUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务组与子任务关联关系
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/26 14:01:15
 */
@Service
public class TaskGroupSubtasksServiceImpl extends ServiceImpl<TaskGroupSubtasksMapper, TaskGroupSubtasks> implements TaskGroupSubtasksService {
    @Autowired
    private TaskGroupSubtasksMapper dqmTaskGroupSubtasksMapper;

    @Autowired
    private MetadataDatasourceService dqmMetadataDatasourceService;

    @Override
    public R saveOrUpdateBatch(TaskGroupSubtasksDTO taskGroupSubtasksDto) {
        this.saveOrUpdateBatch(taskGroupSubtasksDto.getDqmTaskGroupSubtasks());
        return RUtil.success("操作成功！");
    }

    @Override
    public R querySubtask(String taskGroupId) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("TASK_GROUP_ID", taskGroupId);
        List<TaskGroupSubtasks> dqmTaskGroupSubtasks = dqmTaskGroupSubtasksMapper.selectByMap(params);
        List<MetadataDatasourceSubTaskInfoVO> metadataDatasources = new ArrayList<>();
        MetadataDatasource dqmMetadataDatasource;
        MetadataDatasourceSubTaskInfoVO subTaskInfoVO;
        for (TaskGroupSubtasks dqmTaskGroupSubtask : dqmTaskGroupSubtasks) {
            dqmMetadataDatasource = dqmMetadataDatasourceService.getById(dqmTaskGroupSubtask.getSubTaskId());
            if (dqmMetadataDatasource != null) {
                subTaskInfoVO = MapperUtils.INSTANCE.map(MetadataDatasourceSubTaskInfoVO.class, dqmMetadataDatasource);
                subTaskInfoVO.setTaskSubTaskId(dqmTaskGroupSubtask.getId());
                metadataDatasources.add(subTaskInfoVO);
            }
        }
        return RUtil.success(metadataDatasources);
    }

    @Override
    public R deleteSubTask(List<String> ids) {
        this.removeByIds(ids);
        return RUtil.success("删除成功！");
    }
}
