package com.jykj.dqm.metadata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.metadata.entity.TaskGroupSubtasks;
import com.jykj.dqm.metadata.entity.TaskGroupSubtasksDTO;

import java.util.List;

/**
 * 任务组与子任务关联关系
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/26 14:01:15
 */
public interface TaskGroupSubtasksService extends IService<TaskGroupSubtasks> {
    R saveOrUpdateBatch(TaskGroupSubtasksDTO taskGroupSubtasksDto);

    R querySubtask(String taskGroupId);

    R deleteSubTask(List<String> ids);
}
