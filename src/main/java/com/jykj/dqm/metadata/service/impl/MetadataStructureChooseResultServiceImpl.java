package com.jykj.dqm.metadata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.metadata.dao.MetadataStructureChooseResultMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.metadata.entity.MetadataStructureChooseResult;
import com.jykj.dqm.metadata.entity.MetadataStructureChooseResultDTO;
import com.jykj.dqm.metadata.manager.ExecCollectionTask;
import com.jykj.dqm.metadata.service.MetadataStructureChooseResultService;
import com.jykj.dqm.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 库表结构选择结果
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/14 13:43:20
 */
@Service
public class MetadataStructureChooseResultServiceImpl extends ServiceImpl<MetadataStructureChooseResultMapper, MetadataStructureChooseResult> implements MetadataStructureChooseResultService {

    @Autowired
    private MetadataStructureChooseResultMapper metadataStructureChooseResultMapper;

    @Autowired
    private ExecCollectionTask execCollectionTask;

    @Override
    public R getDBStructure(String dataSourceId) {
        QueryWrapper<MetadataStructureChooseResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("DATA_SOURCE_ID", dataSourceId);
        List<MetadataStructureChooseResult> metadataStructureChooseResults = metadataStructureChooseResultMapper.selectList(queryWrapper);
        return RUtil.success(metadataStructureChooseResults);
    }

    @Override
    public R saveDbStructure(MetadataStructureChooseResultDTO metadataStructureChooseResultDTO) {
        QueryWrapper<MetadataStructureChooseResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("DATA_SOURCE_ID", metadataStructureChooseResultDTO.getDataSourceId());
        metadataStructureChooseResultMapper.delete(queryWrapper);
        List<String> tableNames = metadataStructureChooseResultDTO.getTableNames();
        List<MetadataStructureChooseResult> list = new ArrayList<>();
        tableNames.stream().forEach(table -> {
            MetadataStructureChooseResult metadataStructureChooseResult = MetadataStructureChooseResult.builder()
                    .dataSourceId(metadataStructureChooseResultDTO.getDataSourceId())
                    .sysCode(metadataStructureChooseResultDTO.getSysCode())
                    .sysName(metadataStructureChooseResultDTO.getSysName())
                    .databaseName(metadataStructureChooseResultDTO.getDatabaseName())
                    .chooseResult(metadataStructureChooseResultDTO.getChooseResult())
                    .schemaName(metadataStructureChooseResultDTO.getSchemaName())
                    .tableName(table).build();
            list.add(metadataStructureChooseResult);
        });
        this.saveBatch(list);
        return RUtil.success();
    }

    @Override
    public R synchronizeDatabaseStructure(String dataSourceId) {
        MetadataDatasource dqmMetadataDatasource = RedisUtil.getMetadataDatasourceById(dataSourceId);
        return execCollectionTask.execCollectionTask(dqmMetadataDatasource);
    }
}
