package com.jykj.dqm.metadata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.metadata.entity.MetadataStructureDTO;
import com.jykj.dqm.metadata.entity.MetadataStructureInfo;

/**
 * 元数据结构信息
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 16:16:43
 */
public interface MetadataStructureInfoService extends IService<MetadataStructureInfo> {
    /**
     * 获取左侧树形结构
     *
     * @param metadataStructureDTO
     * @return Result
     * <AUTHOR>
     */
    R getLeftTree(MetadataStructureDTO metadataStructureDTO);

    /**
     * 获取右侧表或者视图结构
     *
     * @param metadataStructureDTO
     * @return Result
     * <AUTHOR>
     */
    R getRightStructures(MetadataStructureDTO metadataStructureDTO);
}


