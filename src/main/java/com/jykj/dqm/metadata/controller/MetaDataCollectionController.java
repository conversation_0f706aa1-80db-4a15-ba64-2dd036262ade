package com.jykj.dqm.metadata.controller;

import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.metadata.entity.CollectionTaskInfoDTO;
import com.jykj.dqm.metadata.entity.MGTaskDto;
import com.jykj.dqm.metadata.entity.MetadataTaskInstanceDTO;
import com.jykj.dqm.metadata.service.MetaDataCollectionService;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.quartz.scheduler.QuartzManager;
import com.jykj.dqm.utils.IdUtils;
import com.jykj.dqm.utils.MapperUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 元数据采集任务
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/24 14:21:26
 */
@Api(tags = {"元数据采集任务"})
@RestController
@RequestMapping("/mateData")
public class MetaDataCollectionController {
    public static final String MODULE_NAME = "元数据采集任务";
    @Resource
    private QuartzManager quartzManager;

    @Autowired
    private MetaDataCollectionService metaDataCollectionService;

    /**
     * 新增采集任务/更新采集任务
     *
     * @param mgTaskDto MGTaskDto
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "新增采集任务/更新采集任务", notes = "元数据采集任务")
    @LogRemark(operate = "新增采集任务/更新采集任务", module = MODULE_NAME)
    @PostMapping(value = "/addTaskAllocationInfo")
    public R addTaskAllocationInfo(@RequestBody MGTaskDto mgTaskDto) {
        try {
            if (mgTaskDto.getJobId() == null || "".equals(mgTaskDto.getJobId())) {
                String snowflakeId = IdUtils.getID();
                mgTaskDto.setJobId(snowflakeId);
            }
            ScheduleJobInfo jobInfo = MapperUtils.INSTANCE.map(ScheduleJobInfo.class, mgTaskDto);
            jobInfo.setBizDataId(mgTaskDto.getJobId());
            jobInfo.setTaskType("MG");
            quartzManager.processScheduleJob(jobInfo);
            return RUtil.success("成功！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取元数据采集任务
     *
     * @param collectionTaskInfoDto CollectionTaskInfoDto
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "获取元数据采集任务", notes = "元数据采集任务")
    @PostMapping("/getScheduleJobInfo")
    public R getScheduleJobInfo(@RequestBody CollectionTaskInfoDTO collectionTaskInfoDto) {
        return metaDataCollectionService.getScheduleJobInfo(collectionTaskInfoDto);
    }

    /**
     * 立即执行采集任务
     *
     * @param params 任务组ID
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "立即执行采集任务", notes = "元数据采集任务")
    @LogRemark(operate = "立即执行采集任务", module = MODULE_NAME)
    @PostMapping("/exeScheduleJob")
    // 设置动态参数描述注解
    @DynamicParameters(name = "params",
            properties = {
                    @DynamicParameter(name = "taskGroupId", value = "任务组编号", example = "1", required = true, dataTypeClass = Integer.class)
            })
    public R exeScheduleJob(@RequestBody Map<String, String> params) {
        return metaDataCollectionService.exeScheduleJob(params.get("taskGroupId"));
    }

    /**
     * 删除采集任务
     *
     * @param taskGroupId 任务组ID
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "删除采集任务", notes = "元数据采集任务")
    @LogRemark(operate = "删除采集任务", module = MODULE_NAME)
    @DeleteMapping(value = "/deleteTaskAllocationInfo")
    public R deleteTaskAllocationInfo(@RequestParam("taskGroupId") String taskGroupId) {
        return metaDataCollectionService.deleteTaskAllocationInfo(taskGroupId);
    }

    /**
     * 获取任务执行结果(按照数据源检索，执行方式，执行状态)
     */
    @ApiOperation(value = "获取任务执行结果", notes = "元数据采集任务")
    @PostMapping("/queryTaskAllocationHisInfo")
    public R queryTaskAllocationHisInfo(@RequestBody MetadataTaskInstanceDTO metadataTaskInstance) {
        return metaDataCollectionService.queryTaskAllocationHisInfo(metadataTaskInstance);
    }
}
