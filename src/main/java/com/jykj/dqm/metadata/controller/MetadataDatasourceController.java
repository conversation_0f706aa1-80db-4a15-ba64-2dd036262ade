package com.jykj.dqm.metadata.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.metadata.entity.MetadataDatasourceDeleteDTO;
import com.jykj.dqm.metadata.entity.MetadataDatasourceQueryDTO;
import com.jykj.dqm.metadata.service.MetadataDatasourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 数据源管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/22 11:38:16
 */
@Api(tags = {"数据源管理"})
@Slf4j
@RestController
@RequestMapping("/datasource")
public class MetadataDatasourceController {
    public static final String MODULE_NAME = "数据源管理";
    /**
     * 服务对象
     */
    @Resource
    private MetadataDatasourceService metadataDatasourceService;

    /**
     * 查询数据源配置列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询数据源配置列表", notes = "数据源管理")
    public R list(@RequestBody MetadataDatasourceQueryDTO dqmMetadataDatasource) {
        return metadataDatasourceService.selectMetadataDatasourceList(dqmMetadataDatasource);
    }


    /**
     * 新增数据源配置
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增数据源配置", notes = "数据源管理")
    @LogRemark(operate = "新增数据源配置", module = MODULE_NAME)
    public R add(@RequestBody MetadataDatasource dqmMetadataDatasource) {
        return metadataDatasourceService.addDatasource(dqmMetadataDatasource);
    }

    /**
     * 更新数据源配置
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新数据源配置", notes = "数据源管理")
    @LogRemark(operate = "更新数据源配置", module = MODULE_NAME)
    public R update(@RequestBody MetadataDatasource dqmMetadataDatasource) {
        return metadataDatasourceService.updateDatasource(dqmMetadataDatasource);
    }

    /**
     * 删除数据源配置
     */
    @DeleteMapping("/delete")
    @ApiOperation(value = "删除数据源配置", notes = "数据源管理")
    @LogRemark(operate = "删除数据源配置", module = MODULE_NAME)
    public R delete(@RequestBody MetadataDatasourceDeleteDTO dqmMetadataDatasource) {
        return metadataDatasourceService.deleteDatasource(dqmMetadataDatasource);
    }

    /**
     * 测试数据源是否连通
     *
     * @param dqmMetadataDatasource DqmMetadataDatasource
     * @return Result
     * <AUTHOR>
     */
    @PostMapping(value = "/testDb")
    @ApiOperation(value = "测试数据源是否连通", notes = "数据源管理")
    @LogRemark(operate = "测试数据源是否连通", module = MODULE_NAME)
    public R testDb(@RequestBody MetadataDatasource dqmMetadataDatasource) {
        return metadataDatasourceService.testDb(dqmMetadataDatasource);
    }

    /**
     * 上传驱动文件，返回一个文件名给前端
     *
     * @return Result
     * <AUTHOR>
     */
    @PostMapping(value = "/uploadDriverFile")
    @ApiOperation(value = "上传驱动文件", notes = "数据源管理")
    @LogRemark(operate = "上传驱动文件", module = MODULE_NAME)
    public R uploadDriverFile(@RequestParam("file") MultipartFile file) {
        return metadataDatasourceService.uploadDriverFile(file);
    }
}
