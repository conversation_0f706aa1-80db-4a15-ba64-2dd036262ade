package com.jykj.dqm.metadata.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 采集任务执行情况
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 13:53:47
 */
@ApiModel(value = "采集任务执行情况")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MetadataTaskInstanceVO {
    /**
     * 采集ID
     */
    @ApiModelProperty(value = "采集ID")
    private Integer taskInstanceId;

    /**
     * 任务组编号
     */
    @ApiModelProperty(value = "任务组编号")
    private String taskGroupId;

    /**
     * 任务组名称
     */
    @ApiModelProperty(value = "任务组名称")
    private String taskGroupName;

    /**
     * 数据源ID
     */
    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;

    /**
     * 数据源ID
     */
    @ApiModelProperty(value = "数据源名称")
    private String dataSourceName;

    /**
     * 00:执行中,11:成功,99:失败
     */
    @ApiModelProperty(value = "00:执行中,11:成功,99:失败")
    private String taskState;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")   //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private Date taskStartDt;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")   //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private Date taskEndDt;

    /**
     * 执行结果
     */
    @ApiModelProperty(value = "执行结果")
    private String taskResult;
}