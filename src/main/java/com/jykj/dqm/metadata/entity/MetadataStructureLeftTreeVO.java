package com.jykj.dqm.metadata.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 元数据结构详情
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 16:17:20
 */
@ApiModel(value = "元数据结构(表/视图)详情左侧树形")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MetadataStructureLeftTreeVO {
    /**
     * 数据源ID
     */
    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;

    /**
     * 数据库信息
     */
    @ApiModelProperty(value = "数据库信息")
    private List<DataBaseInfo> databaseName;
}