package com.jykj.dqm.metadata.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据源配置表
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/22 11:35:34
 */
@ApiModel(value = "数据源配置查询条件")
@Data
public class MetadataDatasourceQueryDTO {
    /**
     * 数据源名称
     */
    @TableField(value = "DATA_SOURCE_NAME")
    @ApiModelProperty(value = "数据源名称")
    private String dataSourceName;

    /**
     * 数据库类型
     */
    @TableField(value = "DATABASE_TYPE")
    @ApiModelProperty(value = "数据库类型")
    private String databaseType;

    /**
     * 是否过滤跨库查询 0：全部 1：过滤跨数据源 2：跨库数据源，默认所有
     */
    @ApiModelProperty(value = "是否过滤跨库查询 0：全部 1：过滤跨数据源 2：跨库数据源，默认所有")
    private String whetherFilterCrossDb = "0";

    /**
     * 关联任务状态
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "关联任务状态:0:全部；1：关联；2：未关联")
    private String associatedTaskStatus = "0";

    /**
     * 分页参数 当前页
     */
    @ApiModelProperty(value = "分页参数 当前页")
    private Integer pageNum = 1;

    /**
     * 分页参数 每页数量
     */
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize = 10;
}