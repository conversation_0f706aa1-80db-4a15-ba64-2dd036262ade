package com.jykj.dqm.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 采集任务执行情况
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 13:53:47
 */
@ApiModel(value = "采集任务执行情况")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_METADATA_TASK_INSTANCE")
public class MetadataTaskInstance {
    /**
     * 采集ID
     */
    @TableId(value = "TASK_INSTANCE_ID", type = IdType.AUTO)
    @ApiModelProperty(value = "采集ID")
    private Integer taskInstanceId;

    /**
     * 任务组编号
     */
    @TableField(value = "TASK_GROUP_ID")
    @ApiModelProperty(value = "任务组编号")
    private String taskGroupId;

    /**
     * 数据源ID
     */
    @TableField(value = "DATA_SOURCE_ID")
    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;

    /**
     * 00:执行中,11:成功,99:失败
     */
    @TableField(value = "TASK_STATE")
    @ApiModelProperty(value = "00:执行中,11:成功,99:失败")
    private String taskState;

    /**
     * 开始时间
     */
    @TableField(value = "TASK_START_DT")
    @ApiModelProperty(value = "开始时间")
    private Date taskStartDt;

    /**
     * 结束时间
     */
    @TableField(value = "TASK_END_DT")
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")   //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private Date taskEndDt;

    /**
     * 执行结果
     */
    @TableField(value = "TASK_RESULT")
    @ApiModelProperty(value = "执行结果")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")   //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private String taskResult;
}