package com.jykj.dqm.metadata.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 采集任务配置查询
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/24 14:27:56
 */
@ApiModel(value = "采集任务配置查询")
@Data
public class CollectionTaskInfoDTO {
    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称")
    private String dataSourceName;

    /**
     * 数据源编码
     */
    @ApiModelProperty(value = "数据源编码")
    private String dataSourceId;

    /**
     * 执行方式
     */
    @ApiModelProperty(value = "执行方式")
    private String jobExeType;

    /**
     * 任务类型：MG:元数据采集   QC:数据质量
     */
    @ApiModelProperty(value = "任务类型：MG:元数据采集   QC:数据质量")
    private String taskType = "MG";

    /**
     * 分页参数 当前页
     */
    @ApiModelProperty(value = "分页参数 当前页")
    private Integer pageNum = 1;

    /**
     * 分页参数 每页数量
     */
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize = 10;
}
