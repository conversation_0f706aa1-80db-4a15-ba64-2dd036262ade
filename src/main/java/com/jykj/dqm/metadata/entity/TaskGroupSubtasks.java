package com.jykj.dqm.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 任务组和子任务关系表(数据源)
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/26 14:01:15
 */

/**
 *
 */
@ApiModel(value = "任务组和子任务关系表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_TASK_GROUP_SUBTASKS")
public class TaskGroupSubtasks {
    /**
     * 任务组和子任务ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    @ApiModelProperty(value = "任务组和子任务ID")
    private Integer id;

    /**
     * 任务组ID
     */
    @TableField(value = "TASK_GROUP_ID")
    @ApiModelProperty(value = "任务组ID")
    private String taskGroupId;

    /**
     * 子任务ID（规则ID，数据源ID）
     */
    @TableField(value = "SUB_TASK_ID")
    @ApiModelProperty(value = "子任务ID（规则ID，数据源ID）")
    private String subTaskId;

    /**
     * 任务类型
     */
    @TableField(value = "TASK_TYPE")
    @NotBlank
    @ApiModelProperty(value = "任务类型 MG:元数据采集 QC：数据质量检查")
    private String taskType;
}