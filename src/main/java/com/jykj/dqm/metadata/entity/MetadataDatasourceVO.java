package com.jykj.dqm.metadata.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据源配置表
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/22 11:35:34
 */
@ApiModel(value = "数据源配置表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MetadataDatasourceVO extends MetadataDatasource {
    /**
     * 操作人
     */
    @ApiModelProperty(value = "配置数")
    private long configNum;
}