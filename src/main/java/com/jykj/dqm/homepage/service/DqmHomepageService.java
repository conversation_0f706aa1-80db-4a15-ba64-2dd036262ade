package com.jykj.dqm.homepage.service;

import java.util.List;
import java.util.Map;

/**
 * 数据质量新首页
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2024-04-23 17:24
 */
public interface DqmHomepageService {
    /**
     * 数据分析
     *
     * @param startDate yyyy-MM-dd
     * @param endDate   yyyy-MM-dd
     * @return Map<String, Object>
     * <AUTHOR>
     */
    Map<String, Object> getDataAnalysisAndCheckRuleNum(String startDate, String endDate);

    /**
     * 规则问题级别及占比情况
     *
     * @return List<Map<String, Object>>
     * <AUTHOR>
     */
    List<Map<String, Object>> getRuleQstnLevel(String startDate, String endDate);

    /**
     * 各系统检测数与问题数情况分析
     *
     * @return List<Map<String, Object>>
     * <AUTHOR>
     */
    List<Map<String, Object>> getSysQstnNumAnalysis(String startDate, String endDate, String sysCode);
}
