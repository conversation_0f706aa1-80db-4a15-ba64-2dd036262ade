package com.jykj.dqm.homepage.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jykj.dqm.homepage.service.DqmHomepageService;
import com.jykj.dqm.metadata.dao.MetadataDatasourceMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.quality.dao.DataqualityCheckRuleMapper;
import com.jykj.dqm.quality.dao.DataqulityQstnManagerMapper;
import com.jykj.dqm.quality.entity.DataqualityCheckRule;
import com.jykj.dqm.quality.entity.DataqulityQstnManager;
import com.jykj.dqm.system.dao.DictMappingsMapper;
import com.jykj.dqm.system.entity.DictMappings;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据质量新首页
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2024-04-23 17:23
 */
@Service
@RequiredArgsConstructor
public class DqmHomepageServiceImpl implements DqmHomepageService {
    private final DataqulityQstnManagerMapper dataqulityQstnManagerMapper;
    private final MetadataDatasourceMapper dqmMetadataDatasourceMapper;
    private final DictMappingsMapper dictMappingsMapper;
    private final DataqualityCheckRuleMapper dataqualityCheckRuleMapper;

    /**
     * qstnNum-问题数据量，checkRuleNum-质量规则总数，checkRuleGroupNum-质量规则分组统计，datasourceNum-数据源总量，dictNum-字典数，sysNum-监控系统数
     *
     * @param startDate yyyy-MM-dd
     * @param endDate   yyyy-MM-dd
     * @return
     */
    @Override
    public Map<String, Object> getDataAnalysisAndCheckRuleNum(String startDate, String endDate) {
        Map<String, Object> result = new HashMap(6);
        QueryWrapper<DataqulityQstnManager> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(startDate)) {
            queryWrapper.lambda()
                    .ge(DataqulityQstnManager::getQstnCheckTime, DateUtil.parseDate(startDate));
        }
        if (StrUtil.isNotBlank(endDate)) {
            queryWrapper.lambda()
                    .lt(DataqulityQstnManager::getQstnCheckTime, DateUtil.offsetDay(DateUtil.parseDate(endDate), 1));
        }
        //问题数据
        queryWrapper.select("sum(DATA_QSTN_NUM) as TOTAL");
        DataqulityQstnManager dataqulityQstnManager = dataqulityQstnManagerMapper.selectOne(queryWrapper);
        Integer qstnNum = dataqulityQstnManager == null ? 0 : dataqulityQstnManager.getTotal();
        result.put("qstnNum", qstnNum);

        //质量规则总数
        QueryWrapper<DataqualityCheckRule> checkRuleQueryWrapper = new QueryWrapper<>();
        //checkRuleQueryWrapper.lambda().eq(DataqualityCheckRule::getCheckRuleStatus, "0"); //启用状态
        if (StrUtil.isNotBlank(startDate)) {
            checkRuleQueryWrapper.lambda()
                    .ge(DataqualityCheckRule::getUpdateDate, DateUtil.parseDate(startDate));
        }
        if (StrUtil.isNotBlank(endDate)) {
            checkRuleQueryWrapper.lambda()
                    .lt(DataqualityCheckRule::getUpdateDate, DateUtil.offsetDay(DateUtil.parseDate(endDate), 1));
        }
        List<DataqualityCheckRule> dataqualityCheckRules = dataqualityCheckRuleMapper.selectList(checkRuleQueryWrapper);
        result.put("checkRuleNum", dataqualityCheckRules.size());
        result.put("sysNum", dataqualityCheckRules.stream().map(DataqualityCheckRule::getSysCode).distinct().count());
        //dataqualityCheckRules 按照checkRuleFatherType分组并统计每组的数量
        Map<String, Long> dataqualityCheckRuleGroupNum = dataqualityCheckRules.stream()
                .collect(Collectors.groupingBy(DataqualityCheckRule::getCheckRuleFatherType, Collectors.counting()));
        result.put("checkRuleGroupNum", dataqualityCheckRuleGroupNum);

        QueryWrapper<MetadataDatasource> datasourceQueryWrapper = new QueryWrapper<>();
        //数据源总量
        Long datasourceNum = dqmMetadataDatasourceMapper.selectCount(datasourceQueryWrapper);
        result.put("datasourceNum", datasourceNum);
        //字典数
        QueryWrapper<DictMappings> dictMappingsQueryWrapper = new QueryWrapper<>();
        dictMappingsQueryWrapper.lambda().eq(DictMappings::getDeletedFlag, "0");
        Long dictNum = dictMappingsMapper.selectCount(dictMappingsQueryWrapper);
        result.put("dictNum", dictNum);

        return result;
    }

    @Override
    public List<Map<String, Object>> getRuleQstnLevel(String startDate, String endDate) {
        QueryWrapper<DataqualityCheckRule> checkRuleQueryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(startDate)) {
            checkRuleQueryWrapper.lambda()
                    .ge(DataqualityCheckRule::getUpdateDate, DateUtil.parseDate(startDate));
        }
        if (StrUtil.isNotBlank(endDate)) {
            checkRuleQueryWrapper.lambda()
                    .lt(DataqualityCheckRule::getUpdateDate, DateUtil.offsetDay(DateUtil.parseDate(endDate), 1));
        }
        checkRuleQueryWrapper.groupBy("QST_TYPE").select("QST_TYPE as qstType,COUNT(*) as COUNT");
        List<Map<String, Object>> result = dataqualityCheckRuleMapper.selectMaps(checkRuleQueryWrapper);
        return result;
    }

    @Override
    public List<Map<String, Object>> getSysQstnNumAnalysis(String startDate, String endDate, String sysCode) {
        QueryWrapper<DataqulityQstnManager> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(startDate)) {
            queryWrapper.lambda()
                    .ge(DataqulityQstnManager::getQstnCheckTime, DateUtil.parseDate(startDate));
        }
        if (StrUtil.isNotBlank(endDate)) {
            queryWrapper.lambda()
                    .lt(DataqulityQstnManager::getQstnCheckTime, DateUtil.offsetDay(DateUtil.parseDate(endDate), 1));
        }
        queryWrapper.lambda().eq(StrUtil.isNotBlank(sysCode), DataqulityQstnManager::getQstnPrdusSysCd, sysCode);
        //问题数据
        queryWrapper.select("sum(DATA_QSTN_NUM) as dataQstnNum,QSTN_PRDUS_SYS_CD as qstnPrdusSysCd,QSTN_PRDUS_SYS_NM as qstnPrdusSysNm,sum(DATA_TOTAL_NUM) as dataTotalNum");
        queryWrapper.groupBy("QSTN_PRDUS_SYS_CD,QSTN_PRDUS_SYS_NM");
        List<Map<String, Object>> result = dataqulityQstnManagerMapper.selectMaps(queryWrapper);
        return result;
    }

}
