package com.jykj.dqm.homepage.service;

import com.jykj.dqm.quality.entity.DataqulityQstnManagerVO;

import java.util.List;
import java.util.Map;

/**
 * 首页
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/14 10:55:33
 */
public interface IHomepageService {
    /**
     * 数据分析
     *
     * @param selectDate yyyy-MM-dd
     * @return Map<String, Object>
     * <AUTHOR>
     */
    Map<String, Object> getDataAnalysis(String selectDate);

    /**
     * 问题数据趋势
     *
     * @param startDate yyyy-MM-dd HH:mm:ss 开始时间
     * @param endDate   yyyy-MM-dd HH:mm:ss 结束时间
     * @return Map<String, Object>
     * <AUTHOR>
     */
    Map<String, Object> getProblemDataTrends(String startDate, String endDate);

    /**
     * 系统规则
     *
     * @return Map<String, Object>
     * <AUTHOR>
     */
    Map<String, Object> getSysCheckRule();

    /**
     * 问题占比情况
     *
     * @return Map<String, Object>
     * <AUTHOR>
     */
    Map<String, Object> getTheQstnProportion();

    /**
     * 问题排序
     *
     * @param limitNum 显示数量
     * @return List<DataqulityQstnManagerVO>
     * <AUTHOR>
     */
    List<DataqulityQstnManagerVO> getTheQstnSort(Integer limitNum);
}
