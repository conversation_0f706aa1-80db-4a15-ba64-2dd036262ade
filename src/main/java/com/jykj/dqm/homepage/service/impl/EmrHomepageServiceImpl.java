package com.jykj.dqm.homepage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialTaskAllocation;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialUserListAndTasksVo;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialTaskAllocationService;
import com.jykj.dqm.emr.service.RulePermissionConfigurationService;
import com.jykj.dqm.homepage.service.EmrHomepageService;
import com.jykj.dqm.utils.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 电子病历评级管理首页
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/14 10:56:47
 */
@Service
@RequiredArgsConstructor
public class EmrHomepageServiceImpl implements EmrHomepageService {
    private final EmpiricalMaterialTaskAllocationService taskAllocationService;
    private final RulePermissionConfigurationService rulePermissionConfigurationService;

    @Override
    public Map<String, Object> getMyTasksAndProgress(String userAccount) {
        //实证材料文档
        EmpiricalMaterialUserListAndTasksVo userListAndTasks = getUserListAndTasks(userAccount);
        //等级评审文档
        Map<String, Object> map = rulePermissionConfigurationService.getMyTasksAndProgress(null, null, userAccount, null, null);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("empiricalMaterial", userListAndTasks);
        List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("ALL");
        long finishedNum = 0;
        long allocatedNum = 0;
        for (Map<String, Object> objectMap : list) {
            finishedNum += Long.parseLong(objectMap.get("finishedNum") + "");
            allocatedNum += Long.parseLong(objectMap.get("allocatedNum") + "");
        }
        Map<String, Object> emrAll = new HashMap<>();
        emrAll.put("finishedNum", finishedNum);
        emrAll.put("allocatedNum", allocatedNum);
        emrAll.put("ratio", StringUtil.getRatio(finishedNum, allocatedNum));
        resultMap.put("emr", emrAll);
        return resultMap;
    }


    public EmpiricalMaterialUserListAndTasksVo getUserListAndTasks(String userAccount) {
        EmpiricalMaterialUserListAndTasksVo empiricalMaterialUserListAndTasksVo = new EmpiricalMaterialUserListAndTasksVo();
        List<EmpiricalMaterialTaskAllocation> empiricalMaterialTaskAllocations = taskAllocationService.list(
                new LambdaQueryWrapper<EmpiricalMaterialTaskAllocation>()
                        .eq(EmpiricalMaterialTaskAllocation::getPersonInCharge, userAccount)
                        .isNotNull(EmpiricalMaterialTaskAllocation::getProjectId)
        );
        empiricalMaterialUserListAndTasksVo.setUserAccount(userAccount);
        //已分配数量
        long allocatedNum = empiricalMaterialTaskAllocations.size();
        //已完成数量
        long finishedNum = empiricalMaterialTaskAllocations.stream().filter(entry -> "2".equals(entry.getTaskStatus())).count();
        empiricalMaterialUserListAndTasksVo.setAllocatedNum(allocatedNum);
        empiricalMaterialUserListAndTasksVo.setFinishedNum(finishedNum);
        return empiricalMaterialUserListAndTasksVo;
    }
}
