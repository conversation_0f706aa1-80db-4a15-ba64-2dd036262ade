package com.jykj.dqm.system.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.system.entity.DictMappingsContentDTO;
import com.jykj.dqm.system.entity.DictMappingsDTO;
import com.jykj.dqm.system.service.DictManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 字典管理
 * 1、查询
 * 2、同步
 * 3、子页面查询具体数据
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/17 10:53:31
 */
@Api(tags = {"字典管理"})
@RestController
@RequestMapping("/dictManager")
public class DictManagementController {
    @Autowired
    private DictManagementService dictManagementService;

    /**
     * 查询字典管理列表接口
     *
     * @param dictMappingDTO DictMappingsDTO
     * @return 字典管理列表
     * <AUTHOR>
     */
    @ApiOperation(value = "查询字典管理列表接口", notes = "字典管理")
    @PostMapping("/getDictManagementList")
    public R getDictManagementList(@RequestBody DictMappingsDTO dictMappingDTO) {
        return dictManagementService.getDictManagementList(dictMappingDTO);
    }

    /**
     * 查询字典数据接口
     *
     * @param dictMappingsContentDTO DictMappingsContentDTO
     * @return 字典数据
     * <AUTHOR>
     */
    @ApiOperation(value = "查询字典数据接口", notes = "字典管理")
    @PostMapping("/getDictDataList")
    public R getDictDataList(@RequestBody DictMappingsContentDTO dictMappingsContentDTO) {
        return dictManagementService.getDictDataList(dictMappingsContentDTO);
    }

    /**
     * 查询所有的系统信息
     *
     * @return 所有的系统信息
     * <AUTHOR>
     */
    @ApiOperation(value = "查询所有的系统信息", notes = "字典管理")
    @GetMapping("/getSysInfoList")
    public R getSysInfoList() {
        return dictManagementService.getSysInfoList();
    }

    /**
     * 同步主数据系统数据
     * <p>
     * 如果是本库，直接同步
     * 如果是其他库，根据dataSoureceId建立连接
     * 同步DQM_DICT_MAPPINGS
     * 同步MDM_DICT_MAPPINGS_CONTENT
     *
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "同步主数据系统数据", notes = "字典管理")
    @GetMapping("/syncDictData")
    public R syncDictData() {
        return dictManagementService.syncDictData();
    }
}
