package com.jykj.dqm.system.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.system.entity.SysCodetabContent;
import com.jykj.dqm.system.entity.SysCodetabContentDTO;
import com.jykj.dqm.system.service.SysCodetabContentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统码值内容
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/31 16:50:29
 */
@Api(tags = {"系统码值内容"})
@RestController
@RequestMapping("/sysCodetabContent")
public class SysCodetabContentController {
    public static final String MODULE_NAME = "系统码值内容";
    @Autowired
    private SysCodetabContentService sysCodetabContentService;

    @ApiOperation(value = "新增系统码值内容", notes = "系统码值内容")
    @LogRemark(operate = "新增系统码值内容", module = MODULE_NAME)
    @PostMapping("/add")
    public R add(@RequestBody SysCodetabContent sysCodetabContent) {
        return sysCodetabContentService.addSysCodetabContent(sysCodetabContent);
    }

    @ApiOperation(value = "更新系统码值内容", notes = "系统码值内容")
    @LogRemark(operate = "更新系统码值内容", module = MODULE_NAME)
    @PostMapping("/modify")
    public R modify(@RequestBody SysCodetabContent sysCodetabContent) {
        return sysCodetabContentService.modifySysCodetabContent(sysCodetabContent);
    }

    @ApiOperation(value = "删除系统码值内容", notes = "系统码值内容")
    @LogRemark(operate = "删除系统码值内容", module = MODULE_NAME)
    @DeleteMapping("/delete")
    public R delete(@RequestParam("ids") List<Integer> ids) {
        return sysCodetabContentService.deleteSysCodetabContent(ids);
    }

    @ApiOperation(value = "查询系统码值内容", notes = "系统码值内容")
    @PostMapping("/query")
    public R query(@RequestBody SysCodetabContentDTO sysCodetabContentDTO) {
        return sysCodetabContentService.querySysCodetabContent(sysCodetabContentDTO);
    }

}
