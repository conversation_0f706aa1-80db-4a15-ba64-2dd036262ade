package com.jykj.dqm.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.LogLogin;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.system.dao.LogLoginMapper;
import com.jykj.dqm.system.entity.LogLoginDTO;
import com.jykj.dqm.system.service.LogLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title : LogLoginService
 * @Package : com.jykj.dqm.config.service.impl
 * @Description :登录日志
 * <AUTHOR> 黄杰
 * @DateTime : 2021/8/18 19:03
 */
@Service
public class LogLoginServiceImpl implements LogLoginService {
    @Autowired
    private LogLoginMapper logLoginMapper;

    @Override
    public R insertSelective(LogLogin record) {
        logLoginMapper.insertSelective(record);
        return RUtil.success();
    }

    @Override
    public R queryLog(LogLoginDTO logLoginDTO) {
        if (StrUtil.isNotBlank(logLoginDTO.getEndTime())) {
            logLoginDTO.setEndTime(logLoginDTO.getEndTime() + " 23:59:59");
        }
        PageHelper.startPage(logLoginDTO.getPageNum(), logLoginDTO.getPageSize());
        List<LogLogin> list = logLoginMapper.queryLog(logLoginDTO);
        PageInfo<LogLogin> pageInfo = new PageInfo<>(list);
        return RUtil.success(pageInfo);
    }
}
