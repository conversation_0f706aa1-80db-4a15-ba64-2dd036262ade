package com.jykj.dqm.system.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Sets;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.common.UploadImageFile;
import com.jykj.dqm.config.schedule.SchedulerObjectInterface;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.system.dao.SysConfigMapper;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.system.service.SystemSettingService;
import com.jykj.dqm.utils.FileUtil;
import com.jykj.dqm.utils.ListUtils;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.StringUtil;
import com.jykj.dqm.utils.SystemUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 系统设置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/16 13:48
 */
@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class SystemSettingImpl implements SystemSettingService {
    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private HttpServletRequest request;

    private final SchedulerObjectInterface schedulerObjectInterface;

    @Autowired
    private UploadImageFile uploadImageFile;

    /**
     * 图片文件大小的上限值(1MB)
     */
    public static final int LOGO_MAX_SIZE = 1;

    @Override
    public R querySysConfig() {
        List<SysConfig> list = sysConfigMapper.querySysConfig();
        Map<String, List<SysConfig>> map = new HashMap<>();
        //按照模块划分
        if (list != null) {
            map = list.stream().collect(Collectors.groupingBy(SysConfig::getModuleId));
        }
        return RUtil.success(map);
    }

    /**
     * 添加系统设置
     *
     * @param sysConfigList List<SysConfig>
     * @return Result
     * <AUTHOR>
     */
    @Override
    public R addSysConfig(List<SysConfig> sysConfigList) {
        //校验参数
        if (ListUtils.checkNull(sysConfigList)) {
            return RUtil.error("参数为空！");
        }
        Date date = new Date();
        sysConfigList.stream().forEach(sysConfig -> {
            sysConfig.setCreateTime(date);
            sysConfig.setCreateBy(StpUtil.getLoginIdAsString());
            sysConfigMapper.insertSelective(sysConfig);
        });
        RedisUtil.deleteByNamespacePrefix("EMRM:Sys:");
        return RUtil.success();
    }

    /**
     * 更新系统设置
     *
     * @param sysConfigList List<SysConfig>
     * @return Result
     * <AUTHOR>
     */
    @Override
    public R updateSysConfigs(List<SysConfig> sysConfigList) {
        //校验参数
        if (ListUtils.checkNull(sysConfigList)) {
            return RUtil.error("配置参数为空！");
        }
        //数据库目前配置信息
        List<SysConfig> sysConfigs = sysConfigMapper.querySysConfigAll();
        //页面全部的配置信息
        Set<SysConfig> setNew = Sets.newHashSet(sysConfigList);
        Set<SysConfig> setOld = Sets.newHashSet(sysConfigs);
        Set<SysConfig> difference = Sets.difference(setNew, setOld);
        if (CollUtil.isEmpty(difference)) {
            return RUtil.error("配置参数没有变化！！！");
        }
        Date date = new Date();
        String syncSwicthNow = "";
        String time = "";
        for (SysConfig sysConfig : difference) {
            sysConfig.setUpdateTime(date);
            sysConfig.setUpdateBy(StpUtil.getLoginIdAsString());
            sysConfigMapper.updateByPrimaryKeySelective(sysConfig);
            //更新同步主数据开关
            if (Constant.MDM_AUTO_SYNC_SWICTH.equalsIgnoreCase(sysConfig.getConfigCode())) {
                syncSwicthNow = sysConfig.getConfigValue();
            }
            //更新同步主数据执行时间
            if (Constant.MDM_AUTO_SYNC_TIME.equalsIgnoreCase(sysConfig.getConfigCode()) && StringUtil.isInteger(sysConfig.getConfigValue())) {
                time = sysConfig.getConfigValue();
            }
        }
        SysConfig config = RedisUtil.getSysConfigByName(Constant.MDM_AUTO_SYNC_SWICTH);
        String syncSwicth = config == null ? "" : config.getConfigValue();
        if ("Y".equalsIgnoreCase(syncSwicth) && StrUtil.isNotBlank(time)) {
            long timer = Long.parseLong(time) * 60 * 60 * 1000;
            schedulerObjectInterface.update(timer + "");
        } else if ("Y".equalsIgnoreCase(syncSwicthNow)) {
            schedulerObjectInterface.update("");
        } else if ("N".equalsIgnoreCase(syncSwicth)) {
            schedulerObjectInterface.stop();
        }
        RedisUtil.deleteByNamespacePrefix("EMRM:Sys:");
        return RUtil.success();
    }

    @Override
    public R queryAllSysConfig() {
        List<SysConfig> list = sysConfigMapper.querySysConfigAll();
        HashMap<String, Object> collect = list.stream().collect(Collectors.toMap(sysConfig -> sysConfig.getConfigCode(), sysConfig -> sysConfig.getConfigValue() == null ? "" : sysConfig.getConfigValue(), (n1, n2) -> n2, HashMap<String, Object>::new));
        return RUtil.success(collect);
    }

    @Override
    public R setHospitalLogo(MultipartFile file, String title, String logoType) {
        if (StrUtil.isBlank(logoType)) {
            throw new BusinessException("logoType不允许为空");
        }
        // 判断上传的文件是否为空
        if (file == null || file.isEmpty()) {
            throw new BusinessException("上传的文件不允许为空");
        }
        Integer imageSize = LOGO_MAX_SIZE;
        if (uploadImageFile.getFileSize() != null && uploadImageFile.getFileSize() != 0) {
            imageSize = uploadImageFile.getFileSize();
        }
        int sizeByte = imageSize * 1024 * 1024;
        // 判断上传的文件大小是否超出限制值
        if (file.getSize() > sizeByte) { // getSize()：返回文件的大小，以字节为单位
            throw new BusinessException("不允许上传超过" + (sizeByte / 1024) + "KB的头像文件");
        }

        // 判断上传的文件类型是否超出限制
        String contentType = file.getContentType();
        if (CollUtil.isNotEmpty(uploadImageFile.getFileTypes()) && !uploadImageFile.getFileTypes().contains(contentType)) {
            throw new BusinessException("不支持使用该类型的文件作为头像，允许的文件类型：" + uploadImageFile.getFileTypes());
        }
        //获取上传文件的文件名
        String oldName = file.getOriginalFilename();
        String parent = FileUtil.fixPath(SystemUtils.getPath(UploadImageFile.class));
        // 保存头像文件的文件夹
        File dir = new File(parent);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        //拼接成为新文件的路径
        String filePath = parent + "/" + oldName;
        // 创建文件对象，表示保存的头像文件
        File dest = new File(filePath);
        // 执行保存头像文件
        try {
            file.transferTo(dest);
        } catch (IllegalStateException e) {
            log.error("文件状态异常，可能文件已被移动或删除：" + e.getMessage(), e);
            throw new BusinessException("文件状态异常，可能文件已被移动或删除");
        } catch (IOException e) {
            log.error("上传文件时读写错误，请稍后重新尝试：" + e.getMessage(), e);
            throw new BusinessException("上传文件时读写错误，请稍后重新尝试");
        }
        //删除之前的图片
        SysConfig sysConfigByName = RedisUtil.getSysConfigByName(logoType);
        String configValue = sysConfigByName.getConfigValue();
        if (StrUtil.isNotBlank(configValue)) {
            String[] split = configValue.split("/");
            if (split.length == 3 && StrUtil.isNotBlank(split[3])) {
                cn.hutool.core.io.FileUtil.del(parent + "/" + split[3]);
            }
        }
        // 将图片路径写到系统设置中
        sysConfigByName.setConfigValue("/static/viewimage/" + oldName);
        RedisUtil.deleteByNamespacePrefix("EMRM:Sys:");
        sysConfigMapper.updateByPrimaryKeySelective(sysConfigByName);
        // 返回成功头像路径*/
        return RUtil.success("/static/viewimage/" + oldName);
    }

    @Override
    public R getHospitalLogoPath(String logoType) {
        StringBuffer requestURL = request.getRequestURL();
        String pathPrefix = requestURL.substring(0, requestURL.indexOf("/system/getHospitalLogoPath"));
        SysConfig sysConfigByName = RedisUtil.getSysConfigByName(logoType);
        if (sysConfigByName == null || StrUtil.isBlank(sysConfigByName.getConfigValue())) {
            return RUtil.success("");
        }
        return RUtil.success(pathPrefix + sysConfigByName.getConfigValue());
    }
}