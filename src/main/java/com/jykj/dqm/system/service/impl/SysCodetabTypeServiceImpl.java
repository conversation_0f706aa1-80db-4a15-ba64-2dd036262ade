package com.jykj.dqm.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.system.dao.SysCodetabContentMapper;
import com.jykj.dqm.system.dao.SysCodetabTypeMapper;
import com.jykj.dqm.system.entity.SysCodetabContent;
import com.jykj.dqm.system.entity.SysCodetabType;
import com.jykj.dqm.system.entity.SysCodetabTypeDTO;
import com.jykj.dqm.system.service.SysCodetabContentService;
import com.jykj.dqm.system.service.SysCodetabTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 系统码值类型
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/31 16:36:35
 */
@Service
public class SysCodetabTypeServiceImpl extends ServiceImpl<SysCodetabTypeMapper, SysCodetabType> implements SysCodetabTypeService {
    @Autowired
    private SysCodetabContentService sysCodetabContentService;

    @Override
    public R addSysCodetabType(SysCodetabType sysCodetabType) {
        this.saveOrUpdate(sysCodetabType);
        return RUtil.success("新增成功！");
    }

    @Override
    public R modifySysCodetabType(SysCodetabType sysCodetabType) {
        //添加更新时间
        sysCodetabType.setModifyTime(new Date());
        this.updateById(sysCodetabType);
        return RUtil.success("更新成功！");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R deleteSysCodetabType(List<Integer> ids) {
        //删除对应的码值内容
        SysCodetabType sysCodetabType;
        for (Integer id : ids) {
            sysCodetabType = this.getById(id);
            if (sysCodetabType == null) {
                continue;
            }
            sysCodetabContentService.remove(new QueryWrapper<SysCodetabContent>().eq("TYPE_CODE", sysCodetabType.getTypeCode()));
        }
        this.removeByIds(ids);
        return RUtil.success("删除成功！");
    }

    @Override
    public R querySysCodetabType(SysCodetabTypeDTO sysCodetabTypeDTO) {
        PageHelper.startPage(sysCodetabTypeDTO.getPageNum(), sysCodetabTypeDTO.getPageSize());
        QueryWrapper<SysCodetabType> queryWrapper = new QueryWrapper<SysCodetabType>();
        queryWrapper.eq(!ObjectUtil.isEmpty(sysCodetabTypeDTO.getTypeCode()), "TYPE_CODE", sysCodetabTypeDTO.getTypeCode())
                .like(!ObjectUtil.isEmpty(sysCodetabTypeDTO.getTypeName()), "TYPE_NAME", sysCodetabTypeDTO.getTypeName());
        List<SysCodetabType> sysCodetabTypes = this.list(queryWrapper);
        PageInfo<SysCodetabType> sysCodetabTypePageInfo = new PageInfo<>(sysCodetabTypes);
        return RUtil.success(sysCodetabTypePageInfo);
    }
}

