package com.jykj.dqm.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.system.dao.SysCodetabContentMapper;
import com.jykj.dqm.system.entity.SysCodetabContent;
import com.jykj.dqm.system.entity.SysCodetabContentDTO;
import com.jykj.dqm.system.service.SysCodetabContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统码值内容
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/31 16:53:39
 */
@Service
public class SysCodetabContentServiceImpl extends ServiceImpl<SysCodetabContentMapper, SysCodetabContent> implements SysCodetabContentService {
    @Autowired
    private SysCodetabContentMapper sysCodetabContentMapper;

    @Override
    @CacheEvict(value = "SysCodetabContentServiceImpl", allEntries = true)
    public R addSysCodetabContent(SysCodetabContent sysCodetabContent) {
        this.saveOrUpdate(sysCodetabContent);
        return RUtil.success("新增成功！");
    }

    @Override
    @CacheEvict(value = "SysCodetabContentServiceImpl", allEntries = true)
    public R modifySysCodetabContent(SysCodetabContent sysCodetabContent) {
        this.updateById(sysCodetabContent);
        return RUtil.success("更新成功！");
    }

    @Override
    @CacheEvict(value = "SysCodetabContentServiceImpl", allEntries = true)
    public R deleteSysCodetabContent(List<Integer> ids) {
        this.removeByIds(ids);
        return RUtil.success("删除成功！");
    }

    @Override
    @Cacheable(value = "SysCodetabContentServiceImpl", keyGenerator = "myKeyGenerator")
    public R querySysCodetabContent(SysCodetabContentDTO sysCodetabContentDTO) {
        PageHelper.startPage(sysCodetabContentDTO.getPageNum(), sysCodetabContentDTO.getPageSize());
        QueryWrapper<SysCodetabContent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(!ObjectUtil.isEmpty(sysCodetabContentDTO.getTypeCode()), "TYPE_CODE", sysCodetabContentDTO.getTypeCode())
                .eq(!ObjectUtil.isEmpty(sysCodetabContentDTO.getContentKey()), "CONTENT_KEY", sysCodetabContentDTO.getContentKey())
                .orderByAsc(true, "TYPE_CODE", "CONTENT_SEQ");
        List<SysCodetabContent> sysCodetabContents = this.list(queryWrapper);
        PageInfo<SysCodetabContent> sysCodetabContentPageInfo = new PageInfo<>(sysCodetabContents);
        return RUtil.success(sysCodetabContentPageInfo);
    }
}
