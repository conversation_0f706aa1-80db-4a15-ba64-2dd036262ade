package com.jykj.dqm.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.system.entity.SysCodetabType;
import com.jykj.dqm.system.entity.SysCodetabTypeDTO;

import java.util.List;

/**
 * 系统码值类型
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/31 16:36:35
 */
public interface SysCodetabTypeService extends IService<SysCodetabType> {

    R addSysCodetabType(SysCodetabType sysCodetabType);

    R modifySysCodetabType(SysCodetabType sysCodetabType);

    R deleteSysCodetabType(List<Integer> ids);

    R querySysCodetabType(SysCodetabTypeDTO sysCodetabTypeDTO);
}

