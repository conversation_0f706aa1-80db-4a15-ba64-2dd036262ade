package com.jykj.dqm.system.service;

import com.jykj.dqm.common.LogLogin;
import com.jykj.dqm.common.R;
import com.jykj.dqm.system.entity.LogLoginDTO;

/**
 * @Title : LogLoginService
 * @Package : com.jykj.dqm.config.service
 * @Description :登录日志
 * <AUTHOR> 黄杰
 * @DateTime : 2021/8/18 19:03
 */
public interface LogLoginService {
    R insertSelective(LogLogin record);

    /**
     * 查询登录日志接口
     *
     * @param logLoginDTO LogLoginDTO
     * @return Result
     * <AUTHOR>
     */
    R queryLog(LogLoginDTO logLoginDTO);
}
