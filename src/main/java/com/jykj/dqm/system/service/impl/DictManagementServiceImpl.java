package com.jykj.dqm.system.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.db.ds.simple.SimpleDataSource;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.system.dao.DictManagementMapper;
import com.jykj.dqm.system.entity.DictMappings;
import com.jykj.dqm.system.entity.DictMappingsContent;
import com.jykj.dqm.system.entity.DictMappingsContentDTO;
import com.jykj.dqm.system.entity.DictMappingsDTO;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.system.service.DictManagementService;
import com.jykj.dqm.system.service.DictMappingsContentService;
import com.jykj.dqm.system.service.DictMappingsService;
import com.jykj.dqm.utils.SymmetricCryptoFactory;
import com.jykj.dqm.utils.MapperUtils;
import com.jykj.dqm.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 字典管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/17 11:11:32
 */
@Slf4j
@Service
public class DictManagementServiceImpl implements DictManagementService {
    @Autowired
    private DictManagementMapper dictManagementMapper;

    @Autowired
    private DictMappingsService dictMappingsService;

    @Autowired
    private DictMappingsContentService dictMappingsContentService;

    @Autowired
    DataSourceTransactionManager dataSourceTransactionManager;

    @Autowired
    TransactionDefinition transactionDefinition;

    /**
     * 查询字典管理列表接口
     *
     * @param dictMappingDTO DictMappingsDTO
     * @return 字典管理列表
     * <AUTHOR>
     */
    @Override
    public R getDictManagementList(DictMappingsDTO dictMappingDTO) {
        PageHelper.startPage(dictMappingDTO.getPageNum(), dictMappingDTO.getPageSize());
        List<DictMappings> dictMappings = dictManagementMapper.queryDictList(dictMappingDTO);
        PageInfo<DictMappings> pageInfo = new PageInfo<>(dictMappings);
        return RUtil.success(pageInfo);
    }

    /**
     * 同步主数据系统数据
     * <p>
     * 如果是本库，直接同步
     * 如果是其他库，根据dataSoureceId建立连接
     * 同步DQM_DICT_MAPPINGS
     * 同步MDM_DICT_MAPPINGS_CONTENT
     *
     * @return Result
     * <AUTHOR>
     */
    @Override
    public R syncDictData() {
        // 从系统设置里面获取
        SysConfig sysConfigByName = RedisUtil.getSysConfigByName("mdm.datasource.id");
        String dataSourceId = sysConfigByName.getConfigValue();
        TransactionStatus transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
        try {
            if (StrUtil.isNotBlank(dataSourceId) && !"0".equalsIgnoreCase(dataSourceId)) {
                syncOtherDBDictData(dataSourceId);
            } else {
                // 在一个库，直接同步
                // dictManagementMapper.truncateTable("DQM_DICT_MAPPINGS");
                // dictManagementMapper.syncDictMappingsData();
                // dictManagementMapper.truncateTable("DQM_DICT_MAPPINGS_CONTENT");
                // dictManagementMapper.syncDictMappingsContentData();
                // 存储过程
                dictManagementMapper.syncDictDataByProcedure();
            }
            dataSourceTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            dataSourceTransactionManager.rollback(transactionStatus);
            throw new RuntimeException(e);
        }
        return RUtil.success();
    }

    @Override
    public R getDictDataList(DictMappingsContentDTO dictMappingsContentDTO) {
        PageHelper.startPage(dictMappingsContentDTO.getPageNum(), dictMappingsContentDTO.getPageSize());
        List<DictMappingsContent> dictMappings = dictManagementMapper.queryDictDataList(dictMappingsContentDTO);
        PageInfo<DictMappingsContent> pageInfo = new PageInfo<>(dictMappings);
        return RUtil.success(pageInfo);
    }

    @Override
    public R getSysInfoList() {
        List<Map<String, String>> map = dictManagementMapper.getSysInfoList();
        return RUtil.success(map);
    }

    /**
     * 处理MDM数据和DQM不在一个库的情况
     *
     * @param dataSourceId 数据源ID
     * @throws SQLException
     * <AUTHOR>
     */
    private void syncOtherDBDictData(String dataSourceId) throws SQLException {
        // 根据dataSoureceId建立连接
        MetadataDatasource metadataDatasource = RedisUtil.getMetadataDatasourceById(dataSourceId);
        DataSource ds = new SimpleDataSource(metadataDatasource.getDatabaseUrl(), metadataDatasource.getDatabaseUser(),
                SymmetricCryptoFactory.decrypt(metadataDatasource.getDatabasePwd()));
        String queryDictMappings = "select " +
                "       a.TERM_ID as termId," +
                "       a.TERM_NAME as termName," +
                "       a.MAPPINGS_TABLE as mappingsTable," +
                "       a.MAPPINGS_TYPE as mappingsType," +
                "       a.DELETED_FLAG as deletedFlag," +
                "       a.CODE_FIELD as codeField," +
                "       a.NAME_FIELD as nameField," +
                "       a.MATCH_TYPE as matchType," +
                "       a.BD_CODE as bdCode," +
                "       a.BD_NAME as bdName " +
                "  from MDM_DICT_MAPPINGS a" +
                "  left join MDM_DICT_MAPPINGS_TYPE b" +
                "    on a.mappings_type = b.mappings_type" +
                " WHERE a.DELETED_FLAG = 0";
        List<Entity> mdm_dict_mappings = Db.use(ds).query(queryDictMappings);
        List<DictMappings> dictMappings = MapperUtils.INSTANCE.mapAsList(DictMappings.class, mdm_dict_mappings);
        dictMappingsService.remove(new QueryWrapper<>());
        dictMappingsService.saveBatch(dictMappings);
        String queryDictMappingsContent = "select " +
                "       a.TERM_ID as termId," +
                "       a.SOURCE_FIELD as sourceField," +
                "       a.SOURCE_FIELD_CODE as sourceFieldCode," +
                "       a.TARGET_FIELD as targetField," +
                "       a.TARGET_FIELD_CODE as targetFieldCode," +
                "       (SELECT TERM_NAME FROM MDM_DICT_TERMINOLOGY WHERE TERM_ID = a.TERM_ID AND ROWNUM = 1) AS termName"
                +
                "  from MDM_DICT_MAPPINGS_CONTENT a" +
                " where a.DELETED_FLAG = '0'";
        // todo 分段查询
        List<Entity> mdm_dict_mappings_content = Db.use(ds).query(queryDictMappingsContent);
        List<DictMappingsContent> dictMappingsContents = MapperUtils.INSTANCE.mapAsList(DictMappingsContent.class,
                mdm_dict_mappings_content);
        dictMappingsContentService.remove(new QueryWrapper<>());
        dictMappingsContentService.saveBatch(dictMappingsContents, 1000);
    }
}
