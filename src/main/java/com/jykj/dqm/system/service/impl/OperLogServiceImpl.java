package com.jykj.dqm.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.OperLog;
import com.jykj.dqm.common.OperLogForm;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.system.dao.OperLogMapper;
import com.jykj.dqm.system.service.OperLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OperLogServiceImpl implements OperLogService {
    @Autowired
    private OperLogMapper operLogMapper;

    @Override
    public R queryLog(OperLogForm operLogQuery) {
        PageHelper.startPage(operLogQuery.getPageNum(), operLogQuery.getPageSize());
        if (StrUtil.isNotBlank(operLogQuery.getEndTime())) {
            operLogQuery.setEndTime(operLogQuery.getEndTime() + " 23:59:59");
        }
        List<OperLog> list = operLogMapper.queryLog(operLogQuery);
        PageInfo<OperLog> pageInfo = new PageInfo<OperLog>(list);
        return RUtil.success(pageInfo);
    }

    @Override
    public R insertLog(OperLog operLog) {
        operLogMapper.insertSelective(operLog);
        return RUtil.success();
    }
}
