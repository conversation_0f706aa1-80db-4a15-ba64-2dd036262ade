package com.jykj.dqm.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.common.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 系统码值类型
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/31 16:46:51
 */
@ApiModel(value = "系统码值类型查询")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "SYS_CODETAB_TYPE")
public class SysCodetabTypeDTO {
    /**
     * 类型编码
     */
    @TableField(value = "TYPE_CODE")
    @ApiModelProperty(value = "类型编码")
    private String typeCode;

    /**
     * 类型名称
     */
    @TableField(value = "TYPE_NAME")
    @ApiModelProperty(value = "类型名称")
    private String typeName;


    /**
     * 系统编码
     */
    @TableField(value = "SYS_ID")
    @ApiModelProperty(value = "系统编码", hidden = true)
    private String sysId = Constant.SYS_NAME;


    /**
     * 分页参数 当前页
     */
    @ApiModelProperty(value = "分页参数 当前页")
    private Integer pageNum = 1;

    /**
     * 分页参数 每页数量
     */
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize = 10;
}