package com.jykj.dqm.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系统码值内容
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/31 16:53:39
 */
@ApiModel(value = "系统码值内容查询")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysCodetabContentDTO {
    /**
     * 类型编码
     */
    @TableField(value = "TYPE_CODE")
    @ApiModelProperty(value = "类型编码")
    private String typeCode;


    /**
     * 键
     */
    @TableField(value = "CONTENT_KEY")
    @ApiModelProperty(value = "键")
    private String contentKey;


    /**
     * 分页参数 当前页
     */
    @ApiModelProperty(value = "分页参数 当前页")
    private Integer pageNum = 1;

    /**
     * 分页参数 每页数量
     */
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize = 10;
}