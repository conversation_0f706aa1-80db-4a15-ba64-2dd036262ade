package com.jykj.dqm.system.entity;

import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 术语映射列表DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/17 10:54:29
 */
@ApiModel(value = "术语映射列表DTO")
@Data
public class DictMappingsDTO extends MyPageInfo implements Serializable {
    /**
     * 术语
     */
    @ApiModelProperty(value = "术语名称/术语编码")
    private String term;

    /**
     * 系统编码
     */
    @ApiModelProperty(value = "系统编码")
    private String sysCode;

    /**
     * 系统名称
     */
    @ApiModelProperty(value = "系统名称")
    private String sysName;

    /**
     * 映射类型
     */
    @ApiModelProperty(value = "映射类型")
    private String mappingsType;


    private static final long serialVersionUID = 1L;
}