package com.jykj.dqm.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.system.entity.SysCodetabContent;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 系统码值内容
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/31 16:53:39
 */
@Mapper
public interface SysCodetabContentMapper extends BaseMapper<SysCodetabContent> {
    /**
     * 根据条件获取码值内容
     *
     * @param params Map<String, Object>
     * @return 码值内容
     * <AUTHOR>
     */
    List<String> getSysCodeValueData(Map<String, Object> params);
}