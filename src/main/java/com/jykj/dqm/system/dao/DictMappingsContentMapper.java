package com.jykj.dqm.system.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.system.entity.DictMappingsContent;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 术语映射内容
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/17 14:26:57
 */
@Mapper
public interface DictMappingsContentMapper extends BaseMapper<DictMappingsContent> {
    /**
     * 根据条件获取字典的值
     *
     * @param params Map<String, Object>
     * @return CONTENT_KEY LIST
     * <AUTHOR>
     */
    List<String> getTermDictData(Map<String, Object> params);
}