package com.jykj.dqm.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.auth.entity.LogginForm;
import com.jykj.dqm.auth.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    /**
     * 添加用户
     *
     * @param sysUser SysUser
     */
    void addSysUser(@Param("sysUser") SysUser sysUser);

    /**
     * 添加没有密码失效时间的用户
     *
     * @param sysUser SysUser
     */
    void addSysUserWithoutPasswordExpirationDate(@Param("sysUser") SysUser sysUser);

    /**
     * 更新用户状态
     *
     * @param sysUser SysUser
     */
    void updateUserStatus(@Param("sysUser") SysUser sysUser);

    /**
     * 通过用户Id更新用户
     *
     * @param user SysUser
     */
    void updateByUserId(@Param("sysUser") SysUser user);

    /**
     * 查询用户
     *
     * @param loginId   登录ID
     * @param userName  用户名
     * @param mobile    手机号
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return List<SysUser>
     */
    List<SysUser> queryList(@Param("loginId") String loginId, @Param("userName") String userName, @Param("mobile") String mobile, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 更新密码
     *
     * @param sysUser SysUser
     */
    void updatePasswordByUserId(@Param("sysUser") SysUser sysUser);

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    List<String> getUserName();

    /**
     * 获取用户名和登录Id
     *
     * @param id 用户Id
     * @return 用户名和登录Id
     */
    Map<String, String> getUserById(String id);

    /**
     * 通过用户Id，获取用户
     *
     * @param userId 用户Id
     * @return SysUser
     */
    SysUser getUser(String userId);

    /**
     * 通过用户ID，获取用户名
     *
     * @param id 用户ID
     * @return 用户名
     */
    String getUserNameById(String id);

    /**
     * 查询用户信息
     *
     * @param loggingForm LoggingForm
     * @return SysUser
     */
    SysUser queryUserInfo(LogginForm loggingForm);

    /**
     * 根据用户账号查询用户信息
     *
     * @param UserAccount 用户账号
     * @return 用户信息
     * <AUTHOR>
     */
    SysUser selectByUserAccount(String UserAccount);

    /**
     * 根据用户手机号查询用户信息
     *
     * @param mobile 手机号
     * @return 用户信息
     * <AUTHOR>
     */
    SysUser selectByUserMobile(String mobile);

    /**
     * 根据用户账号获取用户信息，手机号，邮箱，登录方式
     *
     * @param loginId 用户账号
     * @return String 验证方式（0：帐密；1：验证码 ；2：账密+验证码;
     */
    SysUser getUserInfoByLoginId(String loginId);

    /**
     * 根据用户企业微信账号查询用户
     *
     * @param wxAccount 企业微信账号
     * @return 用户信息
     * <AUTHOR>
     */
    SysUser selectByUserWxAccount(String wxAccount);
}
