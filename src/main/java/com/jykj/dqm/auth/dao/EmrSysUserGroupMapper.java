package com.jykj.dqm.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.auth.entity.EmrSysUserGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_SYS_USER_GROUP(用户分组表)】的数据库操作Mapper
 * @createDate 2024-03-15 17:56:21
 * @Entity com.jykj.dqm.auth.entity.EmrSysUserGroup
 */
@Mapper
public interface EmrSysUserGroupMapper extends BaseMapper<EmrSysUserGroup> {
    List<EmrSysUserGroup> queryByGroupName(@Param("groupName") String groupName, @Param("userAccount") String userAccount, @Param("projectId") String projectId);
}
