package com.jykj.dqm.auth.dao;

import com.jykj.dqm.auth.entity.UserWxAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户和企业微信用户关联表
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/3/31 15:05
 */
@Mapper
public interface UserWxAccountMapper {
    int deleteByPrimaryKey(int id);

    int insertSelective(UserWxAccount record);

    UserWxAccount selectByPrimaryKey(int id);

    int updateByPrimaryKeySelective(UserWxAccount record);

    int batchInsert(@Param("list") List<UserWxAccount> list);

    UserWxAccount selectByUserAccount(String userAccount);

    UserWxAccount selectByWxAccount(String wxAccount);

    int deleteByUserAccount(String loginId);
}