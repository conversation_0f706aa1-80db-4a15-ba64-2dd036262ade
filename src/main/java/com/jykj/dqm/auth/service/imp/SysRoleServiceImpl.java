package com.jykj.dqm.auth.service.imp;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.auth.dao.SysRoleMapper;
import com.jykj.dqm.auth.dao.SysRolePermissionMapper;
import com.jykj.dqm.auth.entity.PermissionVo;
import com.jykj.dqm.auth.entity.SysRole;
import com.jykj.dqm.auth.entity.SysRolePermission;
import com.jykj.dqm.auth.service.SysPermissionService;
import com.jykj.dqm.auth.service.SysRolePermissionService;
import com.jykj.dqm.auth.service.SysRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Service
@Slf4j
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    @Autowired
    private SysRoleMapper roleMapper;
    @Autowired
    private SysRolePermissionMapper sysRolePermissionMapper;
    @Autowired
    private SysPermissionService sysPermissionService;
    @Autowired
    private SysRolePermissionService sysRolePermissionService;

    /**
     * 添加角色
     *
     * @param sysRole SysRole
     * @return Result
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R add(SysRole sysRole) {
        roleMapper.addSysRole(sysRole);
        List<PermissionVo> permissionIds = sysRole.getPermissionIds();
        List<String> list = getPermissionId(permissionIds, new ArrayList<>());
        list.stream().forEach(permissionId -> {
            SysRolePermission sysRolePermission = new SysRolePermission();
            sysRolePermission.setRoleId(sysRole.getRoleId());
            sysRolePermission.setPermissionId(permissionId);
            sysRolePermissionMapper.add(sysRolePermission);
        });
        return RUtil.success();
    }

    /**
     * 获取角色Id
     *
     * @param permissionVos List<PermissionVo>
     * @param list          权限Id集合
     * @return 权限Id集合
     */
    private List<String> getPermissionId(List<PermissionVo> permissionVos, List<String> list) {
        for (PermissionVo permissionVo : permissionVos) {
            list.add(permissionVo.getPermissionId() + "");
            if (permissionVo.getChildren().size() != 0) {
                getPermissionId(permissionVo.getChildren(), list);
            }
        }
        return list;
    }

    /**
     * 更新角色
     *
     * @param sysRole SysRole
     * @return Result
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R updateRole(SysRole sysRole) {
        roleMapper.updateByRoleId(sysRole);
        sysRolePermissionMapper.deleteByRoleId(sysRole.getRoleId());
        sysRolePermissionService.addByRoleId(sysRole.getRoleId(), sysRole.getPermissionIds());
        return RUtil.success();
    }

    /**
     * 通过角色Id获取权限
     *
     * @param roleId 角色Id
     * @return Result
     */
    @Override
    public R queryById(String roleId) {
        SysRole role = this.getById(roleId);
        List<PermissionVo> list = sysPermissionService.getPermissionByRoleId(roleId);
        role.setPermissionIds(list);
        return RUtil.success(role);
    }

    /**
     * 查询角色及其权限
     *
     * @param page 当前页
     * @param size 每页数量
     * @return PageInfo
     */
    @Override
    public PageInfo querylist(int page, int size) {
        PageHelper.startPage(page, size);
        List<SysRole> roleList = roleMapper.querylist();
        PageInfo<SysRole> pageInfo = new PageInfo<>(roleList);
        List<SysRole> list = pageInfo.getList();
        list.stream().forEach(sysRole -> {
            sysRole.setPermissionIds(sysPermissionService.getPermissionByRoleId(sysRole.getRoleId()));
        });
        return pageInfo;
    }

    /**
     * 通过角色Id删除角色
     *
     * @param roleId 角色Id
     */
    @Override
    public void deleteById(Integer roleId) {
        roleMapper.deleteByRoleId(roleId);
    }
}
