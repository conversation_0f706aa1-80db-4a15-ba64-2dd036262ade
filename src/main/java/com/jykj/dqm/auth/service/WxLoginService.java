package com.jykj.dqm.auth.service;

import com.jykj.dqm.auth.entity.UserWxAccount;
import com.jykj.dqm.common.R;

/**
 * 微信登录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/7/26 15:15
 */
public interface WxLoginService {
    /**
     * 获取授权url接口
     *
     * @param state 状态码
     * @return 授权url接口
     * @throws Exception
     * <AUTHOR>
     */
    R getAuthUrl(String state) throws Exception;

    /**
     * 首次扫码登录绑定企业微信账户和DQM账户
     *
     * @param odrUserWxAccount UserWxAccount
     * @return Result
     * <AUTHOR>
     */
    R bindAccount(UserWxAccount odrUserWxAccount);
}
