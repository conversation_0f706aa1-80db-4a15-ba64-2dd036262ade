package com.jykj.dqm.auth.service.imp;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.auth.dao.EmrSysUserGroupMapper;
import com.jykj.dqm.auth.entity.EmrSysUserGroup;
import com.jykj.dqm.auth.entity.EmrSysUserGroupVo;
import com.jykj.dqm.auth.service.EmrSysUserGroupService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_SYS_USER_GROUP(用户分组表)】的数据库操作Service实现
 * @createDate 2024-03-15 17:56:21
 */
@Service
public class EmrSysUserGroupServiceImpl extends ServiceImpl<EmrSysUserGroupMapper, EmrSysUserGroup> implements EmrSysUserGroupService {
    @Autowired
    private EmrSysUserGroupMapper emrSysUserGroupMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R save(EmrSysUserGroupVo emrSysUserGroupVo) {
        emrSysUserGroupVo.getUserList().stream().forEach(item -> {
            //去掉特殊的0，使用数据库自增的ID
            if (item.getId() != null && 0 == item.getId()) {
                item.setId(null);
            }
            item.setGroupName(emrSysUserGroupVo.getGroupName());
            item.setProjectId(emrSysUserGroupVo.getProjectId());
        });
        remove(Wrappers.<EmrSysUserGroup>lambdaQuery().eq(EmrSysUserGroup::getGroupName, emrSysUserGroupVo.getGroupName()).eq(EmrSysUserGroup::getProjectId, emrSysUserGroupVo.getProjectId()));
        saveBatch(emrSysUserGroupVo.getUserList());
        return RUtil.success();
    }

    @Override
    public List<EmrSysUserGroup> queryByGroupName(String groupName, String userAccount, String projectId) {
        List<EmrSysUserGroup> emrSysUserGroupList = emrSysUserGroupMapper.queryByGroupName(groupName, userAccount, projectId);
        return emrSysUserGroupList;
    }
}
