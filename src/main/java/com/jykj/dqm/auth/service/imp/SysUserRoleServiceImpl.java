package com.jykj.dqm.auth.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.auth.dao.SysUserRoleMapper;
import com.jykj.dqm.auth.entity.SysUserRole;
import com.jykj.dqm.auth.service.SysUserRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Slf4j
@Service
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements SysUserRoleService {
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    /**
     * 查询用户角色
     *
     * @param page 当前页
     * @param size 每页数量
     * @return List<SysUserRole>
     */
    @Override
    public R queryList(Integer page, Integer size) {
        PageHelper.startPage(page, size);
        List<SysUserRole> list = sysUserRoleMapper.queryList();
        return RUtil.success(new PageInfo<>(list));
    }

    /**
     * 添加用户角色
     *
     * @param sysUserRole SysUserRole
     * @return Result
     */
    @Override
    public R add(SysUserRole sysUserRole) {
        sysUserRoleMapper.add(sysUserRole);
        return RUtil.success();
    }

    /**
     * 通过用户Id查询用户角色
     *
     * @param userId 用户Id
     * @return List<SysUserRole>
     */
    @Override
    public List<SysUserRole> queryByUserId(String userId) {
        return this.list(new QueryWrapper<SysUserRole>().eq("user_id", userId).eq("sys_id", Constant.SYS_NAME));
    }

    /**
     * 通过用户Id删除
     *
     * @param userId userId
     */
    @Override
    public void deleteByUserId(Integer userId) {
        sysUserRoleMapper.delete(new QueryWrapper<SysUserRole>().eq("user_id", userId).eq("sys_id", Constant.SYS_NAME));
    }

    /**
     * 通过用户Id获取所有角色Id
     *
     * @param userId 用户Id
     * @return 所有角色Id
     */
    @Override
    public List<String> getAllRoleList(String userId) {
        return sysUserRoleMapper.getAllRoleList(userId);
    }

    /**
     * 通过用户Id获取所有权限Id
     *
     * @param userId 用户Id
     * @return 所有权限Id
     */
    @Override
    public List<String> getAllPermissionList(String userId) {
        return sysUserRoleMapper.getAllPermissionList(userId);
    }
}
