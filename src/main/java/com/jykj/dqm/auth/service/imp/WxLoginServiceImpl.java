/*
 * Copyright (c) 2021-2022 蓬安县妇幼 All Rights Reserved.
 */

package com.jykj.dqm.auth.service.imp;

import cn.dev33.satoken.stp.StpUtil;
import com.jykj.dqm.auth.dao.SysUserMapper;
import com.jykj.dqm.auth.dao.UserWxAccountMapper;
import com.jykj.dqm.auth.entity.SysUser;
import com.jykj.dqm.auth.entity.UserWxAccount;
import com.jykj.dqm.auth.entity.WeiXinConstant;
import com.jykj.dqm.auth.entity.WxParamsProperties;
import com.jykj.dqm.auth.service.WxLoginService;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 微信登录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/7/26 15:14
 */
@Slf4j
@Service
public class WxLoginServiceImpl implements WxLoginService {
    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private UserWxAccountMapper userWxAccountMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private WxParamsProperties wxParamsProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R bindAccount(UserWxAccount odrUserWxAccount) {
        SysUser userInfo = sysUserMapper.getUserInfoByLoginId(odrUserWxAccount.getUserAccount());
        if (userInfo == null) {
            return RUtil.error("账号不存在！！！");
        }
        //验证系统里面的账号对应的名字和企业微信对应的名字是否一样
        //一样就绑定
        String name;
        if (!StringUtil.isEmpty(odrUserWxAccount.getUserName())) {
            name = odrUserWxAccount.getUserName();
        } else {
            name = redisTemplate.opsForValue().get("dqmwx::userName::" + odrUserWxAccount.getWxAccount());
        }
        if (!userInfo.getUserName().equals(name)) {
            return RUtil.error("用户名不匹配,绑定失败！");
        }
        ////验证用户名和密码是否正确，如果正确，绑定当前微信
        //String passwordMd5Code = MD5Util.EncoderByMd5(odrUserWxAccount.getUserPassWord());
        //if (!userInfo.getPassword().equalsIgnoreCase(passwordMd5Code)) {
        //    return ResultUtil.error("用户名或密码错误！绑定失败！");
        //}
        UserWxAccount odrUserWxAccount1 = userWxAccountMapper.selectByUserAccount(odrUserWxAccount.getUserAccount());
        String wxAccount = odrUserWxAccount.getWxAccount();
        if (odrUserWxAccount1 != null) {
            //一个账号关联几个企业微信账户场景
            wxAccount = odrUserWxAccount1.getWxAccount();
            if (!StringUtil.isEmpty(wxAccount) && wxAccount.contains(odrUserWxAccount.getWxAccount())) {
                //已经绑定直接登陆
                StpUtil.login(userInfo.getUserId());
                return RUtil.success(StpUtil.getTokenValueByLoginId(userInfo.getUserId()));
            }
            if (!StringUtil.isEmpty(wxAccount) && !wxAccount.contains(odrUserWxAccount.getWxAccount())) {
                wxAccount = wxAccount + "," + odrUserWxAccount.getWxAccount();
            }
        }
        //添加企业微信账户
        odrUserWxAccount.setSysId(Constant.SYS_NAME);
        UserWxAccount odrUserWxAccountDb = UserWxAccount.builder()
                .userAccount(odrUserWxAccount.getUserAccount())
                .wxAccount(wxAccount)
                .userName(userInfo.getUserName())
                .sysId(Constant.SYS_NAME).build();
        userWxAccountMapper.deleteByUserAccount(odrUserWxAccount.getUserAccount());
        userWxAccountMapper.insertSelective(odrUserWxAccountDb);
        StpUtil.login(userInfo.getUserId());
        //根据userId获取token过期（数据库是小时）
        int tokenExpirationTime = userInfo.getTokenExpirationTime();
        //页面上要设置token过期时间，Sa-Token无法做到实时更新token的失效时间，所以放入redis中，全局拦截校验，首先判断redis中是否存在
        redisTemplate.opsForValue().set(StpUtil.getTokenValue(), "1", tokenExpirationTime, TimeUnit.HOURS);
        return RUtil.success(StpUtil.getTokenValueByLoginId(userInfo.getUserId()));
    }

    public R getAuthUrl(String state) throws Exception {
        // 获取用户认证授权URL，来获取Authorization Code
        String oauthUrl = WeiXinConstant.AUTH_URL
                .replace(WeiXinConstant.APPID, wxParamsProperties.getAppId())
                .replace(WeiXinConstant.REDIRECT_URI, URLEncoder.encode(wxParamsProperties.getWxRedirectUri(), "UTF-8"))
                .replace(WeiXinConstant.SCOPE, WeiXinConstant.SNSAPI_USERINFO)
                .replace(WeiXinConstant.STATE, state)
                .replace(WeiXinConstant.AGENT_ID, wxParamsProperties.getAgentId());
        Map result = new HashMap<String, String>();
        result.put("authUrl", oauthUrl);
        result.put("state", state);
        return RUtil.success(result);
    }
}
