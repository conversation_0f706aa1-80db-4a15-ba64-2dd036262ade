package com.jykj.dqm.auth.service.imp;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.auth.dao.SysPermissionMapper;
import com.jykj.dqm.auth.entity.PermissionVo;
import com.jykj.dqm.auth.entity.SysPermission;
import com.jykj.dqm.auth.entity.SysRolePermission;
import com.jykj.dqm.auth.entity.SysUserRole;
import com.jykj.dqm.auth.service.SysPermissionService;
import com.jykj.dqm.auth.service.SysRolePermissionService;
import com.jykj.dqm.auth.service.SysUserRoleService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Slf4j
@Service
public class SysPermissionServiceImpl extends ServiceImpl<SysPermissionMapper, SysPermission> implements SysPermissionService {
    @Autowired
    private SysPermissionMapper sysPermissionMapper;

    @Autowired
    private SysRolePermissionService sysRolePermissionService;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    /**
     * 新增权限
     *
     * @param sysPermission SysPermission
     * @return Result
     */
    @Override
    public R add(SysPermission sysPermission) {
        sysPermissionMapper.add(sysPermission);
        return RUtil.success();
    }

    /**
     * 查询所有权限
     *
     * @return Result
     */
    @Override
    public R queryList() {
        List<SysPermission> list = sysPermissionMapper.queryList();
        List list1 = buildTree(list);
        return RUtil.success(list1);
    }

    /**
     * 根据用户Id获取权限
     *
     * @param userId 用户Id
     * @return Result
     */
    @Override
    public R getPermission(String userId) {
        List<SysUserRole> userRoleList = sysUserRoleService.queryByUserId(userId);
        Set<Integer> roleIdSet = new HashSet<>();
        userRoleList.stream().forEach(sysUserRole ->
                roleIdSet.add(sysUserRole.getRoleId()));
        Set<SysRolePermission> rolePermissionSet = new HashSet<>();
        roleIdSet.stream().forEach(roleId ->
                rolePermissionSet.addAll(sysRolePermissionService.queryByRoleId(roleId)));
        Set<String> permissionIdSet = rolePermissionSet.stream().map(SysRolePermission::getPermissionId).collect(Collectors.toSet());

        //批量查询权限
        Set<SysPermission> permissionSet = sysPermissionMapper.getSysPermissionList(permissionIdSet);
        List<SysPermission> list = new ArrayList<>();
        list.addAll(permissionSet);
        Collections.sort(list, new Comparator<SysPermission>() {
            @Override
            public int compare(SysPermission o1, SysPermission o2) {
                return o1.getPermissionId() - o2.getPermissionId();
            }
        });
        List result = this.buildTree(list);
        return RUtil.success(result);
    }

    /**
     * 根据角色Id获取权限
     *
     * @param roleId 角色Id
     * @return List<PermissionVo>
     */
    @Override
    public List<PermissionVo> getPermissionByRoleId(String roleId) {
        List<SysRolePermission> list = sysRolePermissionService.queryByRoleId(Integer.parseInt(roleId));
        Set<String> permissionIdSet = list.stream().map(SysRolePermission::getPermissionId).collect(Collectors.toSet());
        Set<SysPermission> permissionSet = sysPermissionMapper.getSysPermissionList(permissionIdSet);
        ArrayList<SysPermission> arrayList = new ArrayList<>(permissionSet);
        arrayList.removeAll(Collections.singleton(null));
        if (arrayList != null && arrayList.size() != 0) {
            Collections.sort(arrayList, new Comparator<SysPermission>() {
                @Override
                public int compare(SysPermission o1, SysPermission o2) {
                    return o1.getPermissionId() - o2.getPermissionId();
                }
            });
        }
        List resultList = this.buildTree(arrayList);
        return resultList;
    }

    /**
     * 创建树形结构
     *
     * @param permissionSet List<SysPermission>
     * @return List
     */
    private List buildTree(List<SysPermission> permissionSet) {
        //首先查询父级id为0的权限
        List<PermissionVo> list = new ArrayList<>();

        permissionSet.stream().forEach(sysPermission -> {
            if (sysPermission.getParentId() == null || sysPermission.getParentId() == 0) {
                PermissionVo permissionVo = new PermissionVo();
                BeanUtils.copyProperties(sysPermission, permissionVo);
                list.add(permissionVo);
            }
        });

        for (PermissionVo permissionVo : list) {
            permissionVo.setChildren(getChildren(permissionVo, permissionSet));
        }
        Collections.sort(list, (o1, o2) -> o1.getPermissionId() - o2.getPermissionId());
        return list;
    }

    /**
     * 获取子节点
     *
     * @param permissionVo  PermissionVo
     * @param permissionSet List<SysPermission>
     * @return List
     */
    private List getChildren(PermissionVo permissionVo, List<SysPermission> permissionSet) {
        List<PermissionVo> childList = permissionVo.getChildren();
        for (SysPermission sysPermission : permissionSet) {
            if (permissionVo.getPermissionId().equals(sysPermission.getParentId())) {
                PermissionVo permissionVo1 = new PermissionVo();
                BeanUtils.copyProperties(sysPermission, permissionVo1);
                childList.add(permissionVo1);
            }
        }
        for (PermissionVo permissionVo1 : childList) {
            permissionVo1.setChildren(getChildren(permissionVo1, permissionSet));
        }
        if (childList.size() == 0) {
            return new ArrayList();
        }
        Collections.sort(childList, new Comparator<PermissionVo>() {
            @Override
            public int compare(PermissionVo o1, PermissionVo o2) {
                return o1.getPermissionId() - o2.getPermissionId();
            }
        });
        return childList;
    }

}
