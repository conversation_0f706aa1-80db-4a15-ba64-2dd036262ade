package com.jykj.dqm.auth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.auth.entity.SysUserRole;

import java.util.List;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
public interface SysUserRoleService extends IService<SysUserRole> {
    /**
     * 查询用户角色
     *
     * @param page 当前页
     * @param size 每页数量
     * @return List<SysUserRole>
     */
    R queryList(Integer page, Integer size);

    /**
     * 添加用户角色
     *
     * @param sysUserRole SysUserRole
     * @return Result
     */
    R add(SysUserRole sysUserRole);

    /**
     * 通过用户Id查询用户角色
     *
     * @param userId 用户Id
     * @return List<SysUserRole>
     */
    List<SysUserRole> queryByUserId(String userId);

    /**
     * 通过用户Id删除
     *
     * @param userId userId
     */
    void deleteByUserId(Integer userId);

    /**
     * 通过用户Id获取所有角色Id
     *
     * @param userId 用户Id
     * @return 所有角色Id
     */
    List<String> getAllRoleList(String userId);

    /**
     * 通过用户Id获取所有权限Id
     *
     * @param userId 用户Id
     * @return 所有权限Id
     */
    List<String> getAllPermissionList(String userId);
}
