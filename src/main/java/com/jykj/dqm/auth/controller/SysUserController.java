package com.jykj.dqm.auth.controller;

import com.jykj.dqm.auth.entity.LogginForm;
import com.jykj.dqm.auth.entity.SysUser;
import com.jykj.dqm.auth.entity.SysUserDTO;
import com.jykj.dqm.auth.entity.UserQueryForm;
import com.jykj.dqm.auth.service.SysUserService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.sso.service.SsoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Api(tags = {"用户管理"})
@RestController
@RequestMapping("/user/")
public class SysUserController {
    private static final Logger logger = LoggerFactory.getLogger(SysUserController.class);

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SsoService ssoService;


    /**
     * 登录
     *
     * @param loggingForm LoggingForm
     * @return 登录结果
     * <AUTHOR>
     */
    @ApiOperation(value = "登录", notes = "")
    @PostMapping("doLogin")
    public R doLogin(@RequestBody LogginForm loggingForm) {
        return sysUserService.login(loggingForm);
    }


    /**
     * 单点登录
     * 实现思路：
     * 先用token去sso认证，看看是否有效，
     * 如果有效，验证主数据是否有该账号，
     * 如果有该账号，并且状态正常，直接这边登录，不再依赖SSO
     *
     * @param token 凭证
     * @return 结果
     * <AUTHOR>
     */
    @ApiOperation(value = "SSO单点登录", notes = "")
    @PostMapping("doSsoLogin")
    public R doSsoLogin(@RequestParam @NotBlank String token) {
        return ssoService.doSsoLogin(token);
    }

    /**
     * 登出
     *
     * @param token token凭证
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "登出", notes = "")
    @LogRemark(operate = "登出", module = "用户权限")
    @PutMapping("loginOut")
    public R loginOut(@RequestHeader("token") String token) {
        return sysUserService.loginOut(token);
    }

    /**
     * 添加用户
     *
     * @param sysUser SysUser
     */
    @LogRemark(operate = "添加用户", module = "用户权限")
    @PostMapping("/add")
    @ApiOperation(value = "添加用户", notes = "")
    @ApiImplicitParam(name = "sysUser", value = "登录用户", dataType = "SysUser", required = true)
    public R add(@RequestBody SysUser sysUser) {
        return sysUserService.add(sysUser);
    }


    @LogRemark(operate = "修改用户状态", module = "用户权限")
    @PostMapping("/updateUserStatus")
    @ApiOperation(value = "修改用户状态", notes = "")
    @ApiImplicitParam(name = "sysUser", value = "登录用户", dataType = "SysUser", required = true)
    public R updateUserStatus(@RequestBody SysUser sysUser) {
        return sysUserService.updateUserStatus(sysUser);
    }

    /**
     * 更新用户状态
     *
     * @param sysUser SysUser
     */
    @LogRemark(operate = "修改用户", module = "用户权限")
    @PostMapping("/updateUser")
    @ApiOperation(value = "修改用户", notes = "")
    @ApiImplicitParam(name = "sysUser", value = "登录用户", dataType = "SysUser", required = true)
    public R updateUser(@RequestBody SysUser sysUser) {
        return sysUserService.updateUser(sysUser);
    }

    /**
     * 重置密码
     *
     * @param sysUser SysUser
     */
    @LogRemark(operate = "重置密码", module = "用户权限")
    @PostMapping("/updateUserPassword")
    @ApiOperation(value = "重置密码", notes = "")
    @ApiImplicitParam(name = "sysUser", value = "登录用户", dataType = "SysUser", required = true)
    public R updateUserPassword(@RequestBody SysUser sysUser) {
        return sysUserService.updateUserPassword(sysUser);
    }

    /**
     * 通过旧密码更新密码
     *
     * @param sysUserDTO SysUserDTO
     * <AUTHOR>
     */
    @LogRemark(operate = "通过旧密码更新密码", module = "用户权限")
    @PostMapping("/updateUserPasswordByOldPS")
    @ApiOperation(value = "通过旧密码更新密码", notes = "")
    @ApiImplicitParam(name = "sysUserDTO", value = "修改密码SysUser", dataType = "SysUserDTO", required = true)
    public R updateUserPasswordByOldPS(@RequestBody SysUserDTO sysUserDTO) {
        return sysUserService.updateUserPasswordByOldPS(sysUserDTO);
    }

    /**
     * 删除用户
     *
     * @param sysUser SysUser
     * @return Result
     */
    @LogRemark(operate = "删除用户", module = "用户权限")
    @DeleteMapping("/deleteUser")
    @ApiOperation(value = "删除用户", notes = "")
    @ApiImplicitParam(name = "sysUser", value = "登录用户", dataType = "SysUser", required = true)
    public R deleteUser(@RequestBody SysUser sysUser) {
        return sysUserService.deleteUser(sysUser);
    }

    /**
     * 查询用户
     *
     * @param userQueryForm UserQueryForm
     * @return List<SysUser>
     */
    @ApiOperation(value = "查询用户", notes = "")
    @PostMapping("/queryList")
    public R queryList(@RequestBody UserQueryForm userQueryForm) {
        return sysUserService.queryList(userQueryForm);
    }

    /**
     * 查询用户账号、姓名、手机号等信息
     *
     * @param userQueryForm UserQueryForm
     * @return List<Map < String, Object>>
     */
    @ApiOperation(value = "查询用户账号、姓名、手机号等信息", notes = "")
    @PostMapping("/queryUserInfoList")
    public R queryUserInfoList(@Valid @RequestBody UserQueryForm userQueryForm) {
        return sysUserService.queryUserInfoList(userQueryForm);
    }

    /**
     * 获取用户信息
     *
     * @param token token
     * @return Result
     */
    @ApiOperation(value = "获取用户信息", notes = "")
    @GetMapping("/getUserInfo")
    public R getUserInfo(@RequestHeader("token") String token) {
        return sysUserService.getUserInfo(token);
    }

    /**
     * 检查用户信息的状态，包括密码是否过期，账号是否停用
     *
     * @return 登录结果
     * <AUTHOR>
     */
    @ApiOperation(value = "检查用户信息的状态，包括密码是否过期，是否停用", notes = "")
    @GetMapping("checkUserInfoStatus")
    public R checkUserInfoStatus() {
        return sysUserService.checkUserInfoStatus();
    }

    /**
     * 检查token是否过期,如果过期时间小于5分钟就重新获取Token
     *
     * @return token
     * <AUTHOR>
     */
    @ApiOperation(value = "检查token是否过期,如果过期时间小于5分钟就重新获取Token", notes = "")
    @PostMapping("checkToken")
    public R checkToken(@RequestHeader("token") String token) {
        return sysUserService.checkToken();
    }

    /**
     * 根据用户账号获取登录信息
     *
     * @param loginId 用户账号
     * @return Result 返回验证方式（0：帐密；1：验证码 ；2：账密+验证码）;如果是1,2，会返回脱敏的手机号；如果是0，会返回验证码Base64编码
     * <AUTHOR>
     */
    @Deprecated
    @ApiOperation(value = "根据用户账号获取登录信息", notes = "")
    @ApiImplicitParam(paramType = "query", dataType = "String", name = "loginId", value = "用户账号", required = true)
    @GetMapping("/getIdentifyInfo")
    public R getIdentifyInfo(@Param("loginId") String loginId) {
        return sysUserService.getIdentifyInfo(loginId);
    }

    /**
     * 根据获取静态验证码
     *
     * @return Result 验证码Base64编码
     * <AUTHOR>
     */
    @ApiOperation(value = "根据获取静态验证码", notes = "")
    @GetMapping("/getImagePin")
    public R getImagePin() {
        return sysUserService.getImagePin();
    }

    /**
     * 根据用户账号发送短信验证码
     *
     * @param loginId 用户账号
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "根据用户账号发送短信验证码", notes = "")
    @ApiImplicitParam(paramType = "query", dataType = "String", name = "loginId", value = "用户账号", required = true)
    @GetMapping("/sendPin")
    public R sendPin(@Param("loginId") String loginId) {
        return sysUserService.sendPin(loginId);
    }

    /**
     * 检查验证码是否正确
     *
     * @param pin 验证码
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "检查验证码是否正确", notes = "")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "pin", value = "验证码", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "uuid", value = "redis缓存验证码唯一标识", required = true)
    })
    @GetMapping("/checkImagePin")
    public R checkImagePin(@Param("pin") String pin, @Param("uuid") String uuid) {
        return sysUserService.checkPin(pin, uuid);
    }
}