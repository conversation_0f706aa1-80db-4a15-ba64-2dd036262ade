package com.jykj.dqm.auth.controller;

import com.jykj.dqm.auth.entity.SysUserRole;
import com.jykj.dqm.auth.service.SysUserRoleService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Api(tags = {"权限管理"})
@RestController
@RequestMapping("/sysUserRole")
public class SysUserRoleController {
    public static final String MODUE_NAME = "权限管理";

    @Autowired
    private SysUserRoleService sysUserRoleService;

    /**
     * 添加用户角色
     *
     * @param sysUserRole SysUserRole
     * @return Result
     */
    @ApiOperation(value = "添加用户角色", notes = "")
    @LogRemark(operate = "添加用户角色", module = MODUE_NAME)
    @PostMapping("/add")
    public R add(@RequestBody SysUserRole sysUserRole) {
        return sysUserRoleService.add(sysUserRole);
    }


    /**
     * 修改用户角色
     *
     * @param sysUserRole SysUserRole
     * @return Result
     */
    @ApiOperation(value = "修改用户角色", notes = "")
    @LogRemark(operate = "修改用户角色", module = MODUE_NAME)
    @PostMapping("/update")
    public R update(@RequestBody SysUserRole sysUserRole) {
        return RUtil.success(sysUserRoleService.updateById(sysUserRole));
    }

    /**
     * 删除用户角色
     *
     * @param sysUserRoleId 用户角色ID
     * @return Result
     */
    @ApiOperation(value = "删除用户角色", notes = "")
    @LogRemark(operate = "删除用户角色", module = MODUE_NAME)
    @PostMapping("/delete")
    public R delete(String sysUserRoleId) {
        return RUtil.success(sysUserRoleService.removeById(sysUserRoleId));
    }

    /**
     * 查询用户角色
     *
     * @param page 当前页
     * @param size 每页数量
     * @return List<SysUserRole>
     */
    @ApiOperation(value = "查询用户角色", notes = "")
    @PostMapping("/list")
    public R list(Integer page, Integer size) {
        return sysUserRoleService.queryList(page, size);
    }

    /**
     * 根据用户角色Id查询用户角色
     *
     * @param sysUserRoleId 用户角色Id
     * @return Result
     */
    @ApiOperation(value = "根据用户角色Id查询用户角色", notes = "")
    @GetMapping("/queryById")
    public R queryById(String sysUserRoleId) {
        return RUtil.success(sysUserRoleService.getById(sysUserRoleId));
    }

    /**
     * 通过用户Id查询用户角色
     *
     * @param userId 用户Id
     * @return List<SysUserRole>
     */
    @ApiOperation(value = "通过用户Id查询用户角色", notes = "")
    @GetMapping("/queryByUserId")
    public R queryByUserId(String userId) {
        return RUtil.success(sysUserRoleService.queryByUserId(userId));
    }
}
