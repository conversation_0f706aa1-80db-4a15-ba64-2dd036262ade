package com.jykj.dqm.auth.controller;


import com.jykj.dqm.auth.entity.SysRole;
import com.jykj.dqm.auth.service.SysRoleService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限管理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/30 19:03
 */
@Api(tags = {"权限管理"})
@RestController
@RequestMapping("/sysRole")
public class RoleController {
    public static final String MODUE_NAME = "权限管理";

    @Autowired
    private SysRoleService sysRoleService;

    /**
     * 添加角色
     *
     * @param sysRole SysRole
     * @return Result
     */
    @ApiOperation(value = "添加角色", notes = "")
    @LogRemark(operate = "添加角色", module = MODUE_NAME)
    @PostMapping("/add")
    public R add(@RequestBody SysRole sysRole) {
        return sysRoleService.add(sysRole);
    }

    /**
     * 更新角色
     *
     * @param sysRole SysRole
     * @return Result
     */
    @ApiOperation(value = "修改角色", notes = "")
    @LogRemark(operate = "修改角色", module = MODUE_NAME)
    @PostMapping("/updateRole")
    public R updateRole(@RequestBody SysRole sysRole) {
        return sysRoleService.updateRole(sysRole);
    }

    /**
     * 通过角色Id删除角色
     *
     * @param roleId 角色Id
     */
    @ApiOperation(value = "删除角色", notes = "")
    @LogRemark(operate = "删除角色", module = MODUE_NAME)
    @DeleteMapping("/deleteById/{roleId}")
    public R deleteById(@PathVariable(name = "roleId") String roleId) {
        sysRoleService.deleteById(Integer.parseInt(roleId));
        return RUtil.success();
    }

    /**
     * 查询角色及其权限
     *
     * @param page 当前页
     * @param size 每页数量
     * @return PageInfo
     */
    @ApiOperation(value = "查询角色列表", notes = "")
    @GetMapping("/list")
    public R list(@RequestParam("page") int page, @RequestParam("size") int size) {
        return RUtil.success(sysRoleService.querylist(page, size));
    }

    /**
     * 通过角色Id获取权限
     *
     * @param roleId 角色Id
     * @return Result
     */
    @ApiOperation(value = "通过Id查询", notes = "")
    @GetMapping("/queryById")
    public R queryById(String roleId) {
        return sysRoleService.queryById(roleId);
    }
}
