/*
 * Copyright (c) 2021-2022 蓬安县妇幼 All Rights Reserved.
 */

package com.jykj.dqm.auth.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ReUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jykj.dqm.auth.entity.UserWxAccount;
import com.jykj.dqm.auth.entity.WxParamsProperties;
import com.jykj.dqm.auth.service.SysUserService;
import com.jykj.dqm.auth.service.WxLoginService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.utils.DateTimeUtil;
import com.jykj.dqm.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 微信扫码登录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/7/25 13:37
 */
@Api(tags = {"微信扫码登录"})
@RestController
@RequestMapping("/wx/")
public class WxLoginController {
    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private WxParamsProperties wxParamsProperties;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private WxLoginService wxLoginService;

    private static final Logger logger = LoggerFactory.getLogger(WxLoginController.class);

    @ApiOperation(value = "企业微信用户登录", notes = "", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "code", value = "临时code", required = false),
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "state", value = "state", required = false)
    })
    @GetMapping(value = "/qywxLogin", name = "微信用户登录")
    public R qywxLogin(String code, String state) {
        logger.error("qywxLogin-->code:" + code + ";state:" + state);
        //校验state参数，避免网站攻击
        if (!wxParamsProperties.getState().equals(state)) {
            logger.error("wxLogin-->非法请求");
            return RUtil.error("非法请求");
        }
        String access_token = getAccessToken();
        if (access_token == null) {
            return RUtil.error("无法获取access_token，检查账号");
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("access_token", access_token);
        paramMap.put("code", code);
        String resultStr = HttpUtil.get(wxParamsProperties.getAuthAddr() + "/cgi-bin/user/getuserinfo", paramMap);
        if (StringUtil.isEmpty(resultStr)) {
            logger.error("qywxLogin-->获取用户信息失败！/cgi-bin/user/getuserinfo方法返回值为空！");
            return RUtil.error("获取用户信息失败！");
        }
        Map<String, Object> map = JSONArray.parseObject(resultStr, Map.class);
        if (map == null) {
            logger.error("qywxLogin-->用户信息获取失败！/cgi-bin/user/getuserinfo方法返回值转map为空！resultStr=" + resultStr);
            return RUtil.error("用户信息获取失败！1");
        }
        logger.error("qywxLogin-->userinfo:" + JSONObject.toJSONString(map));
        String userId = (String) (map.get("UserId"));
        if (StringUtil.isEmpty(userId)) {
            logger.error("qywxLogin-->用户信息获取失败！/cgi-bin/user/getuserinfo方法返回值中不含UserId！resultStr=" + resultStr);
            return RUtil.error("用户信息获取失败！2");
        }
        logger.warn("{}: 用户登录 -> {}", DateTimeUtil.getNowDateTimeStr(), map.get("UserId"));
        //根据userId，获取读取成员,目前是用户名
        String name = getUserInfoByUserId(userId, access_token);
        if (name == null) {
            logger.error("qywxLogin-->用户信息获取失败！根据userId，获取读取成员为空。");
            return RUtil.error("用户信息获取失败！3");
        }
        //根据微信号获取账户信息
        R checkResult = sysUserService.validateWxInterface(userId);
        if (checkResult.getStatus() != 0) {
            return checkResult;
        }
        Map result = new HashMap();
        Object data = checkResult.getData();
        if ("UNBOUND_ACCOUNT".equals(data)) {
            result.put("wxAccount", userId);
            result.put("msg", "UNBOUND_ACCOUNT");
            result.put("name", name);
            return RUtil.success(result);
        }
        result.put("wxAccount", userId);
        result.put("msg", "LOGIN_SUCCESS");
        result.put("token", data);
        return RUtil.success(result);
    }

    /**
     * 获取access_token
     *
     * @return access_token
     * <AUTHOR>
     */
    private String getAccessToken() {
        String access_token = (String) redisTemplate.opsForValue().get("dqmwx::access_token");
        if (StringUtil.isEmpty(access_token)) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("corpid", wxParamsProperties.getAppId());
            paramMap.put("corpsecret", wxParamsProperties.getSecret());
            String resultStr = HttpUtil.get(wxParamsProperties.getAuthAddr() + "/cgi-bin/gettoken", paramMap);
            if (StringUtil.isEmpty(resultStr)) {
                logger.error("qywxLogin-->用户认证失败！/cgi-bin/gettoken接口返回值为空！");
                return null;
            }
            Map<String, Object> map = JSONArray.parseObject(resultStr, Map.class);
            if (map == null) {
                logger.error("qywxLogin-->用户认证失败！/cgi-bin/gettoken接口返回值转的map为空！resultStr=" + resultStr);
                return null;
            }
            logger.error("qywxLogin-->access_token:" + JSONObject.toJSONString(map));
            access_token = (String) map.get("access_token");

            if (StringUtil.isEmpty(access_token)) {
                logger.error("qywxLogin-->用户认证失败！/cgi-bin/gettoken接口返回值中没有access_token！resultStr=" + resultStr);
                return null;
            }
            redisTemplate.opsForValue().set("dqmwx::access_token", access_token, 119L, TimeUnit.MINUTES);
        }
        return access_token;
    }


    /**
     * 根据userId，获取读取成员，用户名
     *
     * @param userId       企业微信账号
     * @param access_token access_token
     * @return 用户名
     */
    private String getUserInfoByUserId(String userId, String access_token) {
        String name = (String) redisTemplate.opsForValue().get("dqmwx::userName::" + userId);
        if (StringUtil.isEmpty(name)) {
            String resultStr = HttpUtil.get(wxParamsProperties.getAuthAddr() + "/cgi-bin/user/get?access_token=" + access_token + "&userid=" + userId);
            if (StringUtil.isEmpty(resultStr)) {
                logger.error("qywxLogin-->用户认证失败！用户详细信息接口/cgi-bin/user/get返回值空！");
                return null;
            }
            Map<String, Object> map = JSONArray.parseObject(resultStr, Map.class);
            if (map == null) {
                logger.error("qywxLogin-->用户认证失败！用户详细信息接口/cgi-bin/user/get返回值转map为空！resultStr=" + resultStr);
                return null;
            }
            logger.error("qywxLogin-->userInfo:" + JSONObject.toJSONString(map));
            name = (String) map.get("name");
            if (StringUtil.isEmpty(name)) {
                logger.error("qywxLogin-->用户认证失败！/cgi-bin/user/get返回值中没有name！resultStr=" + resultStr);
                return null;
            }
            redisTemplate.opsForValue().set("dqmwx::userName::" + userId, name);
        }
        return name;
    }

    @ApiOperation(value = "企业微信相关信息", notes = "", httpMethod = "GET")
    @GetMapping(value = "/getQywxInfo", name = "企业微信")
    public R getQywxInfo() {
        String ipPort = wxParamsProperties.getQrCodeAddr();
        String appid = wxParamsProperties.getAppId();
        String agentid = wxParamsProperties.getAgentId();
        String redirect_uri = wxParamsProperties.getQywxRedirectUri();
        String state = wxParamsProperties.getState();

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("appid", appid);
        paramMap.put("agentid", agentid);
        paramMap.put("redirect_uri", redirect_uri);
        paramMap.put("state", state);
        String url = ipPort + "/wwopen/sso/qrConnect";
        String resultStr = HttpUtil.get(url, paramMap);
        if (StringUtil.isEmpty(resultStr)) {
            throw new BusinessException("获取key失败！");
        }
        List<String> titleNew = ReUtil.findAll("\"key\":\"(\\w+)\"", resultStr, 1);
        if (CollUtil.isEmpty(titleNew)) {
            throw new BusinessException("获取key失败！");
        }
        //从页面中获取key
        String key = titleNew.get(0);
        String qrCodeUrl = ipPort + "/wwopen/sso/qrImg?key=" + key;
        Map resultMap = new HashMap();
        resultMap.put("key", key);
        resultMap.put("state", state);
        resultMap.put("qrCodeUrl", qrCodeUrl);
        return RUtil.success(resultMap);
    }

    @ApiOperation(value = "企业微信登录二维码", notes = "", httpMethod = "GET")
    @GetMapping(value = "/getQrCode", name = "企业微信")
    public R getQrCode(String key) throws Exception {
        if (StringUtil.isEmpty(key)) {
            return RUtil.error("key不能为空！");
        }
        String ipPort = wxParamsProperties.getQrCodeAddr();
        String url = ipPort + "/wwopen/sso/qrImg";
        URL urlConet = new URL(url + "?key=" + key);
        InputStream inStream = null;
        ByteArrayOutputStream outStream = null;
        try {
            HttpURLConnection con = (HttpURLConnection) urlConet.openConnection();
            con.setRequestMethod("GET");
            con.setConnectTimeout(4 * 1000);
            //通过输入流获取图片数据
            inStream = con.getInputStream();
            outStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[2048];
            int len;
            while ((len = inStream.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            byte[] data = outStream.toByteArray();
            return RUtil.success(Base64.encode(data));
        } catch (Exception e) {
            logger.error("企业微信登录二维码失败", e);
            throw new BusinessException("企业微信登录二维码失败");
        } finally {
            IoUtil.close(inStream);
            IoUtil.close(outStream);
        }
    }

    @ApiOperation(value = "获取企业微信扫码结果", notes = "", httpMethod = "GET")
    @GetMapping(value = "/getScanResult", name = "企业微信")
    public R getScanResult(String key) {
        if (StringUtil.isEmpty(key)) {
            throw new BusinessException("key为空！");
        }
        String ipPort = wxParamsProperties.getQrCodeAddr();
        String appid = wxParamsProperties.getAppId();
        String redirect_uri = wxParamsProperties.getQywxRedirectUri();
        String state = wxParamsProperties.getState();
        String timedQueryUrl = ipPort + "/wwopen/sso/l/qrConnect?callback=jsonpCallback&key=" + key + "&redirect_uri=" + redirect_uri + "&appid=" + appid + "&state=" + state + "&_=" + System.currentTimeMillis();
        String resultStr = HttpUtil.get(timedQueryUrl);
        List<String> resultStrNor = ReUtil.findAll("jsonpCallback\\(([\\s\\S]*)\\)", resultStr, 1);
        if (CollUtil.isEmpty(resultStrNor) || StringUtil.isEmpty(resultStrNor.get(0))) {
            throw new BusinessException("获取key失败！");
        }
        Map<String, String> map = JSONArray.parseObject(resultStrNor.get(0), Map.class);
        String status = map.get("status");
        String auth_code = map.get("auth_code");
        logger.error(status);
        if ("QRCODE_SCAN_NEVER".equals(status)) {
            //请扫码
            return RUtil.error("QRCODE_SCAN_NEVER");
        } else if ("QRCODE_SCAN_SUCC".equals(status)) {
            //扫码成功
            return RUtil.success(auth_code);
        } else if ("QRCODE_SCAN_ERR".equals(status)) {
            //超时
            return RUtil.error("QRCODE_SCAN_ERR");
        } else if ("QRCODE_SCAN_ING".equals(status)) {
            //扫码中（已扫描请确认）
            return RUtil.error("QRCODE_SCAN_ING");
        }
        return RUtil.error("扫码错误");
    }

    /**
     * 首次扫码登录绑定企业微信账户和DQM账户
     *
     * @param odrUserWxAccount UserWxAccount
     * @return Result
     * <AUTHOR>
     */
    @PostMapping("/bindAccount")
    @ApiOperation(value = "绑定企业微信账户和DQM账户", notes = "", httpMethod = "POST")
    public R bindAccount(@RequestBody UserWxAccount odrUserWxAccount) {
        return wxLoginService.bindAccount(odrUserWxAccount);
    }

    /**
     * 获取授权url接口和state
     *
     * @return 授权url接口和state
     * @throws Exception
     * <AUTHOR>
     */
    @ApiOperation(value = "微信登录获取授权url接口和state", notes = "", httpMethod = "GET")
    @GetMapping("/getAuthUrl")
    public R getAuthUrl() throws Exception {
        // 用于第三方应用防止CSRF攻击
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        //保存5分钟
        redisTemplate.opsForValue().set(uuid, 1, 5, TimeUnit.MINUTES);
        return wxLoginService.getAuthUrl(uuid);
    }

    /**
     * 授权回调接口
     */
    @ApiOperation(value = "微信登录授权回调接口（微信调用）", notes = "", httpMethod = "GET")
    @GetMapping(value = "/callback")
    public String callback(HttpServletRequest request) throws Exception {
        // 得到Authorization Code
        String code = request.getParameter("code");
        // 我们放在地址中的状态码
        String state = request.getParameter("state");
        Object value = redisTemplate.opsForValue().get(state);
        R result;
        // 验证我们发送的状态码
        String stateKey = "result" + state;
        if (value == null) {
            result = RUtil.error("参数为空或者状态码已过期,状态码不正确");
            redisTemplate.opsForValue().set(stateKey, result, 5, TimeUnit.MINUTES);
            return htmlWeb("参数为空或者状态码已过期,状态码不正确");
        }
        // 建立微信用户和PC端用户的绑定关系
        String access_token = getAccessToken();
        if (access_token == null) {
            result = RUtil.error("无法获取access_token，检查账号");
            redisTemplate.opsForValue().set(stateKey, result, 5, TimeUnit.MINUTES);
            return htmlWeb("无法获取access_token，检查账号");
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("access_token", access_token);
        paramMap.put("code", code);
        String resultStr = HttpUtil.get(wxParamsProperties.getAuthAddr() + "/cgi-bin/user/getuserinfo", paramMap);
        if (StringUtil.isEmpty(resultStr)) {
            result = RUtil.error("获取用户信息失败");
            logger.error("wxLogin-->用户信息获取失败！/cgi-bin/user/getuserinfo接口返回值为空！");
            redisTemplate.opsForValue().set(stateKey, result, 5, TimeUnit.MINUTES);
            return htmlWeb("获取用户信息失败");
        }
        Map<String, Object> map = JSONArray.parseObject(resultStr, Map.class);
        if (map == null) {
            logger.error("wxLogin-->用户信息获取失败！/cgi-bin/user/getuserinfo接口返回值转map为空！");
            result = RUtil.error("用户信息获取失败！1");
            redisTemplate.opsForValue().set(stateKey, result, 5, TimeUnit.MINUTES);
            return htmlWeb("用户信息获取失败！1");
        }
        logger.error("qywxLogin-->userinfo:" + JSONObject.toJSONString(map));
        String userId = (String) (map.get("UserId"));
        if (StringUtil.isEmpty(userId)) {
            logger.error("wxLogin-->用户信息获取失败！/cgi-bin/user/getuserinfo接口返回值UserId不存在！");
            result = RUtil.error("用户信息获取失败！2");
            redisTemplate.opsForValue().set(stateKey, result, 5, TimeUnit.MINUTES);
            return htmlWeb("用户信息获取失败！2");
        }
        logger.warn("{}: 用户登录 -> {}", DateTimeUtil.getNowDateTimeStr(), map.get("UserId"));
        //根据userId，获取读取成员,目前是用户名
        String name = getUserInfoByUserId(userId, access_token);
        if (name == null) {
            logger.error("wxLogin-->用户信息获取失败！根据userId，获取读取成员为空。");
            result = RUtil.error("用户信息获取失败！3");
            redisTemplate.opsForValue().set(stateKey, result, 5, TimeUnit.MINUTES);
            return htmlWeb("用户信息获取失败！3");
        }
        //根据微信号获取账户信息
        R checkResult = sysUserService.validateWxInterface(userId);
        if (checkResult.getStatus() != 0) {
            return htmlWeb(checkResult.getMsg());
        }
        Object data = checkResult.getData();
        Map resultMap = new HashMap();
        if ("UNBOUND_ACCOUNT".equals(data)) {
            resultMap.put("wxAccount", userId);
            resultMap.put("msg", "UNBOUND_ACCOUNT");
            resultMap.put("name", name);
            result = RUtil.success(resultMap);
            redisTemplate.opsForValue().set(stateKey, result, 5, TimeUnit.MINUTES);
            logger.error(result.toString());
            return htmlWeb("当前微信未绑定账号！！！请在页面上完成绑定！！！");
        }

        resultMap.put("wxAccount", userId);
        resultMap.put("msg", "LOGIN_SUCCESS");
        resultMap.put("token", data);
        result = RUtil.success(resultMap);
        redisTemplate.opsForValue().set(stateKey, result, 5, TimeUnit.MINUTES);
        return htmlWeb("统一门户登陆成功，请返回");
    }

    /**
     * 生成微信登录返回页面
     *
     * @param content 需要显示的内容
     * @return 处理后的内容
     * <AUTHOR>
     */
    private String htmlWeb(String content) {
        String web = "<h1 align=\"center\" style=\"font-size:40px\">MYMSG</h1>";
        return web.replace("MYMSG", content);
    }

    /**
     * 微信登录接口（轮询来查询登录结果）
     */
    @ApiOperation(value = "微信登录接口（轮询来查询登录结果）", notes = "", httpMethod = "GET")
    @GetMapping(value = "/weixinLogin")
    @ApiImplicitParam(paramType = "query", dataType = "String", name = "state", value = "state", required = true)
    public Object weixinLogin(HttpServletRequest request) {
        // 我们放在地址中的状态码
        String state = request.getParameter("state");
        if (StringUtil.isEmpty(state)) {
            return RUtil.error("state参数为空");
        }
        Object result = redisTemplate.opsForValue().get(state);
        if (result == null) {
            return RUtil.error("QRCODE_SCAN_ERR");
        }
        String stateKey = "result" + state;
        result = redisTemplate.opsForValue().get(stateKey);
        if (result == null) {
            return RUtil.error("QRCODE_SCAN_NEVER");
        }
        return result;
    }
}
