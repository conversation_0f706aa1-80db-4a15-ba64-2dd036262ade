package com.jykj.dqm.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jykj.dqm.common.Constant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 权限（业务层之间的数据传递）
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/8/20 14:51
 */
@Data
@NoArgsConstructor
public class PermissionVo {
    /**
     * 集团编码
     */
    private String groupCode = Constant.GROUP_CODE;

    /**
     * 组织编码
     */
    private String organizationCode = Constant.ORGANIZATION_CODE;
    /**
     * 权限id
     */
    @TableId(type = IdType.INPUT)
    private Integer permissionId;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 系统id
     */
    private String sysId = Constant.SYS_NAME;

    /**
     * 描述
     */
    private String description;

    /**
     * 父级id
     */
    private Integer parentId;

    private List<PermissionVo> children = new ArrayList<>();
}
