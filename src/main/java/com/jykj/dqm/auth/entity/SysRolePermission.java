package com.jykj.dqm.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.common.Constant;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 角色和权限类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/8/20 14:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName(value = "SYS_ROLE_PERMISSION")
public class SysRolePermission {

    /**
     * 集团编码
     */
    @TableField(value = "GROUP_CODE")
    private String groupCode = Constant.GROUP_CODE;

    /**
     * 组织编码
     */
    @TableField(value = "ORGANIZATION_CODE")
    private String organizationCode = Constant.ORGANIZATION_CODE;

    /**
     * 角色id
     */
    @TableField(value = "ROLE_PERMISSION_ID")
    @TableId(type = IdType.INPUT)
    private Integer rolePermissionId;

    /**
     * 角色名称
     */
    @TableField(value = "ROLE_ID")
    private String roleId;

    /**
     * 系统id
     */
    @TableField(value = "PERMISSION_ID")
    private String permissionId;

    /**
     * 系统编码
     */
    @TableField(value = "SYS_ID")
    @ApiParam(value = "系统编码")
    private String sysId = Constant.SYS_NAME;
}
