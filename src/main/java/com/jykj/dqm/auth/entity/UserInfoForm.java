package com.jykj.dqm.auth.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel
public class UserInfoForm {
    /**
     * 分页参数 当前页
     */
    @ApiModelProperty(value = "分页参数 当前页", required = true)
    private Integer pageNum = 1;

    /**
     * 分页参数 每页数量
     */
    @ApiModelProperty(value = "分页参数 每页数量", required = true)
    private Integer pageSize = 10;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "用户ID")
    private Integer userId;
}
