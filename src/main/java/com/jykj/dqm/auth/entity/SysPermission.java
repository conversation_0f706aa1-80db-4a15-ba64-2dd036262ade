package com.jykj.dqm.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.common.Constant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 系统权限
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/8/20 14:53
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName(value = "SYS_PERMISSION")
public class SysPermission {
    /**
     * 集团编码
     */
    @TableField(value = "GROUP_CODE")
    private String groupCode = Constant.GROUP_CODE;

    /**
     * 组织编码
     */
    @TableField(value = "ORGANIZATION_CODE")
    private String organizationCode = Constant.ORGANIZATION_CODE;

    /**
     * 权限id
     */
    @TableId(type = IdType.INPUT)
    @TableField(value = "PERMISSION_ID")
    private Integer permissionId;

    /**
     * 权限名称
     */
    @TableField(value = "PERMISSION_NAME")
    private String permissionName;

    /**
     * 系统id
     */
    @TableField(value = "SYS_ID")
    private String sysId = Constant.SYS_NAME;

    /**
     * 描述
     */
    @TableField(value = "DESCRIPTION")
    private String description;

    /**
     * 父级id
     */
    @TableField(value = "PARENT_ID")
    private Integer parentId;
}
