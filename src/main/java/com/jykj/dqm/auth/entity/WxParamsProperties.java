package com.jykj.dqm.auth.entity;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(value = "wx.config")
@Component
public class WxParamsProperties {
    String state;
    String appId;
    String secret;
    String agentId;
    String qywxRedirectUri;
    String wxRedirectUri;
    String qrCodeAddr;
    String authAddr;
    String grantType = "authorization_code";

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getQywxRedirectUri() {
        return qywxRedirectUri;
    }

    public void setQywxRedirectUri(String qywxRedirectUri) {
        this.qywxRedirectUri = qywxRedirectUri;
    }

    public String getQrCodeAddr() {
        return qrCodeAddr;
    }

    public void setQrCodeAddr(String qrCodeAddr) {
        this.qrCodeAddr = qrCodeAddr;
    }

    public String getAuthAddr() {
        return authAddr;
    }

    public void setAuthAddr(String authAddr) {
        this.authAddr = authAddr;
    }

    public String getWxRedirectUri() {
        return wxRedirectUri;
    }

    public void setWxRedirectUri(String wxRedirectUri) {
        this.wxRedirectUri = wxRedirectUri;
    }
}
