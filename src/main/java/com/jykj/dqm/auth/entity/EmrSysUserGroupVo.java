package com.jykj.dqm.auth.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户分组表VO
 *
 * @TableName DQM_EMR_SYS_USER_GROUP
 */
@Data
public class EmrSysUserGroupVo implements Serializable {
    /**
     * 0、病历文档；1、基础数据；2、病历数据；3、质量数据；4、实证材料
     */
    @ApiModelProperty(value = "直接填名字:0、病历文档；1、基础数据；2、病历数据；3、质量数据；4、实证材料")
    private String groupName;

    private String projectId;

    List<EmrSysUserGroup> userList;

}
