package com.jykj.dqm.auth.entity;

import com.jykj.dqm.common.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 用户和企业微信账号关联关系
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/3/31 15:05
 */

/**
 * 用户和企业微信账号关联关系
 */
@ApiModel(value = "用户和企业微信账号关联关系")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserWxAccount {
    /**
     * ID
     */
    @ApiModelProperty(hidden = true, value = "ID")
    private int id;

    /**
     * 用户账号
     */
    @NotBlank
    @ApiModelProperty(value = "用户账号")
    private String userAccount;

    /**
     * 用户密码
     */
    @ApiModelProperty(value = "用户密码")
    private String userPassWord;

    /**
     * 用户姓名(方便用户手动更新)
     */
    @ApiModelProperty(value = "用户姓名")
    private String userName;

    /**
     * 企业微信账户
     */
    @NotBlank
    @ApiModelProperty(value = "企业微信账户")
    private String wxAccount;

    /**
     * 系统编码
     */
    @ApiModelProperty(hidden = true, value = "系统编码")
    private String sysId = Constant.SYS_NAME;
}