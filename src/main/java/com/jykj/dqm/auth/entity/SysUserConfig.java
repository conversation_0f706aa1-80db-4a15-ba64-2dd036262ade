package com.jykj.dqm.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jykj.dqm.common.Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/8/20 15:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName(value = "SYS_USER")
@ApiModel
public class SysUserConfig implements Serializable {
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableId(type = IdType.INPUT)
    private Integer userId;
    /**
     * 登陆用户id
     */
    @ApiModelProperty(value = "登陆用户id", required = true)
    private String loginId;
    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", required = true)
    private String password;

    /**
     * 旧密码
     */
    @ApiModelProperty(value = "旧密码", required = true)
    private String oldPassword;

    /**
     * 密码强制修改标志
     */
    @ApiModelProperty(value = "密码强制修改标志")
    private Integer passwordReset;

    /**
     * 密码失效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "密码失效时间")
    private String passwordExpirationDate;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String userName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;
    /**
     * 系统编码
     */
    @ApiModelProperty(hidden = true)
    private String sysId = Constant.SYS_NAME;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String gender;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    private String idCard;

    /**
     * 验证方式（0：密码；1：CA ；2：二维码） 默认0密码
     */
    @ApiModelProperty(value = "验证方式（0：密码；1：CA ；2：二维码） 默认0密码")
    private String identifyType = "0";

    /**
     * token过期时间（单位小时，默认2小时）
     */
    @ApiModelProperty(value = "token过期时间（单位小时，默认2小时）")
    private int tokenExpirationTime = 2;

    /**
     * 新页面打开方式，1、新开一个页面，2、当前页
     */
    @ApiModelProperty(value = "新页面打开方式，1、新开一个页面，2、当前页")
    private String newPageOpenType = "1";
}
