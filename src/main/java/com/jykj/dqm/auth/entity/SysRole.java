package com.jykj.dqm.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.jykj.dqm.common.Constant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 角色
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/8/20 14:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName(value = "SYS_ROLE")
@JsonIgnoreProperties(ignoreUnknown = true)
public class SysRole {

    /**
     * 集团编码
     */
    @TableField(value = "GROUP_CODE")
    private String groupCode = Constant.GROUP_CODE;

    /**
     * 组织编码
     */
    @TableField(value = "ORGANIZATION_CODE")
    private String organizationCode = Constant.ORGANIZATION_CODE;

    /**
     * 角色id
     */
    @TableId(type = IdType.INPUT)
    @TableField(value = "ROLE_ID")
    private String roleId;

    /**
     * 角色名称
     */
    @TableField(value = "ROLE_NAME")
    private String roleName;

    /**
     * 系统id
     */
    @TableField(value = "SYS_ID")
    private String sysId = Constant.SYS_NAME;

    /**
     * 描述
     */
    @TableField(value = "DESCRIPTION")
    private String description;

    @TableField(exist = false)
    private List<PermissionVo> permissionIds = new ArrayList<>();
}
