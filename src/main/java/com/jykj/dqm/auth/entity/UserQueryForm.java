package com.jykj.dqm.auth.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class UserQueryForm {
    @ApiModelProperty(name = "分页参数 当前页 ", required = true)
    private Integer current;
    @ApiModelProperty(name = "分页参数 每页数量 ", required = true)
    private Integer size;
    @ApiModelProperty(name = "姓名")
    private String userName;
    @ApiModelProperty(name = "手机号")
    private String mobile;
    @ApiModelProperty(name = "登录账号")
    private String loginId;
    @ApiModelProperty(name = "开始时间")
    private String startTime;
    @ApiModelProperty(name = "开始时间")
    private String endTime;
}
