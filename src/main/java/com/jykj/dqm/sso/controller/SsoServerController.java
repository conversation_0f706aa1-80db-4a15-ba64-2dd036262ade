package com.jykj.dqm.sso.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.sso.entity.SsoSysUser;
import com.jykj.dqm.sso.service.SsoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 单点登录系统接口
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/8/31 16:43
 */
@Api(tags = {"人员注册"})
@RestController
@RequestMapping("/sso")
public class SsoServerController {
    @Autowired
    private SsoService ssoService;

    /**
     * 人员注册
     *
     * @param ssoSysUser SsoSysUser
     * @return 响应结果
     */
    @ApiOperation(value = "人员注册", notes = "")
    @LogRemark(operate = "人员注册", module = "人员注册")
    @PostMapping("/userRegistration")
    public R registration(@Valid @RequestBody SsoSysUser ssoSysUser) {
        return ssoService.registration(ssoSysUser);
    }
}
