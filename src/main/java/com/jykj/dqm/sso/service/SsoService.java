package com.jykj.dqm.sso.service;

import com.jykj.dqm.common.R;
import com.jykj.dqm.sso.entity.SsoSysUser;

/**
 * 单点登录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/2 15:48
 */
public interface SsoService {
    /**
     * 单点登录人员注册
     *
     * <AUTHOR>
     * @version 1.0
     * @Date 2021/9/2 15:49
     */
    R registration(SsoSysUser ssoSysUser);

    /**
     * 单点登录
     * 实现思路：
     * 先用token去sso认证，看看是否有效，
     * 如果有效，验证主数据是否有该账号，
     * 如果有该账号，并且状态正常，直接这边登录，不再依赖SSO
     *
     * @param token 凭证
     * @return 结果
     * <AUTHOR>
     */
    R doSsoLogin(String token);
}
