//package com.jykj.dqm.config;
//
//import com.alibaba.fastjson2.support.config.FastJsonConfig;
//import com.alibaba.fastjson2.support.spring.http.converter.FastJsonHttpMessageConverter;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.http.MediaType;
//import org.springframework.http.converter.HttpMessageConverter;
//import org.springframework.web.servlet.config.annotation.EnableWebMvc;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//import java.nio.charset.Charset;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 将SpringBoot默认的JSON解析器Jackson替换为FastJson
// *
// * <AUTHOR>
// * @version 1.0
// * @Date 22/11/8 16:55:53
// */
//@Configuration
//@EnableWebMvc
//public class WebConfig implements WebMvcConfigurer {
//    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
//        FastJsonHttpMessageConverter fastJsonConverter = new FastJsonHttpMessageConverter();
//        FastJsonConfig config = new FastJsonConfig();
//        config.setCharset(Charset.forName("UTF-8"));
//        fastJsonConverter.setFastJsonConfig(config);
//        List<MediaType> list = new ArrayList<>();
//        list.add(MediaType.APPLICATION_JSON);
//        list.add(MediaType.APPLICATION_JSON_UTF8);
//        fastJsonConverter.setSupportedMediaTypes(list);
//        converters.add(fastJsonConverter);
//    }
//}
