/**
 * @Title : AsyncConfiguration
 * @Package : com.scjy.mdm.config
 * @Description :线程池配置
 * <AUTHOR> 黄杰
 * @DateTime : 2021/8/18 19:02
 */
package com.jykj.dqm.config.threadpool;

import com.alibaba.ttl.threadpool.TtlExecutors;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池配置
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Configuration
@EnableAsync
public class AsyncConfiguration {
    /**
     * 配置发送术语数据线程池
     *
     * @return ThreadPoolTaskExecutor对象
     */
    @Bean("doDQMExecutor")
    public Executor doDQMExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数：线程池创建时候初始化的线程数
        executor.setCorePoolSize(8);
        // 最大线程数：线程池最大的线程数，只有在缓冲队列满了之后才会申请超过核心线程数的线程
        executor.setMaxPoolSize(20);
        // 缓冲队列：用来缓冲执行任务的队列
        executor.setQueueCapacity(500);
        // 允许线程的空闲时间60秒：当超过了核心线程之外的线程在空闲时间到达之后会被销毁
        executor.setKeepAliveSeconds(60);
        // 线程池名的前缀：设置好了之后可以方便我们定位处理任务所在的线程池
        executor.setThreadNamePrefix("EMRM-");
        // 缓冲队列满了之后的拒绝策略：由调用线程处理（一般是主线程）
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 导出文档，支持阿里的TTL
     *
     * @return ThreadPoolTaskExecutor对象
     */
    @Bean("doExportExecutor")
    public Executor doExportExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数：线程池创建时候初始化的线程数
        executor.setCorePoolSize(16);
        // 最大线程数：线程池最大的线程数，只有在缓冲队列满了之后才会申请超过核心线程数的线程
        executor.setMaxPoolSize(32);
        // 缓冲队列：用来缓冲执行任务的队列
        executor.setQueueCapacity(500);
        // 允许线程的空闲时间60秒：当超过了核心线程之外的线程在空闲时间到达之后会被销毁
        executor.setKeepAliveSeconds(60);
        // 线程池名的前缀：设置好了之后可以方便我们定位处理任务所在的线程池
        executor.setThreadNamePrefix("EMRM-EXPORT-");
        // 缓冲队列满了之后的拒绝策略：由调用线程处理（一般是主线程）
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        executor.initialize();
        //TtlExecutors使用这个包装
        return TtlExecutors.getTtlExecutorService(executor.getThreadPoolExecutor());
    }

    //@Bean("doEMExportExecutor")
    //public Executor doEMExportExecutor() {
    //    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    //    // 核心线程数：线程池创建时候初始化的线程数
    //    executor.setCorePoolSize(8);
    //    // 最大线程数：线程池最大的线程数，只有在缓冲队列满了之后才会申请超过核心线程数的线程
    //    executor.setMaxPoolSize(16);
    //    // 缓冲队列：用来缓冲执行任务的队列
    //    executor.setQueueCapacity(100);
    //    // 允许线程的空闲时间60秒：当超过了核心线程之外的线程在空闲时间到达之后会被销毁
    //    executor.setKeepAliveSeconds(60);
    //    // 线程池名的前缀：设置好了之后可以方便我们定位处理任务所在的线程池
    //    executor.setThreadNamePrefix("EM-EMRM-EXPORT-");
    //    // 缓冲队列满了之后的拒绝策略：由调用线程处理（一般是主线程）
    //    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
    //    executor.initialize();
    //    //TtlExecutors使用这个包装
    //    return TtlExecutors.getTtlExecutorService(executor.getThreadPoolExecutor());
    //}

    @Resource
    private ThreadPoolConfigProperties threadPoolConfigProperties;

    /**
     * 公共线程池，可以打印线程情况，以及执行时间
     *
     * @return 返回一个公共线程池
     * <AUTHOR>
     */
    @Bean
    public ThreadPoolExecutor threadPoolMonitor() {
        return new ThreadPoolMonitor(
                threadPoolConfigProperties.getCoreSize(),
                threadPoolConfigProperties.getMaxSize(),
                threadPoolConfigProperties.getKeepAliveTime(),
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(threadPoolConfigProperties.getQueueCapacity()),
                new ThreadPoolMonitor.EventThreadFactory("dqm-public"),
                "dqm-public",
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }
}
