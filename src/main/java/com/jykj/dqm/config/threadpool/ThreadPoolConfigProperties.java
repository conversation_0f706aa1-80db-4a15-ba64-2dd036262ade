package com.jykj.dqm.config.threadpool;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 自定义线程池配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/12/29 10:44
 */
@ConfigurationProperties(prefix = "custom.thread-pool")
@Component
@Data
public class ThreadPoolConfigProperties {
    /**
     * 核心线程数
     */
    private Integer coreSize;
    /**
     * 最大线程数
     */
    private Integer maxSize;

    /**
     * 空闲线程存活时间
     */
    private Integer keepAliveTime;
    /**
     * 队列
     */
    private Integer queueCapacity;
}
