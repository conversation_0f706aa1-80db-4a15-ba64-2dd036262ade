package com.jykj.dqm.config;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.filter.SaServletFilter;
import cn.dev33.satoken.interceptor.SaAnnotationInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import com.jykj.dqm.common.RUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * SaToken组件配置类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Slf4j
@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {
    @Autowired
    private HttpServletRequest request;

    @Autowired
    private HttpServletResponse response;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 需要放行的路径
     * "swagger","/v2/api-docs","/csrf" 为swagger的拦截放行
     */
    private static final List<String> EXCLUDEPATH = Arrays.asList("/static", ".html", "swagger", "/v2/api-docs", "/csrf", "/druid", "minIcon.png", "/DQM", "/webjars/");

    /**
     * 注册sa-token的拦截器，打开注解式鉴权功能
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册注解拦截器
        registry.addInterceptor(new SaAnnotationInterceptor()).addPathPatterns("/**").excludePathPatterns("/user/**");
    }

    /**
     * 注册 [sa-token全局过滤器]
     *
     * @return SaServletFilter
     * <AUTHOR>
     */
    @Bean
    public SaServletFilter getSaServletFilter() {
        return new SaServletFilter()

                // 指定 [拦截路由] 与 [放行路由]
                .addInclude("/**").addExclude("/static/**").addExclude("/favicon.ico").addExclude("/user/doLogin").addExclude("/user/doSsoLogin")
                .addExclude("/user/getIdentifyInfo")
                .addExclude("/user/checkImagePin")
                .addExclude("/user/sendPin")
                .addExclude("/user/getImagePin")
                .addExclude("/system/getHospitalLogoPath")
                .addExclude("/wx/**")

                // 认证函数: 每次请求执行
                .setAuth(r -> {
                    // 路由为/syssetting/{id}的访问用户必须要有0001的权限
                    //SaRouter.match("/**", "/syssetting/**", () -> StpUtil.checkLogin());
                    // 处理过滤资源
                    if (needToCheck()) {
                        StpUtil.checkLogin();
                        //个性化看用户token是否过期
                        String token = StpUtil.getTokenValue();
                        if (!redisTemplate.hasKey(token)) {
                            StpUtil.logoutByTokenValue(token);
                            throw NotLoginException.newInstance(StpUtil.getLoginType(), "-3");
                        }
                    }
                })

                // 异常处理函数：每次认证函数发生异常时执行此函数
                .setError(e -> {
                    try {
                        response.sendError(403);
                    } catch (Exception ioException) {
                        log.error("添加response403失败！！", ioException);
                    }
                    return RUtil.error(e.getLocalizedMessage());
                })
                // 前置函数：在每次认证函数之前执行
                .setBeforeAuth(r -> {
                    // ---------- 设置一些安全响应头 ----------
                    SaHolder.getResponse()
                            // 服务器名称
                            .setServer("dqm-server")
                            // 是否可以在iframe显示视图： DENY=不可以 | SAMEORIGIN=同域下可以 | ALLOW-FROM uri=指定域名下可以
                            .setHeader("X-Frame-Options", "SAMEORIGIN")
                            // 是否启用浏览器默认XSS防护： 0=禁用 | 1=启用 | 1; model=block 启用, 并在检查到XSS攻击时，停止渲染页面
                            .setHeader("X-Frame-Options", "1; model=block")
                            // 禁用浏览器内容嗅探
                            .setHeader("X-Content-Type-Options", "nosniff")
                    ;
                });

    }

    /**
     * 是都需要校验登录
     *
     * @return true 需要校验登录，false 不需要校验登录
     * <AUTHOR>
     */
    public boolean needToCheck() {
        String requestPath = request.getRequestURI();
        for (String path : EXCLUDEPATH) {
            if ("/".equals(requestPath)) {
                return false;
            }
            if (requestPath.contains(path)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 跨域配置
     *
     * @param registry CorsRegistry
     * <AUTHOR>
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("POST", "GET", "PUT", "DELETE", "OPTIONS")
                .maxAge(3600)
                // 是否允许发送Cookie
                .allowCredentials(true)
                .allowedHeaders("*");
    }

    /**
     * 添加资源处理程序
     *
     * @param registry ResourceHandlerRegistry
     * <AUTHOR>
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 静态文件
        registry.addResourceHandler("/static/").addResourceLocations("classpath:/static/");
        // swagger
        registry.addResourceHandler("swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
    }
}


