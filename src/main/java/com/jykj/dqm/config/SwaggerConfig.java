package com.jykj.dqm.config;

import com.google.common.base.Function;
import com.google.common.base.Predicate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

import java.util.Optional;

/**
 * Swagger2配置类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Configuration
@EnableSwagger2WebMvc
public class SwaggerConfig {
    //定义分隔符,配置Swagger多包
    private static final String splitor = ";";

    /**
     * Swagger2配置类
     *
     * @return Docket对象
     * <AUTHOR>
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(true)
                .groupName("数据质量管理")
                .select()
                .apis(basePackage("com.jykj.dqm.auth;" +
                        "com.jykj.dqm.homepage.controller;" +
                        "com.jykj.dqm.metadata;" +
                        "com.jykj.dqm.quality;" +
                        "com.jykj.dqm.sso;" +
                        "com.jykj.dqm.system"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    @Bean(value = "EmrApi")
    public Docket emrApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(true)
                .groupName("电子病历评级文档管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.jykj.dqm.emr;" +
                        "com.jykj.dqm.homepage.controller;" +
                        "com.jykj.dqm.empiricalmaterial;"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("数据质量管理")
                .description("数据质量管理接口")
                .version("1.0")
                .contact(new Contact("佳缘科技", "", ""))
                .build();
    }

    public static Predicate<RequestHandler> basePackage(final String basePackage) {
        return input -> declaringClass(input).map(handlerPackage(basePackage)).orElse(true);
    }

    private static Function<Class<?>, Boolean> handlerPackage(final String basePackage) {
        return input -> {
            // 循环判断匹配
            for (String strPackage : basePackage.split(splitor)) {
                boolean isMatch = input.getPackage().getName().startsWith(strPackage);
                if (isMatch) {
                    return true;
                }
            }
            return false;
        };
    }

    private static Optional<Class<?>> declaringClass(RequestHandler input) {
        return Optional.ofNullable(input.declaringClass());
    }
}