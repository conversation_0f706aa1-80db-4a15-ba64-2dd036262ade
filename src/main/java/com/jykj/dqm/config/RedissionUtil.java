package com.jykj.dqm.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 分布式锁初始化
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/4/1 14:09
 */
@Configuration
public class RedissionUtil {
    @Autowired
    private RedisProperties redisProperties;

    @Bean
    public RedissonClient redissionClient() {
        Config config = new Config();
        String redisUrl = String.format("redis://%s:%s", redisProperties.getHost() + "", redisProperties.getPort() + "");

        //单机模式
        config.useSingleServer()
                .setAddress(redisUrl)
                .setPassword(redisProperties.getPassword())
                .setDatabase(redisProperties.getDatabase());

        //集群模式
        /*config.useClusterServers()
                .addNodeAddress("redis://**************:36379")
                .addNodeAddress("redis://**************:36379")
                .addNodeAddress("redis://**************:36379")
                .setPassword("1111111")
                .setScanInterval(5000);*/

        //哨兵模式
        /*config.useSentinelServers().addSentinelAddress("redis://ip1:port1",
                "redis://ip2:port2",
                "redis://ip3:port3")
                .setMasterName("mymaster")
                .setPassword("password")
                .setDatabase(0);*/

        RedissonClient redissonClient = Redisson.create(config);
        return redissonClient;
    }


}
