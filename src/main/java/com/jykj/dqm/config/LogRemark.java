package com.jykj.dqm.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 日志注解
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface LogRemark {
    /**
     * 设置操作
     *
     * @return 操作
     */
    String operate() default "";

    /**
     * 设置模块
     *
     * @return 模块
     */
    String module() default "";
}
