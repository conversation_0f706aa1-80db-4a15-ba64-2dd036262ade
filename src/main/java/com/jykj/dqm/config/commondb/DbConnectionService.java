package com.jykj.dqm.config.commondb;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.utils.SystemUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.net.URL;
import java.net.URLClassLoader;
import java.sql.Connection;
import java.sql.Driver;
import java.sql.DriverManager;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 创建数据库连接
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/22 17:57:19
 */
@Slf4j
@Component
public class DbConnectionService {
    private String url;
    private String username;
    private String password;
    private String sourceJarPath;
    private String databaseDriver;
    private static Map<String, URLClassLoader> loadMap = new ConcurrentHashMap<String, URLClassLoader>();

    public DbConnectionService() {

    }

    /*
       DbConnectionService  和 public Connection getConnection() 两个作为一个方法使用
       或者
       public Connection getConnection(String url, String username, String password, String sourceJarPath, String databaseDriver)
     */
    public DbConnectionService(String url, String username, String password, String sourceJarPath, String databaseDriver) {
        this.url = url;
        this.username = username;
        this.password = password;
        this.sourceJarPath = sourceJarPath;
        this.databaseDriver = databaseDriver;
    }

    public Connection getConnection() throws Exception {
        //在JDBC工具类中加载驱动
        File file = null;
        if (StrUtil.isNotBlank(sourceJarPath)) {
            String sourceJarPath = SystemUtils.getFilePath() + "/" + this.sourceJarPath;
            file = new File(sourceJarPath);
        }
        Connection conn = null;
        //支持本地的jar JDBC驱动
        if (file == null || !file.exists() || file.isDirectory()) {
            //指定目录不存对应JDBC驱动，就试图使用应用下的JDBC
            Class.forName(this.databaseDriver);
            conn = DriverManager.getConnection(url, username, password);
        } else {
            //查看之前是否有加载
            if (!loadMap.containsKey(sourceJarPath)) {
                URLClassLoader loader = new URLClassLoader(new URL[]{file.toURI().toURL()}, null);
                loader.clearAssertionStatus();
                loadMap.put(sourceJarPath, loader);
            }
            Driver driver = (Driver) loadMap.get(sourceJarPath).loadClass(databaseDriver).newInstance();
            Properties obj = new Properties();
            obj.setProperty("user", username);
            obj.setProperty("password", password);
            conn = driver.connect(url, obj);
        }
        return conn;
    }

    public Connection getConnection(String url, String username, String password, String sourceJarPath, String databaseDriver) throws Exception {
        //在JDBC工具类中加载驱动
        File file = null;
        if (StrUtil.isNotBlank(sourceJarPath)) {
            String sourceJarPath2 = SystemUtils.getFilePath() + "/" + sourceJarPath;
            file = new File(sourceJarPath2);
        }
        Connection conn = null;
        //支持本地的jar JDBC驱动
        if (file == null || !file.exists() || file.isDirectory()) {
            //指定目录不存对应JDBC驱动，就试图使用应用下的JDBC
            Class.forName(databaseDriver);
            if (StrUtil.isBlank(password)) {
                password = null;
            }
            conn = DriverManager.getConnection(url, username, password);
        } else {
            //查看之前是否有加载
            if (!loadMap.containsKey(sourceJarPath)) {
                URLClassLoader loader = new URLClassLoader(new URL[]{file.toURI().toURL()}, null);
                loader.clearAssertionStatus();
                loadMap.put(sourceJarPath, loader);
            }
            Driver driver = (Driver) loadMap.get(sourceJarPath).loadClass(databaseDriver).newInstance();
            Properties obj = new Properties();
            obj.setProperty("user", username);
            obj.setProperty("password", password);
            conn = driver.connect(url, obj);
        }
        return conn;
    }
}

