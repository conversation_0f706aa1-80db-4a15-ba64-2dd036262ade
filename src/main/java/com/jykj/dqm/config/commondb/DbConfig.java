package com.jykj.dqm.config.commondb;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * 数据库属性配置实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/4/23 9:17:58
 */
@Data
public class DbConfig {
    /**
     * 数据源ID
     */
    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;

    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称")
    private String dataSourceName;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String dataSourceDescribe;

    /**
     * 数据库类型
     */
    @ApiModelProperty(value = "数据库类型")
    private String databaseType;

    /**
     * 数据库驱动
     */
    @ApiModelProperty(value = "数据库驱动")
    private String databaseDriver;

    /**
     * 数据库访问URL
     */
    @ApiModelProperty(value = "数据库访问URL")
    private String databaseUrl;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String databaseUser;

    /**
     * 密码(加密后)
     */
    @ApiModelProperty(value = "密码(加密后)")
    private String databasePwd;

    /**
     * 采集数据库名称
     */
    @ApiModelProperty(value = "采集数据库名称")
    private String databaseName;

    /**
     * SCHEMA
     */
    @ApiModelProperty(value = "SCHEMA")
    private String databaseSchema;

    /**
     * 测试语句
     */
    @ApiModelProperty(value = "测试语句")
    private String testsql;

    /**
     * 驱动包路径/sourceJarPath
     */
    @ApiModelProperty(value = "驱动包路径/sourceJarPath")
    private String driverFiles;

    @Override
    public String toString() {
        return dataSourceName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DbConfig dbConfig = (DbConfig) o;
        return Objects.equals(dataSourceName, dbConfig.dataSourceName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dataSourceName);
    }

}

