package com.jykj.dqm.config.commondb;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericKeyedObjectPool;
import org.springframework.stereotype.Component;

import java.io.Closeable;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 自定义数据库连接池工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/4/23 9:18:28
 */
@Slf4j
@Component
public class DbConnectionPoolUtil implements Closeable {
    private static GenericKeyedObjectPool<DbConfig, Connection> internalPool;

    private static final Lock lock = new ReentrantLock();

    public DbConnectionPoolUtil() {
        //initPool();
    }

    /**
     * 初始化连接池
     */
    public static void initPool() {
        if (internalPool != null) {
            closeInternalPool();
        }
        // 初始化GenericKeyedObjectPool
        // 连接工厂类，可以创建连接、销毁连接等
        DbConnectionFactory dbConnectionFactory = new DbConnectionFactory();
        //池配置参数
        DbPoolConfig dbPoolConfig = new DbPoolConfig();
        internalPool = new GenericKeyedObjectPool<>(dbConnectionFactory, dbPoolConfig);
    }

    /**
     * 获取连接
     * 首次获取连接时，如果池为空则初始化池，同时factory创建一个连接放入池中
     *
     * @param key 连接池Key，根据不同key获取对应的连接
     * @return java.sql.Connection
     */
    public static Connection getConnection(DbConfig key) {
        try {
            if (internalPool != null) {
                return internalPool.borrowObject(key);
            }
            lock.lock();
            try {
                //重新判断
                if (internalPool != null) {
                    return internalPool.borrowObject(key);
                }
                initPool();
                return internalPool.borrowObject(key);
            } finally {
                lock.unlock();
            }
        } catch (Exception e) {
            throw new RuntimeException(key.getDatabaseUrl() + ":Could not get connection from the pool" + e.getMessage(), e);
        }
    }

    /**
     * 关闭连接
     *
     * @param key 连接池Key
     */
    public static void closeOnePool(DbConfig key) {
        internalPool.clear(key);
    }

    /**
     * 返还连接
     *
     * @param key        连接池Key
     * @param connection 需返还的连接
     */
    public static void returnConnection(DbConfig key, Connection connection) {
        if (connection != null) {
            try {
                internalPool.returnObject(key, connection);
                log.info(key.getDatabaseUrl() + "数据库连接归还成功！");
            } catch (Exception e) {
                log.error(key.getDatabaseUrl() + ":Could not return the connection to the pool" + e.getMessage(), e);
                try {
                    connection.close();
                } catch (SQLException ex) {
                    log.error(key.getDatabaseUrl() + ":Could not close the connection" + e.getMessage(), e);
                }
            }
        }
    }

    @Override
    public void close() {
        closeInternalPool();
    }

    private static void closeInternalPool() {
        try {
            internalPool.close();
        } catch (Exception e) {
            throw new RuntimeException("Could not destroy the pool", e);
        }
    }

}

