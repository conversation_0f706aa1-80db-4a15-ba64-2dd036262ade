package com.jykj.dqm.config.schedule;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 继承Application接口后项目启动时会按照执行顺序执行run方法
 * 通过设置Order的value来指定执行的顺序
 */
@Component
@Order(value = 1)
public class StartService implements ApplicationRunner {
    @Autowired
    private MyTimerJob myTimerJob;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        //不传时间，使用数据库配置表中的时间，或者默认值24小时
        myTimerJob.start("");
    }
}