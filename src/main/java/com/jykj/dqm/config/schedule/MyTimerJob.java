package com.jykj.dqm.config.schedule;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.system.service.DictManagementService;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.support.PeriodicTrigger;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.concurrent.ScheduledFuture;

@Slf4j
@Data
@Configuration
@EnableScheduling
//@PropertySource("classpath:/task-config.ini")
public class MyTimerJob implements SchedulerObjectInterface {
    private static final String myTimerJobKey = "MyTimerJob";

    //@Value("${scheduleTime.timer}")
    //默认24小时
    private long timer = 86400000;

    private ScheduledFuture future;

    @Autowired
    private TaskScheduler scheduler;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private DictManagementService dictManagementService;

    @Override
    public void start(String time) {
        //判断是否开启同步功能
        SysConfig sysConfig = RedisUtil.getSysConfigByName(Constant.MDM_AUTO_SYNC_SWICTH);
        if (sysConfig == null || "N".equalsIgnoreCase(sysConfig.getConfigValue())) {
            return;
        }
        getTime(time);
        log.info("进入start方法");
        future = scheduler.schedule(
                () -> syncDictData(),
                triggerContext -> {
                    PeriodicTrigger periodicTrigger = new PeriodicTrigger(timer);
                    Date nextExecutionTime = periodicTrigger.nextExecutionTime(triggerContext);
                    return nextExecutionTime;
                });
    }

    private void getTime(String time) {
        if (StrUtil.isNotBlank(time)) {
            timer = Long.parseLong(time);
        } else {
            SysConfig config = RedisUtil.getSysConfigByName(Constant.MDM_AUTO_SYNC_TIME);
            if (config != null && StringUtil.isInteger(config.getConfigValue())) {
                timer = Long.parseLong(config.getConfigValue()) * 60 * 60 * 1000;
            }
        }
    }

    @Override
    public void update(String time) {
        stop();
        getTime(time);
        log.info("进入update方法");
        future = scheduler.schedule(
                () -> syncDictData(),
                triggerContext -> {
                    PeriodicTrigger periodicTrigger = new PeriodicTrigger(timer);
                    Date nextExecutionTime = periodicTrigger.nextExecutionTime(triggerContext);
                    return nextExecutionTime;
                });
    }

    @Override
    public void stop() {
        log.info("进入stop方法");
        if (future != null) {
            future.cancel(false);
        }
    }

    private void syncDictData() {
        boolean result = false;
        RLock lock = null;
        //获取分布式锁
        try {
            lock = redissonClient.getLock(myTimerJobKey);
            result = lock.tryLock();
            if (result) {
                //判断是否开启同步功能
                SysConfig sysConfig = RedisUtil.getSysConfigByName(Constant.MDM_AUTO_SYNC_SWICTH);
                if (sysConfig == null || "N".equalsIgnoreCase(sysConfig.getConfigValue())) {
                    return;
                }
                log.info("syncDictData start-------------->Current time： {}", LocalDateTime.now());
                dictManagementService.syncDictData();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            if (result) {
                lock.unlock();
            }
        }
    }
}