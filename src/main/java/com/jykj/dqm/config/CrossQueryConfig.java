package com.jykj.dqm.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 跨库查询配置
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
@ConfigurationProperties(prefix = "cross-query")
public class CrossQueryConfig {

    /**
     * 是否开启自动配置跨库catalog
     */
    private boolean autoCatalogConfig = false;

    /**
     * Presto Docker目录路径
     */
    private String prestoDockerPath = "/home/<USER>";

    /**
     * Presto环境密码
     */
    private String prestoEnvPassword = "Jykj1994@";

    public boolean isAutoCatalogConfig() {
        return autoCatalogConfig;
    }

    public void setAutoCatalogConfig(boolean autoCatalogConfig) {
        this.autoCatalogConfig = autoCatalogConfig;
    }

    public String getPrestoDockerPath() {
        return prestoDockerPath;
    }

    public void setPrestoDockerPath(String prestoDockerPath) {
        this.prestoDockerPath = prestoDockerPath;
    }

    public String getPrestoEnvPassword() {
        return prestoEnvPassword;
    }

    public void setPrestoEnvPassword(String prestoEnvPassword) {
        this.prestoEnvPassword = prestoEnvPassword;
    }
}