项目名称                                             病房医嘱处理
级别                                                5
项目字段                                             医嘱序号、医嘱项目编码、药疗医嘱给药途径、药疗医嘱剂量
字段表格表头字段（整合性：两个；及时性：一个，位置最后）        药疗医嘱记录表与字段名	护理执行记录表与字段名
数量表格表头字段（整合性：两个，位置中间）                   药疗医嘱记录数	        护理执行可关联对照记录数
统计开始时间
统计结束时间

情况说明：
整合性（5、7级，4级有一个特殊）：医嘱记录中有药疗医嘱 182524 条记录，其中有 225 条医嘱是医师下达的患者自备药品医嘱，没有医嘱代码。实际各个项目情况如下表：
一致性：主记录表中共有12561700条记录，包含医嘱项目编码,医嘱项目名称,详细情况如下：
完整性：病房医嘱处理记录中有12561700条记录，实际各个项目情况如下表：
及时性（6级）：只考察一个检验标本相关的时间逻辑关系，n=1
        实际数据情况：50000 条检验主记录（申请）相关联的标本记录、检验结果记录中，有 46000条满足时间逻辑关系（2500 条采集时间空缺，1500 条标本接收时间=报告时间）。

完整性：
      项目总记录数T  SQL （1条）
	  每项完整记录数N  SQL（多条）
及时性：
      考察记录时间项目总数量T1  SQL    （1条）
      数据记录内容符合逻辑关系时间项数量S1  SQL  （1条）
一致性：
      数据记录项的总记录数T1 （多条）
	  数据记录对应的项目中与字典内容一致的记录数C1（多条）
整合性：
      项目总记录数T SQL（1条）      空值（或空格值）作为不可匹配项处理。
      对照项可匹配数L SQL（多条）

公式：
    一致性系数= (C1/T1 + C2/T2 + C3/T3) /n
			 = (2620/5000 + 3893/5000 + 25980/26000) / 3
			 = (0.524 + 0.7786 + 0.99923) / 3
			 = 0.76727666666

    完整性系数 = (N1/T1 + N2/T2 + N3/T3 + N4/T4 +N5/T5)/n
			   = （0.99938+1+0.69777+0.82223+1）/5
			   = 0.903876

	整合性系数 = (L1/T1 + L2/T2 + L3/T3 + L4/T4)/n
               = （1+0.998767+1+0.998767）/ 4
               = 0.999384

    及时性系数 = S1/T1
			   = 46000/50000
			   = 0.92