package com.jykj.dqm.emr.dao;

import com.jykj.dqm.emr.entity.DataDictionaryDirectoryRuleConfiguration;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【DQM_EMR_DATA_DICTIONARY_DIRECTORY_RULE_CONFIGURATION(数据字典规则配置)】的数据库操作Mapper
* @createDate 2024-04-10 17:57:26
* @Entity com.jykj.dqm.emr.entity.DataDictionaryDirectoryRuleConfiguration
*/
@Mapper
public interface DataDictionaryDirectoryRuleConfigurationMapper extends BaseMapper<DataDictionaryDirectoryRuleConfiguration> {

}
