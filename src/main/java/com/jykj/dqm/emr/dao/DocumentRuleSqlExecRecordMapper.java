package com.jykj.dqm.emr.dao;

import com.jykj.dqm.emr.entity.DocumentRuleSqlExecRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【DQM_EMR_DOCUMENT_RULE_SQL_EXEC_RECORD(文档规则配置SQL执行结果数据)】的数据库操作Mapper
* @createDate 2024-02-28 10:17:01
* @Entity com.jykj.dqm.emr.entity.DocumentRuleSqlExecRecord
*/
@Mapper
public interface DocumentRuleSqlExecRecordMapper extends BaseMapper<DocumentRuleSqlExecRecord> {

}
