package com.jykj.dqm.emr.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.emr.entity.RequireProjectDictionary;
import com.jykj.dqm.emr.entity.RequireProjectDictionaryQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 要求项目字典
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/20 16:23:09
 */
@Mapper
public interface RequireProjectDictionaryMapper extends BaseMapper<RequireProjectDictionary> {
    List<RequireProjectDictionary> query(RequireProjectDictionaryQuery requireProjectDictionaryQuery);
}