package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文档导出记录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/23 14:47:11
 */
@ApiModel(description = "文档导出记录")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_EMR_DOCUMENT_EXPORT_RECORD")
public class DocumentExportRecord extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private String id;

    @TableField(value = "PROJECT_ID")
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 导出文档等级
     */
    @TableField(value = "EXPORT_DOCUMENT_LEVEL")
    @ApiModelProperty(value = "导出文档等级")
    private String exportDocumentLevel;

    /**
     * 导出文档模板个数
     */
    @TableField(value = "EXPORT_DOCUMENT_TEMPLATES_NUM")
    @ApiModelProperty(value = "导出文档模板个数")
    private Integer exportDocumentTemplatesNum;

    /**
     * 数据开始时间
     */
    @TableField(value = "DATA_START_TIME")
    @ApiModelProperty(value = "数据开始时间")
    private String dataStartTime;

    /**
     * 数据结束时间
     */
    @TableField(value = "DATA_END_TIME")
    @ApiModelProperty(value = "数据结束时间")
    private String dataEndTime;

    /**
     * 导出状态：1待生成、2生成中、3已完成
     */
    @TableField(value = "EXPORT_STATUS")
    @ApiModelProperty(value = "导出状态：1待生成、2生成中、3已完成")
    private String exportStatus = "1";

    /**
     * 定时导出时间
     */
    @TableField(value = "TIMED_EXPORT_TIME")
    @ApiModelProperty(value = "定时导出时间")
    private String timedExportTime;

    /**
     * 导出的文档类型 0：病历文档 1：基础数据 2：病历数据 3：质量数据，默认勾选病历文档
     */
    @TableField(value = "EXPORT_DOCUMENT_TYPE")
    @ApiModelProperty(value = "导出的文档类型 0：病历文档 1：基础数据 2：病历数据 3：质量数据，默认勾选病历文档，用逗号分隔")
    private String exportDocumentType = "0";

    private static final long serialVersionUID = 1L;
}