package com.jykj.dqm.emr.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 重新生成某个章节文档导出类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/27 11:13:45
 */
@Data
public class DocumentExporQueryDTO {
    /**
     * 文档导出记录ID
     */
    @ApiModelProperty(value = "文档导出记录ID")
    private String exportRecordId;

    /**
     * 目录名称
     */
    @ApiModelProperty(value = "目录名称")
    @NotBlank
    private String directoryName;

    /**
     * 目录编码
     */
    @ApiModelProperty(value = "目录编码")
    @NotBlank
    private String directoryCode;

    /**
     * 规则类型（1：一致性、2：完整性、3：整合性、4：及时性）
     */
    @NotBlank
    @ApiModelProperty(value = "规则类型（1：一致性、2：完整性、3：整合性、4：及时性），直接填汉字")
    private String emrRuleType;

    @ApiModelProperty(value = "problemDataRemarks1、problemDataRemarks2、problemDataRemarks3、problemDataRemarks4")
    private String type;
}
