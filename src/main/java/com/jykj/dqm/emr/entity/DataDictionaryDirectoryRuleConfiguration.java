package com.jykj.dqm.emr.entity;

import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* 数据字典规则配置
* @TableName DQM_EMR_DATA_DICTIONARY_DIRECTORY_RULE_CONFIGURATION
*/
@TableName(value = "DQM_EMR_DATA_DICTIONARY_DIRECTORY_RULE_CONFIGURATION")
@Data
public class DataDictionaryDirectoryRuleConfiguration implements Serializable {
    /**
    * 
    */
    @ApiModelProperty(value = "")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
    * 项目ID
    */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
    * 父ID
    */
    @ApiModelProperty(value = "父ID")
    private String parentId;

    /**
    * 目录编码
    */
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
    * 目录名称
    */
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
    * 数据源ID
    */
    @ApiModelProperty(value = "数据源ID")
    private String dataSourceId;

    /**
    * 数据SQL
    */
    @ApiModelProperty(value = "数据SQL")
    private String dataSql;

    /**
    * 关联类型（0：编辑SQL，1：选择数据及运算公式）
    */
    @ApiModelProperty(value = "关联类型（0：编辑SQL，1：选择数据及运算公式）")
    private String associatedType;

    /**
    * 关联级别
    */
    @ApiModelProperty(value = "关联级别")
    private String associationLevel;

    /**
    * 数据单位
    */
    @ApiModelProperty(value = "数据单位")
    private String dataUnit;

    /**
    * 目录类型（1、基础数据，2、病历数据，3、质量数据）
    */
    @ApiModelProperty(value = "目录类型（1、基础数据，2、病历数据，3、质量数据）")
    private String directoryType;

    /**
    * 符合要求的记录数（数据质量字典使用）
    */
    @ApiModelProperty(value = "符合要求的记录数（数据质量字典使用）")
    private String conditionalDataSql;

    /**
    * 符合要求的记录关联类型（0：编辑SQL，1：选择数据及运算公式）数据质量字典使用
    */
    @ApiModelProperty(value = "符合要求的记录关联类型（0：编辑SQL，1：选择数据及运算公式）数据质量字典使用")
    private String conditionalAssociatedType;

    /**
    * 数据类型（0：基础；1：选择）
    */
    @ApiModelProperty(value = "数据类型（0：基础；1：选择）")
    private String dataType;

    /**
    * 符合要求的数据源ID
    */
    @ApiModelProperty(value = "符合要求的数据源ID")
    private String conditionalDataSourceId;

    /**
    * 是否跨库查询，0：否 ，1：是
    */
    @ApiModelProperty(value = "是否跨库查询，0：否 ，1：是")
    private String whetherCrossDbQuery;

    /**
    * 跨库查询数据源ID
    */
    @ApiModelProperty(value = "跨库查询数据源ID")
    private String crossDbQueryDataSourceId;

    //1：选择数据及运算公式
    @TableField(exist = false)
    @ApiModelProperty(value = "数据字典关联关系（分母）")
    private List<DataDictionaryAssociated> associatedList;

    @TableField(exist = false)
    @ApiModelProperty(value = "符合要求的数据字典关联关系（分子）")
    private List<DataDictionaryAssociated> conditionalAssociatedList;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态:0-未分配，1-已分配，2-已完成")
    @TableField(exist = false)
    private String taskStatus;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(exist = false)
    private String personInCharge;

}
