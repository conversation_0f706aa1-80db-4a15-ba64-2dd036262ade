package com.jykj.dqm.emr.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SQL语句测试查询
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 15:48:52
 */
@ApiModel(description = "SQL语句测试查询")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SqlStatementQuery {
    /**
     * 数据源ID
     */
    @ApiModelProperty(value = "数据源ID")
    private String dataSourceId;

    /**
     * SQL
     */
    @ApiModelProperty(value = "SQL")
    private String sql;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间偏移量（小时）：查询当前时间到当前时间减去偏移量的数据")
    private Integer offsetHour;

    /**
     * 分页参数 当前页
     */
    @ApiModelProperty(value = "分页参数 当前页")
    private Integer pageNum = 1;

    /**
     * 分页参数 每页数量
     */
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize = 10;
}