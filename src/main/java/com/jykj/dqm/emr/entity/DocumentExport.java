package com.jykj.dqm.emr.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 文档导出类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/27 11:13:45
 */
@Data
public class DocumentExport {
    /**
     * 导出文档等级
     */
    @NotBlank
    @ApiModelProperty(value = "导出文档等级")
    private String exportDocumentLevel;

    @NotBlank
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 数据开始时间
     */
    @NotBlank
    @ApiModelProperty(value = "数据开始时间")
    private String dataStartTime;

    /**
     * 数据结束时间
     */
    @NotBlank
    @ApiModelProperty(value = "数据结束时间")
    private String dataEndTime;

    /**
     * 定时导出时间
     */
    @ApiModelProperty(value = "定时导出时间")
    private String timedExportTime;

    /**
     * 导出的文档类型 0：病历文档 1：基础数据 2：病历数据 3：质量数据，默认勾选病历文档
     */
    @ApiModelProperty(value = "导出的文档类型 0：病历文档 1：基础数据 2：病历数据 3：质量数据，默认勾选病历文档，用逗号分隔")
    private String exportDocumentType = "0";
}
