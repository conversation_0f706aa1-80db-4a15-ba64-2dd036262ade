package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据字典目录配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/9/26 11:56:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_EMR_DATA_DICTIONARY_DIRECTORY_CONFIGURATION")
public class DataDictionaryDirectoryConfigurationExportVo extends DataDictionaryDirectoryConfiguration {
    /**
     * 实际总记录数
     */
    @ApiModelProperty(value = "实际总记录数")
    private Long allNum;

    /**
     * 符合要求的记录数
     */
    @ApiModelProperty(value = "符合要求的记录数")
    private Long conditionalNum;

    /**
     * 数据类型（0：基础；1：选择）
     */
    @ApiModelProperty(value = "数据类型（0：基础；1：选择）")
    private String dataType;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String failureReason;

    /**
     * 执行状态，0成功，1失败
     */
    @ApiModelProperty(value = "执行状态，0成功，1失败")
    private String execStatus = "0";

    @ApiModelProperty(value = "数据质量指数")
    private String dataQualityIndex;
}