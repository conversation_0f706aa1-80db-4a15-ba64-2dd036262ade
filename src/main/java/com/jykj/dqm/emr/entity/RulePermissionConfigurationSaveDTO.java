package com.jykj.dqm.emr.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 规则配置权限
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/7/4 16:20:48
 */
@ApiModel(description = "规则配置权限")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RulePermissionConfigurationSaveDTO {
    /**
     * 用户账号
     */
    @ApiModelProperty(value = "用户账号")
    @NotBlank
    private String userAccount;

    /**
     * 配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据
     */
    @ApiModelProperty(value = "配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据")
    @NotBlank
    private String configType;

    @ApiModelProperty(value = "项目ID")
    @NotBlank
    private String projectId;

    /**
     * 等级关联
     */
    @ApiModelProperty(value = "等级关联")
    private String levelCode;

    /**
     * 规则权限配置List
     */
    @ApiModelProperty(value = "规则权限配置List")
    List<RulePermissionConfiguration> rulePermissionList;
}