package com.jykj.dqm.emr.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 文档目录配置一级标题
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 15:48:52
 */
@ApiModel(description = "文档目录配置一级标题")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataDictionaryDirectoryFirst {
    /**
     * 目录名称
     */
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serialNum;

    /**
     * 规则类型（1：一致性、2：完整性、3：整合性、4：及时性）
     */
    @ApiModelProperty(value = "规则类型（1：一致性、2：完整性、3：整合性、4：及时性），直接填汉字")
    private String emrRuleType;

    @ApiModelProperty(value = "二级标题list")
    private List<DataDictionaryDirectorySecond> secondLevels;
}