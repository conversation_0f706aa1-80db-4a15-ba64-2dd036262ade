package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 数据评分数据量记录
 *
 * @TableName DQM_EMR_DATA_SCORE_NUM_RECORD
 */
@TableName(value = "DQM_EMR_DATA_SCORE_NUM_RECORD")
@Data
@Accessors(chain = true)
public class DataScoreNumRecord implements Serializable {
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer id;

    /**
     * DQM_EMR_DATA_DICTIONARY_DIRECTORY_CONFIGURATION表ID
     */
    @ApiModelProperty(value = "DQM_EMR_DATA_DICTIONARY_DIRECTORY_CONFIGURATION表ID")
    private String configurationId;

    /**
     * DQM_EMR_DOCUMENT_EXPORT_RECORD表ID
     */
    @ApiModelProperty(value = "DQM_EMR_DOCUMENT_EXPORT_RECORD表ID")
    private String exportRecordId;

    /**
     * 实际总记录数
     */
    @ApiModelProperty(value = "实际总记录数")
    private Long allNum;

    /**
     * 总记录数的数据类型（0、关联；1、SQL）
     */
    @ApiModelProperty(value = "总记录数的数据类型（（0：编辑SQL，1：选择数据及运算公式）数据质量字典使用）")
    private String allNumType;

    /**
     * 符合要求的记录数
     */
    @ApiModelProperty(value = "符合要求的记录数")
    private Long conditionalNum;

    /**
     * 符合要求的记录数的数据类型（0、关联；1、SQL）
     */
    @ApiModelProperty(value = "符合要求的记录数的数据类型（0：编辑SQL，1：选择数据及运算公式）数据质量字典使用")
    private String conditionalNumType;

    /**
     * 数据类型（0：基础；1：选择）
     */
    @ApiModelProperty(value = "数据类型（0：基础；1：选择）")
    private String dataType;

    /**
     * 目录类型（1、基础数据，2、病历数据，3、质量数据）
     */
    @ApiModelProperty(value = "目录类型（1、基础数据，2、病历数据，3、质量数据）")
    private String directoryType;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String failureReason;

    /**
     * 执行状态，0成功，1失败
     */
    @ApiModelProperty(value = "执行状态，0成功，1失败")
    private String execStatus = "0";

}
