package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文档规则配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/22 15:50:15
 */
@ApiModel(description = "文档规则配置Query")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocumentRuleConfigurationQuery {
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 规则类型
     */
    @TableField(value = "EMR_RULE_TYPE")
    @ApiModelProperty(value = "规则类型")
    private String emrRuleType;

    /**
     * 配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据
     */
    @ApiModelProperty(value = "配置类型（标记完成接口使用次参数）：0-文档字典，1-基础数据，2-病历数据，3-质量数据")
    private String configType = "0";

    /**
     * 用户账号（标记完成接口使用此参数）
     */
    @ApiModelProperty(value = "用户账号（标记完成接口使用此参数）")
    private String userAccount;
}