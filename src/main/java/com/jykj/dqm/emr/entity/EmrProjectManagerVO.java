package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.auth.entity.EmrSysUserGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 项目管理VO
 *
 * @TableName DQM_EMR_PROJECT_MANAGER
 */
@TableName(value = "DQM_EMR_PROJECT_MANAGER")
@Data
public class EmrProjectManagerVO extends EmrProjectManager {
    @ApiModelProperty(value = "项目成员数量")
    private Integer memberCount;

    /**
     * 等级关联名称
     */
    @ApiModelProperty(value = "等级关联名称")
    private String levelName;

    @ApiModelProperty(value = "项目成员")
    private List<EmrSysUserGroup> members;

    @ApiModelProperty(value = "当前项目进度（质量文档|实证材料）")
    private String progress;
}
