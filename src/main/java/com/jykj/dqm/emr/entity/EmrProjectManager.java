package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 项目管理
 *
 * @TableName DQM_EMR_PROJECT_MANAGER
 */
@TableName(value = "DQM_EMR_PROJECT_MANAGER")
@Data
public class EmrProjectManager extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目类型不能为空")
    @ApiModelProperty(value = "项目类型：0-数据质量文档；1-实证材料管理")
    private String projectType;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 等级关联
     */
    @NotBlank(message = "等级关联不能为空")
    @ApiModelProperty(value = "等级关联")
    private String levelCode;

    /**
     * 数据开始时间
     */
    @ApiModelProperty(value = "数据开始时间")
    private String dataStartTime;

    /**
     * 数据结束时间
     */
    @ApiModelProperty(value = "数据结束时间")
    private String dataEndTime;

    /**
     * 负责人
     */
    @NotBlank(message = "负责人不能为空")
    @ApiModelProperty(value = "负责人")
    private String personInCharge;

    /**
     * 项目成员json
     */
    @ApiModelProperty(value = "项目成员json")
    private String projectMembers;

}
