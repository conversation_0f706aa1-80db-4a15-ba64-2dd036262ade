package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 规则配置权限
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/7/4 16:20:48
 */
@ApiModel(description = "规则配置权限")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_EMR_RULE_PERMISSION_CONFIGURATION")
public class RulePermissionConfiguration extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Integer id;

    /**
     * 用户账号
     */
    @TableField(value = "USER_ACCOUNT")
    @ApiModelProperty(value = "用户账号")
    private String userAccount;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 规则类型
     */
    @TableField(value = "EMR_RULE_TYPE")
    @ApiModelProperty(value = "规则类型")
    private String emrRuleType;


    /**
     * 任务状态:0-未分配，1-已分配，2-已完成
     */
    @TableField(value = "TASK_STATUS")
    @ApiModelProperty(value = "任务状态:0-未分配，1-已分配，2-已完成")
    private String taskStatus;

    /**
     * 配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据
     */
    @TableField(value = "CONFIG_TYPE")
    @ApiModelProperty(value = "配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据")
    private String configType;

    /**
     * 项目ID
     */
    @TableField(value = "PROJECT_ID")
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 等级
     */
    @TableField(value = "LEVEL_CODE")
    @ApiModelProperty(value = "等级")
    private String levelCode;


    private static final long serialVersionUID = 1L;
}