package com.jykj.dqm.emr.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目管理
 *
 * @TableName DQM_EMR_PROJECT_MANAGER
 */
@Data
public class EmrProjectManagerQuery {
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目类型：0-数据质量文档；1-实证材料管理")
    private String projectType;

    /**
     * 是否是我参加的项目
     */
    @ApiModelProperty(value = "是否是我参加的项目")
    private boolean myProject = false;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 等级关联
     */
    @ApiModelProperty(value = "等级关联")
    private String levelCode;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String personInCharge;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

}
