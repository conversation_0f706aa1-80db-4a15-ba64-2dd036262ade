package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文档规则配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/22 15:50:15
 */
@ApiModel(description = "文档规则配置Query")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocumentRuleConfigurationQueryProject {
    ///**
    // * 目录编码
    // */
    //@TableField(value = "DIRECTORY_CODE")
    //@ApiModelProperty(value = "目录编码")
    //private String directoryCode;
    //
    ///**
    // * 目录名称
    // */
    //@TableField(value = "DIRECTORY_NAME")
    //@ApiModelProperty(value = "目录名称")
    //private String directoryName;
    //
    ///**
    // * 规则类型
    // */
    //@TableField(value = "EMR_RULE_TYPE")
    //@ApiModelProperty(value = "规则类型")
    //private String emrRuleType;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;
}