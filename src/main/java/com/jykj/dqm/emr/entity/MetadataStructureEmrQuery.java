package com.jykj.dqm.emr.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 元数据结构详情
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/29 16:17:20
 */
@ApiModel(value = "元数据结构(表/视图)详情查询")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MetadataStructureEmrQuery {
    /**
     * 数据源ID
     */
    @NotBlank
    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;

    /**
     * 表名或视图名
     */
    @ApiModelProperty(value = "表名或视图名")
    private String structureName;

}