package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 等级字典
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 9:55:52
 */
@ApiModel(description = "等级字典query")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LevelDictionaryQuery extends MyPageInfo {
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    private static final long serialVersionUID = 1L;
}