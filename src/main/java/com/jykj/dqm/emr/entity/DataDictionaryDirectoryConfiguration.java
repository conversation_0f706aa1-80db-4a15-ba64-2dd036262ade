package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据字典目录配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/9/26 11:56:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_EMR_DATA_DICTIONARY_DIRECTORY_CONFIGURATION")
public class DataDictionaryDirectoryConfiguration {
    //为了共用字典的一二级，特殊处理
    @TableId(value = "ID", type = IdType.INPUT)
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 父ID
     */
    @TableField(value = "PARENT_ID")
    @ApiModelProperty(value = "父ID")
    private String parentId;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 关联级别
     */
    @TableField(value = "ASSOCIATION_LEVEL")
    @ApiModelProperty(value = "关联级别")
    private String associationLevel;

    /**
     * 数据单位
     */
    @TableField(value = "DATA_UNIT")
    @ApiModelProperty(value = "数据单位")
    private String dataUnit;

    /**
     * 数据类型（0：基础；1：选择）
     */
    @TableField(value = "DATA_TYPE")
    @ApiModelProperty(value = "数据类型（0：基础；1：选择）")
    private String dataType;

    /**
     * 目录类型（1、基础数据，2、病历数据，3、质量数据）
     */
    @TableField(value = "DIRECTORY_TYPE")
    @ApiModelProperty(value = "目录类型（1、基础数据，2、病历数据，3、质量数据）")
    private String directoryType;

    /**
     * 关联类型（0：编辑SQL，1：选择数据及运算公式）
     */
    @TableField(value = "ASSOCIATED_TYPE")
    @ApiModelProperty(value = "关联类型（0：编辑SQL，1：选择数据及运算公式）")
    private String associatedType;

    /**
     * 符合要求的记录关联类型（0：编辑SQL，1：选择数据及运算公式）数据质量字典使用
     */
    @TableField(value = "CONDITIONAL_ASSOCIATED_TYPE")
    @ApiModelProperty(value = "符合要求的记录关联类型（0：编辑SQL，1：选择数据及运算公式）数据质量字典使用")
    private String conditionalAssociatedType;

    /**
     * 是否跨库查询0：否 ，1：是
     */
    @TableField(value = "WHETHER_CROSS_DB_QUERY")
    @ApiModelProperty(value = "是否跨库查询 0：否 ，1：是")
    private String whetherCrossDbQuery = "0";

    /**
     * 跨库查询数据源ID
     */
    @TableField(value = "CROSS_DB_QUERY_DATA_SOURCE_ID")
    @ApiModelProperty(value = "跨库查询数据源ID")
    private String crossDbQueryDataSourceId;

    //0：编辑SQL
    /**
     * 数据源ID
     */
    @TableField(value = "DATA_SOURCE_ID")
    @ApiModelProperty(value = "数据源ID")
    private String dataSourceId;

    /**
     * 符合要求的数据源ID
     */
    @TableField(value = "CONDITIONAL_DATA_SOURCE_ID")
    @ApiModelProperty(value = "符合要求的数据源ID")
    private String conditionalDataSourceId;

    /**
     * 数据SQL
     */
    @TableField(value = "DATA_SQL")
    @ApiModelProperty(value = "数据SQL")
    private String dataSql;

    /**
     * 符合要求的记录数（数据质量字典使用）
     */
    @TableField(value = "CONDITIONAL_DATA_SQL")
    @ApiModelProperty(value = "符合要求的记录数（数据质量字典使用）")
    private String conditionalDataSql;

    //1：选择数据及运算公式
    @TableField(exist = false)
    @ApiModelProperty(value = "数据字典关联关系（分母）")
    private List<DataDictionaryAssociated> associatedList;

    @TableField(exist = false)
    @ApiModelProperty(value = "符合要求的数据字典关联关系（分子）")
    private List<DataDictionaryAssociated> conditionalAssociatedList;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态:0-未分配，1-已分配，2-已完成")
    @TableField(exist = false)
    private String taskStatus;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(exist = false)
    private String personInCharge;
}