package com.jykj.dqm.emr.manager;

import cn.hutool.core.io.FileUtil;
import com.freewayso.image.combiner.ImageCombiner;
import com.freewayso.image.combiner.enums.OutputFormat;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.emr.utils.CommonUtils;
import com.jykj.dqm.quality.manager.rulesql.SqlUtils;
import com.jykj.dqm.utils.DateTimeUtil;
import lombok.SneakyThrows;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.Map;

/**
 * 图片处理
 * <p>
 * //加图片元素
 * //combiner.addImageElement("http://xxx.com/image/product.png", 0, 300);
 * //加文本元素
 * //combiner.addTextElement("周末大放送", 60, 100, 960);
 * //执行图片合并
 * //combiner.combine();
 * //可以获取流（并上传oss等）
 * //InputStream is = combiner.getCombinedImageStream();
 * //也可以保存到本地
 * //combiner.save("d://image.jpg");
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/4/3 13:49:04
 */
public class PictureUtils {
    public void combinePicture(String path, Map<String, String> params) {
        BufferedImage out = null;
        ImageCombiner combiner = null;
        try {
            out = ImageIO.read(new File(path.substring(0, path.lastIndexOf("/") + 1) + "picturetemplate.png"));
            combiner = new ImageCombiner(out, OutputFormat.PNG);
            String fontName = "微软雅黑";
            fontName = "阿里巴巴普惠体";
            //添加数据源
            combiner.addTextElement(params.get("dbName"), fontName, 18, 220, 252).setColor(new Color(98, 99, 101));
            //添加SQL
            //处理SQL跑到格子外
            String sql = params.get("sql");
            sql = SqlUtils.getSqlNormal(sql);
            StringBuilder stringBuilder = new StringBuilder();
            int length = 1400;
            for (String str : sql.split(Constant.LINE_SEPARATOR)) {
                if (str.length() > length) {
                    stringBuilder.append(CommonUtils.splitStringByLength(str, length));
                } else {
                    stringBuilder.append(str);
                }
                stringBuilder.append(Constant.LINE_SEPARATOR);
            }
            combiner.addTextElement(stringBuilder.toString(), fontName, 18, 220, 307).setAutoBreakLine("\n", 22).setColor(new Color(98, 99, 101));
            //添加SQL执行结果
            combiner.addTextElement(params.get("result"), fontName, 18, 214, 946).setColor(new Color(98, 99, 101));
            //右下角时间
            combiner.addTextElement(params.get("datetime").split(" ")[1], fontName, 12, 1806, 1042).setColor(Color.white);
            combiner.addTextElement(params.get("datetime").split(" ")[0], fontName, 12, 1799, 1061).setColor(Color.white);
            combiner.combine();
            combiner.save(path);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            combiner = null;
            out = null;
        }
    }

    @SneakyThrows
    public void combinePictureAddUserName(String path, Map<String, String> params) {
        //目前不需要添加用户信息，先直接复制图片到指定位置
        if (true) {
            //将Resource下的word/picturetemplate.png图片复制到指定位置path
            //FileUtil.copy("word/picturetemplate.png", path, true);

            InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("word/picturetemplate.png");
            Files.copy(resourceAsStream, Paths.get(path), StandardCopyOption.REPLACE_EXISTING);
            return;
        }
        BufferedImage out = null;
        ImageCombiner combiner = null;
        try {
            URL url = this.getClass().getClassLoader().getResource("word/picturetemplate.png");
            out = ImageIO.read(url);
            combiner = new ImageCombiner(out, OutputFormat.PNG);
            String fontName = "微软雅黑";
            fontName = "阿里巴巴普惠体";
            //添加用户信息
            combiner.addTextElement(params.get("exportUserName"), fontName, 15, 1807, 85).setColor(new Color(98, 99, 101));
            combiner.combine();
            combiner.save(path);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            combiner = null;
            out = null;
        }
    }

    public static void main(String[] args) throws Exception {
        PictureUtils pictureUtils = new PictureUtils();
        Map<String, String> params = new HashMap<>();
        params.put("dbName", "DQM");
        params.put("sql", "select count(*) from bl_cg_ip ip   inner join bd_srv srv on ip.id_srv=srv.id_srv where to_date(ip.createdtime,'yyyy-mm-dd hh24:mi:ss') BETWEEN to_date('2023-01-01','yyyy-MM-dd') AND to_date('2023-01-31','yyyy-MM-dd')");
        params.put("result", "1231");
        params.put("datetime", DateTimeUtil.getNowDateTimeStr());
        FileUtil.copy("word/picturetemplate.png", "C:/hjWork/elastic/", true);
        pictureUtils.combinePicture("C:/hjWork/elastic/111.png", params);
    }
}
