package com.jykj.dqm.emr.manager.generateword;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepoove.poi.data.Pictures;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DocumentRuleConfiguration;
import com.jykj.dqm.emr.entity.DocumentRuleSqlExecRecord;
import com.jykj.dqm.emr.manager.DbQueryNewUtil;
import com.jykj.dqm.emr.manager.WordUtils;
import com.jykj.dqm.emr.utils.PathNameUtils;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.utils.CircledNumberConverter;
import com.jykj.dqm.utils.SymmetricCryptoFactory;
import com.jykj.dqm.utils.DateTimeUtils;
import com.jykj.dqm.utils.NumberMyUtil;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.SystemUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 整合性
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/28 14:09:35
 */
@Slf4j
@Component
public class ZHXGenerateChapterWord extends GenerateChapterWord {
    @Autowired
    private DbQueryNewUtil dbQueryUtil;

    @Override
    public void dealEachDoc(List<DocumentRuleConfiguration> documentRuleConfigurations,
            DocumentDirectoryConfiguration documentDirectoryConfiguration, String dataStartTime, String dataEndTime,
            String recordId, DocumentDirectoryConfiguration secondDirectoryConfiguration) {
        if (documentRuleConfigurations.size() == 0) {
            return;
        }
        List<Map<String, Object>> table1Data = new ArrayList<>();
        // 标题行
        DocumentRuleConfiguration documentRuleConfiguration1 = documentRuleConfigurations.get(0);
        Map<String, Object> table1Map = new LinkedHashMap<>();
        boolean projectSystemTogether = whetherProjectSystemTogether(documentRuleConfigurations);
        if (projectSystemTogether) {
            table1Map.put("zero", "类别");
        }
        table1Map.put("one", "要求项目");
        table1Map.put("two", "医院项目");
        // 动态添加表头
        String headerName1 = documentRuleConfiguration1.getHeaderName1();
        String headerName2 = documentRuleConfiguration1.getHeaderName2();
        table1Map.put("three", headerName1 + "记录表与字段名");
        table1Map.put("four", headerName2 + "记录表与字段名");
        table1Data.add(table1Map);
        List<Map<String, Object>> table2Data = new ArrayList<>();
        // 标题行
        Map<String, Object> table2Map = new LinkedHashMap<>();
        table2Map.put("one", "序号");
        table2Map.put("two", "医院项目");
        // 动态添加表头
        table2Map.put("three", headerName1 + "记录数");
        table2Map.put("four", headerName2 + "可关联对照记录数");
        table2Map.put("five", "整合性比例I");
        table2Data.add(table2Map);
        // 文件名
        String docNameBase = null;
        // 带路径的文件名
        String docNamePathBase = null;
        List<Map<String, Object>> sqlData = new ArrayList();
        Map<String, Object> sqlmap;
        int countNum = 0;
        String recordsSql = "";
        String conditionalRecordsSql = "";
        long all = 0L;
        String picturePath;
        Map<String, Object> params = new LinkedHashMap<>();
        long match = 0L;
        String rate = "";
        String path = SystemUtils.getFilePath() + "/word/" + recordId + "/";
        String dbType = "";
        String dbType2 = "";
        String dataSourceId = "";
        String dataSourceId2 = "";
        List<DocumentRuleSqlExecRecord> documentRuleSqlExecRecords = new ArrayList<>();
        String configValue = RedisUtil.getSysConfigValue("emr.word.haspicture", "N");
        String system = "";
        String hospitalProject = "";
        Map<String, Object> sysMap;
        List<Map<String, Object>> sysData = new ArrayList();
        //documentRuleConfigurations按照documentRuleConfiguration.getHospitalProject()的系统进行分组
        Map<String, List<DocumentRuleConfiguration>> systemGroupMap = groupDataBySystem(documentRuleConfigurations, projectSystemTogether);
        int sysNum = 0;
        List<String> requiredProjects = new ArrayList<>();
        String requiredProject = "";
        String requiredProjectNormal;
        for (Map.Entry<String, List<DocumentRuleConfiguration>> stringListEntry : systemGroupMap.entrySet()) {
            sysNum++;
            system = stringListEntry.getKey();
            sysMap = new HashMap<>();
            countNum = 0;
            sqlData = new ArrayList();
            for (DocumentRuleConfiguration documentRuleConfiguration : stringListEntry.getValue()) {
                hospitalProject = documentRuleConfiguration.getHospitalProject();
                requiredProject = documentRuleConfiguration.getRequiredProject();
                requiredProjectNormal = requiredProject.replace(system + SYSTEM_SPLIT, "");
                table1Map = new LinkedHashMap<>();
                if (StrUtil.isNotBlank(system)) {
                    table1Map.put("zero", system);
                }
                table1Map.put("one", requiredProjectNormal);
                table1Map.put("two", hospitalProject.replace(system + SYSTEM_SPLIT, ""));
                table1Map.put("three", getTableAndFieldName(documentRuleConfiguration.getTableAndFiledType(),
                        documentRuleConfiguration.getStructureName1(), documentRuleConfiguration.getTableFieldName1()));
                table1Map.put("four",
                        "1".equals(documentRuleConfiguration.getNeedStatistics()) ? ""
                                : getTableAndFieldName(documentRuleConfiguration.getTableAndFiledType(),
                                documentRuleConfiguration.getStructureName2(),
                                documentRuleConfiguration.getTableFieldName2()));
                table1Data.add(table1Map);
                if (!"1".equals(documentRuleConfiguration.getNeedStatistics())) {
                    if ("1".equals(documentRuleConfiguration.getWhetherCrossDbQuery())
                            && StrUtil.isBlank(documentRuleConfiguration.getCrossDbQueryDataSourceId())) {
                        throw new BusinessException("未配置数据源！！！");
                    }
                    if ("0".equals(documentRuleConfiguration.getWhetherCrossDbQuery())
                            &&
                            (StrUtil.isBlank(documentRuleConfiguration.getDataSourceId())
                                    || StrUtil.isBlank(documentRuleConfiguration.getDataSourceId2()))) {
                        throw new BusinessException("未配置数据源！！！");
                    }
                    countNum++;
                    // 连接数据源查询结果
                    // DbQueryUtil dbQueryUtil = new DbQueryUtil();
                    // 根据dataSourceId查询数据源信息
                    dataSourceId = getDataSourceId(documentRuleConfiguration);
                    MetadataDatasource metadataDatasource = RedisUtil.getMetadataDatasourceById(dataSourceId);
                    metadataDatasource.setDatabasePwd(SymmetricCryptoFactory.decrypt(metadataDatasource.getDatabasePwd()));

                    // 第一次进入
                    dbType = metadataDatasource.getDatabaseType();
                    if (docNameBase == null) {
                        docNameBase = PathNameUtils.getFileName(documentRuleConfiguration.getDirectoryCode(),
                                documentRuleConfiguration.getDirectoryName(), documentRuleConfiguration.getEmrRuleType());
                        docNamePathBase = path + docNameBase;
                    }
                    recordsSql = addTimeToSql(documentRuleConfiguration.getRecordsSql(), dbType, dataStartTime,
                            dataEndTime);
                    all = dbQueryUtil.queryCount(metadataDatasource, recordsSql);
                    // 生成总的图片
                    if ("Y".equalsIgnoreCase(configValue)) {
                        picturePath = docNamePathBase + "All.png";
                        generatePicture(picturePath, recordsSql, metadataDatasource, all);
                        sysMap.put("pictureAll", Pictures.ofLocal(picturePath).create());
                    }
                    sysMap.put("showImage", "Y".equalsIgnoreCase(configValue));
                    dataSourceId2 = getDataSourceId2(documentRuleConfiguration);
                    MetadataDatasource metadataDatasource2 = RedisUtil.getMetadataDatasourceById(dataSourceId2);
                    metadataDatasource2
                            .setDatabasePwd(SymmetricCryptoFactory.decrypt(metadataDatasource2.getDatabasePwd()));
                    dbType2 = metadataDatasource2.getDatabaseType();

                    conditionalRecordsSql = addTimeToSql(documentRuleConfiguration.getConditionalRecordsSql(), dbType2,
                            dataStartTime, dataEndTime);
                    match = dbQueryUtil.queryCount(metadataDatasource2, conditionalRecordsSql);
                    rate = NumberMyUtil.calculationRatio(all, match);

                    buildSqlExecRecord(documentRuleConfiguration, (int) all, (int) match, documentRuleSqlExecRecords,
                            recordId);

                    table2Map = new LinkedHashMap<>();
                    table2Map.put("serialNum", countNum);
                    table2Map.put("one", hospitalProject);
                    table2Map.put("two", all);
                    table2Map.put("three", match);
                    table2Map.put("four", rate);
                    table2Data.add(table2Map);

                    if (!requiredProjects.contains(requiredProjectNormal)) {
                        requiredProjects.add(requiredProjectNormal);
                    }
                    // SQL
                    sqlmap = new LinkedHashMap();
                    sqlmap.put("name", "L" + countNum);
                    sqlmap.put("sql", conditionalRecordsSql);
                    sqlmap.put("sql2desc", documentRuleConfiguration.getConditionalRecordsSqlDesc());
                    sqlmap.put("showImage", "Y".equalsIgnoreCase(configValue));

                    if ("Y".equalsIgnoreCase(configValue)) {
                        picturePath = docNamePathBase + "L" + countNum + ".png";
                        generatePicture(picturePath, conditionalRecordsSql, metadataDatasource, match);
                        sqlmap.put("picture", Pictures.ofLocal(picturePath).create());
                    }
                    sqlData.add(sqlmap);
                }
                sysMap.put("sql1desc", documentRuleConfiguration.getRecordsSqlDesc());
            }
            if (projectSystemTogether) {
                sysMap.put("system", CircledNumberConverter.convertToCircledNumber(sysNum) + system);
            } else {
                sysMap.put("system", "");
            }
            sysMap.put("showImage", "Y".equalsIgnoreCase(configValue));
            sysMap.put("sqlData", sqlData);
            sysMap.put("dbType", dbType);
            sysMap.put("dbType2", dbType2);// 目前没改
            // 完整性特殊 T1-T6
            int size = stringListEntry.getValue().size();
            sysMap.put("tRange", size == 1 ? "T1" : "T1-T" + size);
            sysMap.put("sqlAll", recordsSql);
            sysData.add(sysMap);
        }
        // 对table2Data进行系统分组合并处理
        List<Map<String, Object>> mergedTable2Data = table2Data;
        if(projectSystemTogether){
            mergedTable2Data = mergeTable2DataBySystem(table2Data,params);
        } else {
            //后面拼接comment
            params.put("allRecord", all);
        }
        WordUtils wordUtils = new WordUtils();
        // 生成表1和表2
        wordUtils.generateTableWithCategoryMerge(table1Data, docNamePathBase + "table1");
        wordUtils.generateTableWithCategoryMerge(mergedTable2Data, docNamePathBase + "table2");

        params.put("sysData", sysData);

        // 其他数据（每个规则对应的名称，级别，时间范围，统计项目数 n（可以从data2的size-1获得，及时性为固定值1））
        int projectNum = mergedTable2Data.size() - 1;
        params.put("nNum", projectNum);
        params.put("dataStartTime", dataStartTime);// 转换为2022-04-01 yyyy-MM-dd
        params.put("dataEndTime", dataEndTime);// 转换为2022-04-01 yyyy-MM-dd
        params.put("levelCode", documentDirectoryConfiguration.getLevelCode());

        // 处理公式
        dealCalculationFormula(mergedTable2Data, params, projectNum, documentDirectoryConfiguration, recordId);

        params.put("table1", docNamePathBase + "table1");
        params.put("table2", docNamePathBase + "table2");
        params.put("directoryName", secondDirectoryConfiguration.getDirectoryName());
        params.put("allRequiredProject",  String.join("、", requiredProjects));
        params.put("headerName1", headerName1);
        params.put("headerName2", headerName2);

        params.put("monthNum", DateTimeUtils.getMonthBetween(dataStartTime, dataEndTime));
        // 增加标记，用于导出时，补充默认信息 private void updateDoc(List<DocumentExportRecordRuleDetail>
        // list, String filePath) throws IOException
        params.put("problemDataRemarks1", "{{problemDataRemarks1}}");
        params.put("problemDataRemarks2", "{{problemDataRemarks2}}");
        params.put("problemDataRemarks3", "{{problemDataRemarks3}}");
        params.put("problemDataRemarks4", "{{problemDataRemarks4}}");
        params.put("remarks1", "{{?remarks1}}");
        params.put("remarks1e", "{{/remarks1}}");
        params.put("remarks2", "{{?remarks2}}");
        params.put("remarks2e", "{{/remarks2}}");
        params.put("remarks3", "{{?remarks3}}");
        params.put("remarks3e", "{{/remarks3}}");
        wordUtils.generateWordForEachChapter(docNamePathBase + "_pre", RuleTypeEnum.ZHX.getValue(), params);
        // 删除是为了处理重新生成的场景
        documentRuleSqlExecRecordService.remove(Wrappers.<DocumentRuleSqlExecRecord>lambdaQuery()
                .eq(DocumentRuleSqlExecRecord::getExportRecordId, recordId)
                .eq(DocumentRuleSqlExecRecord::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
                .eq(DocumentRuleSqlExecRecord::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                .eq(DocumentRuleSqlExecRecord::getDirectoryName, documentDirectoryConfiguration.getDirectoryName()));
        documentRuleSqlExecRecordService.saveBatch(documentRuleSqlExecRecords);
    }

    /**
     * 对table2Data按项目类型进行分组处理
     * 按项目类型分组，显示所有系统的数据
     *
     * @param table2Data 原始table2数据
     * @param params
     * @return 处理后的table2数据
     */
    private List<Map<String, Object>> mergeTable2DataBySystem(List<Map<String, Object>> table2Data, Map<String, Object> params) {
        if (table2Data.size() <= 1) {
            return table2Data; // 只有标题行或无数据，直接返回
        }

        List<Map<String, Object>> processedData = new ArrayList<>();
        // 保留标题行
        processedData.add(table2Data.get(0));

        // 按项目类型分组数据
        Map<String, List<Map<String, Object>>> projectTypeGroupMap = new LinkedHashMap<>();
        Map<String, Long> totalSumMap = new LinkedHashMap<>(); // 所有系统的总数
        Map<String, Long> matchSumMap = new LinkedHashMap<>(); // 所有系统的匹配数

        // 从第二行开始处理（跳过标题行）
        for (int i = 1; i < table2Data.size(); i++) {
            Map<String, Object> row = table2Data.get(i);
            String hospitalProject = (String) row.get("one");

            // 提取项目类型（SYSTEM_SPLIT后的内容）
            String projectType = hospitalProject.substring(hospitalProject.indexOf(SYSTEM_SPLIT) + 1);

            // 获取数值，兼容不同的数据类型
            long totalCount = getLongValue(row.get("two"));
            long matchCount = getLongValue(row.get("three"));

            // 按项目类型分组
            if (!projectTypeGroupMap.containsKey(projectType)) {
                projectTypeGroupMap.put(projectType, new ArrayList<>());
                totalSumMap.put(projectType, 0L);
                matchSumMap.put(projectType, 0L);
            }

            projectTypeGroupMap.get(projectType).add(row);
            totalSumMap.put(projectType, totalSumMap.get(projectType) + totalCount);
            matchSumMap.put(projectType, matchSumMap.get(projectType) + matchCount);
        }

        // 生成合并后的数据行：在单元格内显示多行信息
        int serialNum = 1;
        for (Map.Entry<String, List<Map<String, Object>>> entry : projectTypeGroupMap.entrySet()) {
            String projectType = entry.getKey();
            List<Map<String, Object>> projectRows = entry.getValue();

            // 构建单元格内的多行内容
            StringBuilder totalCountContent = new StringBuilder();
            StringBuilder matchCountContent = new StringBuilder();

            // 添加各个系统的数据
            for (Map<String, Object> row : projectRows) {
                String hospitalProject = (String) row.get("one");
                String systemName = hospitalProject.substring(0, hospitalProject.indexOf(SYSTEM_SPLIT));
                long totalCount = getLongValue(row.get("two"));
                long matchCount = getLongValue(row.get("three"));

                if (totalCountContent.length() > 0) {
                    totalCountContent.append("\n");
                    matchCountContent.append("\n");
                }

                // 记录总数T列：系统名称 + 数量
                totalCountContent.append(systemName).append(" ").append(totalCount);
                // 有对照记录数C列：系统名称 + 数量
                matchCountContent.append(systemName).append(" ").append(matchCount);
            }

            // 添加合计行
            long projectTotal = totalSumMap.get(projectType);
            long projectMatch = matchSumMap.get(projectType);
            String rate = NumberMyUtil.calculationRatio(projectTotal, projectMatch);

            totalCountContent.append("\n合计 ").append(projectTotal);
            matchCountContent.append("\n合计 ").append(projectMatch);

            if (ObjectUtil.isNull(params.get("allRecord"))) {
                params.put("allRecord", projectTotal);
            }

            // 创建合并后的行
            Map<String, Object> mergedRow = new LinkedHashMap<>();
            mergedRow.put("serialNum", serialNum++);
            mergedRow.put("one", projectType); // 医院项目列只显示项目类型
            mergedRow.put("two", totalCountContent.toString());
            mergedRow.put("three", matchCountContent.toString());
            mergedRow.put("four", rate);

            processedData.add(mergedRow);
        }

        return processedData;
    }

    private static void buildSqlExecRecord(DocumentRuleConfiguration documentRuleConfiguration, int all, int match,
            List<DocumentRuleSqlExecRecord> documentRuleSqlExecRecords, String recordId) {
        DocumentRuleSqlExecRecord documentRuleSqlExecRecord = new DocumentRuleSqlExecRecord();
        documentRuleSqlExecRecord.setExportRecordId(recordId);
        documentRuleSqlExecRecord.setEmrRuleType(documentRuleConfiguration.getEmrRuleType());
        documentRuleSqlExecRecord.setDirectoryCode(documentRuleConfiguration.getDirectoryCode());
        documentRuleSqlExecRecord.setDirectoryName(documentRuleConfiguration.getDirectoryName());
        documentRuleSqlExecRecord.setRequiredProject(documentRuleConfiguration.getRequiredProject());
        documentRuleSqlExecRecord.setRecordsNum(all);
        documentRuleSqlExecRecord.setConditionalRecordsNum(match);
        documentRuleSqlExecRecords.add(documentRuleSqlExecRecord);
    }

    private void dealCalculationFormula(List<Map<String, Object>> table2Data, Map<String, Object> params,
            int projectNum, DocumentDirectoryConfiguration documentDirectoryConfiguration, String recordId) {
        // 完整性系数 = ( N1/T1 + N2/T2 + N3/T3 + N4/T4 + N5/T5 + N6/T6 ) / 6
        // = ( 1.0 + 1.0 + 1.0 + 1.0 + 1.0 + 1.0 ) / 6
        // = 1.0
        StringBuilder calculationFormula1 = new StringBuilder();
        StringBuilder calculationFormula2 = new StringBuilder();
        calculationFormula1.append("(");
        calculationFormula2.append(projectNum > 1 ? "(" : "");
        double allCount = 0.0;
        double eachCountDouble;
        // 第一行是标题
        for (int i = 1; i <= projectNum; i++) {
            if (i != 1) {
                calculationFormula1.append("+");
                calculationFormula2.append("+");
            }
            String eachCount = (String) table2Data.get(i).get("four");
            eachCountDouble = Double.parseDouble(eachCount);
            allCount += eachCountDouble;
            calculationFormula1.append("L" + i).append("/").append("T" + i);
            calculationFormula2.append(eachCount);
        }

        calculationFormula1.append(")").append("/").append("n");
        calculationFormula2.append(projectNum > 1 ? ")" : "").append("/").append(projectNum);
        params.put("calculationFormula1", calculationFormula1.toString());
        params.put("calculationFormula2", calculationFormula2.toString());

        String ratio = NumberMyUtil.calculationRatio(allCount, projectNum);
        params.put("calculationFormulaResult", ratio);
        // 记录告警
        recordRedisExceptionInfo(recordId, documentDirectoryConfiguration.getDirectoryCode(),
                documentDirectoryConfiguration.getDirectoryName(), RuleTypeEnum.ZHX.getValue(), ratio);
    }

    @Override
    public RuleTypeEnum gainRuleRuleType() {
        return RuleTypeEnum.ZHX;
    }
}
