package com.jykj.dqm.emr.manager;

import com.jykj.dqm.config.commondb.DbConfig;
import com.jykj.dqm.config.commondb.DbConnectionPoolUtil;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.quality.manager.rulesql.SqlUtils;
import com.jykj.dqm.utils.SymmetricCryptoFactory;
import com.jykj.dqm.utils.MapperUtils;
import com.jykj.dqm.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库查询
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/16 17:19:33
 */
@Slf4j
@Component
public class DbQueryNewUtil {
    /**
     * 记得DbConnectionPool.returnConnection(dbConfig, conn);
     *
     * @param metadataDatasource MetadataDatasource
     * @return Connection
     */
    public Connection getConnection(MetadataDatasource metadataDatasource) {
        Connection conn = null;
        try {
            String asePassWord = metadataDatasource.getDatabasePwd();
            if (SymmetricCryptoFactory.isEncrypted(metadataDatasource.getDatabasePwd())) {
                asePassWord = SymmetricCryptoFactory.decrypt(asePassWord);
                metadataDatasource.setDatabasePwd(asePassWord);
            }
            DbConfig dbConfig = MapperUtils.INSTANCE.map(DbConfig.class, metadataDatasource);
            conn = DbConnectionPoolUtil.getConnection(dbConfig);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        return conn;
    }

    /**
     * 根据数据源，查询SQL，并返回结果
     *
     * @param metadataDatasource MetadataDatasource
     * <AUTHOR>
     */
    public List<Map<String, Object>> query(MetadataDatasource metadataDatasource, String querySql) {
        // 检查SQL，只支持查询操作
        SqlUtils.checkSql(querySql);
        Connection conn = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            conn = getConnection(metadataDatasource);
            statement = conn.createStatement();
            log.info("执行sql:{}", querySql);
            resultSet = statement.executeQuery(querySql);
            List<Map<String, Object>> data = convertResultSetToList(resultSet);
            return data;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbConfig dbConfig = MapperUtils.INSTANCE.map(DbConfig.class, metadataDatasource);
            DbConnectionPoolUtil.returnConnection(dbConfig, conn);
        }
    }

    /**
     * 根据数据源，查询SQL，并返回结果
     *
     * @param metadataDatasource MetadataDatasource
     * <AUTHOR>
     */
    public List<Map<String, Object>> queryLimit(MetadataDatasource metadataDatasource, String querySql, int limit) {
        // 检查SQL，只支持查询操作
        SqlUtils.checkSql(querySql);
        Connection conn = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            conn = getConnection(metadataDatasource);
            statement = conn.createStatement();
            log.info("执行sql:{}", querySql);
            resultSet = statement.executeQuery(querySql);
            List<Map<String, Object>> data = convertResultSetToList(resultSet, limit);
            return data;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            DbConfig dbConfig = MapperUtils.INSTANCE.map(DbConfig.class, metadataDatasource);
            DbConnectionPoolUtil.returnConnection(dbConfig, conn);
        }
    }

    /**
     * 根据数据源，查询Count函数 SQL，并返回Long结果
     *
     * @param metadataDatasource MetadataDatasource
     * @return Count函数, Long结果
     * <AUTHOR>
     */
    public Long queryCount(MetadataDatasource metadataDatasource, String querySql) {
        List<Map<String, Object>> query = query(metadataDatasource, querySql);
        if (query.size() == 0) {
            return 0L;
        }
        ArrayList<Object> values = new ArrayList<>(query.get(0).values());
        if (values.size() == 0) {
            return 0L;
        }
        String count = values.get(0) + "";
        if (!StringUtil.isInteger(count)) {
            return 0L;
        }
        return Long.parseLong(count);
    }

    private List<Map<String, Object>> convertResultSetToList(ResultSet resultSet) throws SQLException {
        if (null == resultSet) {
            return null;
        }
        List<Map<String, Object>> data = new ArrayList<>();
        ResultSetMetaData rsmd = resultSet.getMetaData();
        while (resultSet.next()) {
            Map<String, Object> rowData = new HashMap<String, Object>();
            for (int i = 0, columnCount = rsmd.getColumnCount(); i < columnCount; i++) {
                rowData.put(rsmd.getColumnName(i + 1), resultSet.getObject(i + 1));
            }
            data.add(rowData);
        }
        return data;
    }

    private List<Map<String, Object>> convertResultSetToList(ResultSet resultSet, int limit) throws SQLException {
        if (null == resultSet) {
            return null;
        }
        List<Map<String, Object>> data = new ArrayList<>();
        ResultSetMetaData rsmd = resultSet.getMetaData();
        int count = 0;
        while (resultSet.next() && count <= limit) {
            Map<String, Object> rowData = new HashMap<String, Object>();
            for (int i = 0, columnCount = rsmd.getColumnCount(); i < columnCount; i++) {
                rowData.put(rsmd.getColumnName(i + 1), resultSet.getObject(i + 1));
            }
            count++;
            data.add(rowData);
        }
        return data;
    }

}
