package com.jykj.dqm.emr.config;

import cn.hutool.core.util.ObjectUtil;
import com.deepoove.poi.policy.DynamicTableRenderPolicy;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.emr.utils.CommonUtils;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * 自定义动态表格,不用合并单元格
 * 重写render方法
 */
@Deprecated
public class DetailTableNoMergePolicy extends DynamicTableRenderPolicy {
    @Override
    public void render(XWPFTable table, Object data) throws Exception {
        if (null == data) return;
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        tblPr.getTblW().setType(STTblWidth.DXA);
        //8503DXA≈15CM 9735  DXA≈17.17CM  1厘米≈567 DXA
        //650像素 17.17cm   17.17/650=0.0264
        int tableWidth = 9735;
        tblPr.getTblW().setW(BigInteger.valueOf(tableWidth));
        List<Map<String, Object>> targetRowData = (List<Map<String, Object>>) data;

        int oneRowLength = 360;
        if (ObjectUtil.isNotEmpty(targetRowData)) {
            int rowCount = targetRowData.get(0).size();
            int rowLength;
            if (rowCount == 4) {
                rowLength = 26;
            } else if (rowCount == 3) {
                rowLength = 41;
            } else {
                rowLength = 16;
            }
            table.removeRow(0);
            //循环插入行数据
            for (int i = 0; i < targetRowData.size(); i++) {
                //第一行是标题行
                XWPFTableRow xwpfTableRow = table.insertNewTableRow(i);
                //循环列 row-cell
                int row = 0;
                for (Map.Entry vo : targetRowData.get(i).entrySet()) {
                    XWPFTableCell cell = xwpfTableRow.createCell();
                    CTTcPr tcpr = cell.getCTTc().addNewTcPr();
                    CTTblWidth cellw = tcpr.addNewTcW();
                    if (rowCount == 3) {
                        if (row < 2) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(2835)); //5*567
                        } else {
                            // 计算剩余宽度
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth).subtract(BigInteger.valueOf(2835 * 2)); // 减去前两个单元格的宽度
                            // 如果是3个单元格，第三个单元格占满剩余宽度
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth);
                        }
                    } else if (rowCount == 4) {
                        if (row == 0) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(1570)); //2.77*567
                        } else if (row == 1) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(1815)); //3.2*567
                        } else {
                            // 计算剩余宽度
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth).subtract(BigInteger.valueOf(1570+1815)); // 减去前两个单元格的宽度
                            // 如果是4个单元格，后面的两个单元格平分剩余宽度
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth.divide(BigInteger.valueOf(2))); // 平分剩余的宽度
                        }
                    } else if (rowCount >= 5) {
                        if (row == 0) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(oneRowLength * 2));
                        } else if (row == 1) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(oneRowLength * 7));
                        } else {
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth).subtract(BigInteger.valueOf(oneRowLength * 9)); // 减去前两个单元格的宽度
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth.divide(BigInteger.valueOf(rowCount - 2)));
                        }
                    }

                    XWPFParagraph p = cell.getParagraphs().get(0);
                    // 文字靠左位置
                    p.setAlignment(ParagraphAlignment.LEFT);
                    // create a run
                    XWPFRun r = p.createRun();
                    r.setFontFamily("宋体");
                    r.setFontSize(11);
                    //单元格赋值
                    //手动换行
                    String string = CommonUtils.splitStringByLength(vo.getValue().toString(), rowLength);
                    String[] split = new String[]{string};
                    if (string.contains(Constant.LINE_SEPARATOR)) {
                        split = string.split(Constant.LINE_SEPARATOR);
                    } else if (string.contains("\n")) {
                        split = string.split("\n");
                    }
                    for (int j = 0; j < split.length; j++) {
                        if (j > 0) {
                            r.addBreak();
                        }
                        r.setText(split[j]);
                        //r.addCarriageReturn();
                    }
                    if (i == 0) {
                        // set font to bold
                        r.setBold(true);
                    }
                    row++;
                }
            }
        }
    }
}