package com.jykj.dqm.emr.config;

import cn.hutool.core.util.ObjectUtil;
import com.deepoove.poi.policy.DynamicTableRenderPolicy;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.emr.utils.CommonUtils;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STVerticalJc;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 自定义动态表格，支持类别列合并单元格
 * 专门用于table1的类别列合并
 *
 * <AUTHOR>
 */
public class DetailTableCategoryMergePolicy extends DynamicTableRenderPolicy {

    @Override
    public void render(XWPFTable table, Object data) throws Exception {
        if (null == data) return;

        CTTblPr tblPr = table.getCTTbl().getTblPr();
        tblPr.getTblW().setType(STTblWidth.DXA);
        //8503DXA≈15CM 9735  DXA≈17.17CM  1厘米≈567 DXA
        //650像素 17.17cm   17.17/650=0.0264
        int tableWidth = 9735;
        tblPr.getTblW().setW(BigInteger.valueOf(tableWidth));

        List<Map<String, Object>> targetRowData = (List<Map<String, Object>>) data;
        int oneRowLength = 360;
        //数据质量分析
        boolean qualityAnalysis = false;
        boolean hasRemark = false;
        if (ObjectUtil.isNotEmpty(targetRowData)) {
            int rowCount = targetRowData.get(0).size();
            if(Objects.equals(targetRowData.get(0).get("remark"),"备注")){
                hasRemark = true;
            }
            table.removeRow(0);

            // 用于记录类别列的值，以便后续合并
            List<String> categoryList = new ArrayList<>();

            // 循环插入行数据
            for (int i = 0; i < targetRowData.size(); i++) {
                // 第一行是标题行
                XWPFTableRow xwpfTableRow = table.insertNewTableRow(i);
                // 循环列 row-cell
                int colIndex = 0;
                for (Map.Entry<String, Object> vo : targetRowData.get(i).entrySet()) {
                    XWPFTableCell cell = xwpfTableRow.createCell();
                    CTTcPr tcpr = cell.getCTTc().addNewTcPr();
                    CTTblWidth cellw = tcpr.addNewTcW();

                    // 设置列宽
                    if (hasRemark && qualityAnalysis) {
                        if (colIndex == 0) {
                            // 类别列，较窄
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(986)); // 约1.74cm  *567
                        } else if (colIndex == 1 || colIndex == 2) {
                            // 要求项目列 和 医院项目列
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(1582)); // 约2.79cm
                        } else {
                            // 剩余两列平分剩余宽度
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth)
                                    .subtract(BigInteger.valueOf(986 + 1582 + 1582));
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth.divide(BigInteger.valueOf(2)));
                        }
                    } else if (hasRemark && !qualityAnalysis) {
                        if (colIndex == 0 || colIndex == 1) {
                            // 要求项目列 和 医院项目列
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(1582)); // 约2.79cm
                        } else {
                            // 剩余两列平分剩余宽度
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth)
                                    .subtract(BigInteger.valueOf(1582 + 1582));
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth.divide(BigInteger.valueOf(2)));
                        }
                    } else if (qualityAnalysis && rowCount == 5) {
                        // 5列表格的列宽设置：类别、要求项目、医院项目、数据库表与字段名、数据字典表与字段名
                        if (colIndex == 0) {
                            // 类别列，较窄
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(986)); // 约1.74cm  *567
                        } else if (colIndex == 1 || colIndex == 2) {
                            // 要求项目列 和 医院项目列
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(1582)); // 约2.79cm
                        } else {
                            // 剩余两列平分剩余宽度
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth)
                                    .subtract(BigInteger.valueOf(986 + 1582 + 1582));
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth.divide(BigInteger.valueOf(2)));
                        }
                    } else if (rowCount == 4) {
                        if (colIndex == 0) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(1570)); //2.77*567
                        } else if (colIndex == 1) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(1815)); //3.2*567
                        } else {
                            // 计算剩余宽度
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth).subtract(BigInteger.valueOf(1570+1815)); // 减去前两个单元格的宽度
                            // 如果是4个单元格，后面的两个单元格平分剩余宽度
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth.divide(BigInteger.valueOf(2))); // 平分剩余的宽度
                        }
                    } else if (rowCount == 3) {
                        if (colIndex < 2) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(2835)); //5*567
                        } else {
                            // 计算剩余宽度
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth).subtract(BigInteger.valueOf(2835 * 2)); // 减去前两个单元格的宽度
                            // 如果是3个单元格，第三个单元格占满剩余宽度
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth);
                        }
                    } else if (rowCount >= 5) {
                        //
                        if (colIndex == 0) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(oneRowLength * 2));
                        } else if (colIndex == 1) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(oneRowLength * 7));
                        } else {
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth).subtract(BigInteger.valueOf(oneRowLength * 9)); // 减去前两个单元格的宽度
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth.divide(BigInteger.valueOf(rowCount - 2)));
                        }
                    } else {
                        cellw.setType(STTblWidth.DXA);
                        cellw.setW(BigInteger.valueOf(tableWidth / rowCount));
                    }

                    // 设置单元格垂直居中对齐
                    if ((qualityAnalysis && colIndex < 3) || (!qualityAnalysis && colIndex < 2) || (!qualityAnalysis && rowCount >= 5)) {
                        // 前3列垂直居中
                        tcpr.addNewVAlign().setVal(STVerticalJc.CENTER);
                    }

                    XWPFParagraph p = cell.getParagraphs().get(0);
                    // 前3列水平居中对齐，其他列左对齐
                    if ((qualityAnalysis && colIndex < 3) || (!qualityAnalysis && colIndex < 2) || (!qualityAnalysis && rowCount >= 5)) {
                        p.setAlignment(ParagraphAlignment.CENTER);
                    } else {
                        p.setAlignment(ParagraphAlignment.LEFT);
                    }
                    // create a run
                    XWPFRun r = p.createRun();
                    r.setFontFamily("宋体");
                    r.setFontSize(11);

                    // 单元格赋值
                    // 根据列的实际宽度（DXA）自动计算字符长度限制，实现自动换行
                    BigInteger cellWidth = getCellWidth(colIndex, rowCount, hasRemark, qualityAnalysis, tableWidth);
                    int charLimit = calculateCharLimitByWidth(cellWidth);
                    String string = CommonUtils.splitStringByLength(vo.getValue().toString(), charLimit);
                    String[] split = new String[]{string};
                    if (string.contains(Constant.LINE_SEPARATOR)) {
                        split = string.split(Constant.LINE_SEPARATOR);
                    } else if (string.contains("\n")) {
                        split = string.split("\n");
                    }

                    for (int j = 0; j < split.length; j++) {
                        if (j > 0) {
                            r.addBreak();
                        }
                        r.setText(split[j]);
                        //r.addCarriageReturn();
                    }

                    if (i == 0) {
                        // 标题行设置为粗体
                        r.setBold(true);
                    } else {
                        // 记录类别列的值（第一列，colIndex == 0）
                        if (colIndex == 0) {
                            categoryList.add(vo.getValue().toString());
                        }
                    }
                    colIndex++;
                }
            }
            if(qualityAnalysis){
                // 合并类别列的单元格
                mergeCategoryColumn(table, categoryList);
            }
        }
    }

    /**
     * 获取指定列的宽度（DXA单位）
     *
     * @param colIndex 列索引
     * @param rowCount 列数
     * @param hasRemark 是否有备注列
     * @param qualityAnalysis 是否是数据质量分析表格
     * @param tableWidth 表格总宽度
     * @return 列宽度（DXA单位）
     */
    private BigInteger getCellWidth(int colIndex, int rowCount, boolean hasRemark, boolean qualityAnalysis, int tableWidth) {
        // 复用原有的列宽设置逻辑
        if (hasRemark && qualityAnalysis) {
            if (colIndex == 0) {
                return BigInteger.valueOf(986); // 类别列
            } else if (colIndex == 1 || colIndex == 2) {
                return BigInteger.valueOf(1582); // 要求项目列和医院项目列
            } else {
                // 剩余两列平分剩余宽度
                BigInteger remainingWidth = BigInteger.valueOf(tableWidth)
                        .subtract(BigInteger.valueOf(986 + 1582 + 1582));
                return remainingWidth.divide(BigInteger.valueOf(2));
            }
        } else if (hasRemark && !qualityAnalysis) {
            if (colIndex == 0 || colIndex == 1) {
                return BigInteger.valueOf(1582); // 要求项目列和医院项目列
            } else {
                // 剩余两列平分剩余宽度
                BigInteger remainingWidth = BigInteger.valueOf(tableWidth)
                        .subtract(BigInteger.valueOf(1582 + 1582));
                return remainingWidth.divide(BigInteger.valueOf(2));
            }
        } else if (qualityAnalysis && rowCount == 5) {
            if (colIndex == 0) {
                return BigInteger.valueOf(986); // 类别列
            } else if (colIndex == 1 || colIndex == 2) {
                return BigInteger.valueOf(1582); // 要求项目列和医院项目列
            } else {
                // 剩余两列平分剩余宽度
                BigInteger remainingWidth = BigInteger.valueOf(tableWidth)
                        .subtract(BigInteger.valueOf(986 + 1582 + 1582));
                return remainingWidth.divide(BigInteger.valueOf(2));
            }
        } else if (rowCount == 4) {
            if (colIndex == 0) {
                return BigInteger.valueOf(1570);
            } else if (colIndex == 1) {
                return BigInteger.valueOf(1815);
            } else {
                // 计算剩余宽度
                BigInteger remainingWidth = BigInteger.valueOf(tableWidth).subtract(BigInteger.valueOf(1570+1815));
                return remainingWidth.divide(BigInteger.valueOf(2));
            }
        } else if (rowCount == 3) {
            if (colIndex < 2) {
                return BigInteger.valueOf(2835);
            } else {
                // 计算剩余宽度
                return BigInteger.valueOf(tableWidth).subtract(BigInteger.valueOf(2835 * 2));
            }
        } else if (rowCount >= 5) {
            int oneRowLength = 360;
            if (colIndex == 0) {
                return BigInteger.valueOf(oneRowLength * 2);
            } else if (colIndex == 1) {
                return BigInteger.valueOf(oneRowLength * 7);
            } else {
                BigInteger remainingWidth = BigInteger.valueOf(tableWidth).subtract(BigInteger.valueOf(oneRowLength * 9));
                return remainingWidth.divide(BigInteger.valueOf(rowCount - 2));
            }
        } else {
            return BigInteger.valueOf(tableWidth / rowCount);
        }
    }

    /**
     * 根据列宽度（DXA单位）计算合适的字符长度限制
     *
     * @param cellWidth 列宽度（DXA单位）
     * @return 字符长度限制
     */
    private int calculateCharLimitByWidth(BigInteger cellWidth) {
        // DXA转换为厘米：1厘米≈567 DXA
        // 宋体11号字体，1个中文字符约占0.4厘米宽度
        double widthInCm = cellWidth.doubleValue() / 567.0;
        double charsPerCm = 2.5; // 每厘米约2.5个中文字符（宋体11号）
        int charLimit = (int) Math.floor(widthInCm * charsPerCm);

        // 设置最小和最大限制，避免过小或过大
        charLimit = Math.max(charLimit, 6);  // 最少6个字符
        charLimit = Math.min(charLimit, 50); // 最多50个字符

        return charLimit;
    }

    /**
     * 合并类别列的单元格（第一列）
     *
     * @param table        XWPFTable
     * @param categoryList 类别列表
     */
    private void mergeCategoryColumn(XWPFTable table, List<String> categoryList) {
        if (categoryList.size() == 0) {
            return;
        }

        // 统计每个类别的出现次数，保持顺序
        Map<String, Integer> categoryCountMap = new LinkedHashMap<>();
        for (String category : categoryList) {
            categoryCountMap.put(category, categoryCountMap.getOrDefault(category, 0) + 1);
        }

        int startRowIndex = 1; // 从第二行开始（第一行是标题行）

        for (Map.Entry<String, Integer> entry : categoryCountMap.entrySet()) {
            int count = entry.getValue();
            if (count > 1) {
                // 需要合并的行数大于1时才进行合并
                int endRowIndex = startRowIndex + count - 1;
                mergeColumn(table, 0, startRowIndex, endRowIndex);
            }
            startRowIndex += count;
        }
    }

    /**
     * 合并列
     *
     * @param table   表格
     * @param col     要合并的列
     * @param fromRow 开始的行
     * @param toRow   结束的行
     */
    public void mergeColumn(XWPFTable table, int col, int fromRow, int toRow) {
        for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
            XWPFTableCell cell = table.getRow(rowIndex).getCell(col);
            if (rowIndex == fromRow) {
                // 第一个合并的单元格设置为RESTART
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                // 后续合并的单元格设置为CONTINUE
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            }
        }
    }
}
