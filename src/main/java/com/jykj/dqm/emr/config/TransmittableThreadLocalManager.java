package com.jykj.dqm.emr.config;

import com.alibaba.ttl.TransmittableThreadLocal;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 线程内缓存管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/3/21 9:29
 */
public class TransmittableThreadLocalManager {

    public static final Map<String, Map<String, Object>> processMap = new ConcurrentHashMap<>();

    public static final Map<String, Map<String, Object>> emprocessMap = new ConcurrentHashMap<>();

    /**
     * 根据线程信息缓存UserId，主线程和子线程都可以共享
     *  需要搭配
     *  1、@Async("doExportExecutor")
     *  2、TtlRunnable.get(() -> {
     *             String value = threadLocal.get();
     *             System.out.println("TransmittableThreadLocal value in new thread: " + value);
     *         })
     *  3、使用TtlExecutors包装线程池
     *   ExecutorService ttlExecutorService = TtlExecutors.getTtlExecutorService(executorService);
     *
     *   TransmittableThreadLocalManager.set(StpUtilMy.getTokenValue());
     *   String token = TransmittableThreadLocalManager.get();
     *   TransmittableThreadLocalManager.remove();
     *
     *
     */
    private static final TransmittableThreadLocal<String> ttl = new TransmittableThreadLocal<>();

    /**
     * 获取
     *
     * @return 值
     * <AUTHOR>
     */
    public static String get() {
        return ttl.get();
    }

    /**
     * 设置
     *
     * @param value 值
     * <AUTHOR>
     */
    public static void set(String value) {
        ttl.set(value);
    }

    /**
     * 清除当前线程userId
     */
    public static void remove() {
        ttl.remove();
    }
}
