package com.jykj.dqm.emr.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.emr.entity.DocumentRuleConfigurationDTO;
import com.jykj.dqm.emr.entity.DocumentRuleConfigurationQuery;
import com.jykj.dqm.emr.entity.DocumentRuleConfigurationQueryProject;
import com.jykj.dqm.emr.entity.MetadataStructureEmrQuery;
import com.jykj.dqm.emr.service.DocumentRuleConfigurationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 文档规则配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/22 15:53:05
 */
@Api(tags = {"文档规则配置"})
@RestController
@RequestMapping("/emr/documentRuleConfiguration")
public class DocumentRuleConfigurationController {
    @Resource
    private DocumentRuleConfigurationService documentRuleConfigurationService;

    @LogRemark(operate = "新增文档规则配置", module = "文档规则配置")
    @ApiOperation(value = "新增文档规则配置", notes = "文档规则配置")
    @PostMapping("/add")
    public R add(@Validated @RequestBody DocumentRuleConfigurationDTO documentRuleConfigurationDTO) {
        return documentRuleConfigurationService.add(documentRuleConfigurationDTO);
    }

    @LogRemark(operate = "更新文档规则配置", module = "文档规则配置")
    @ApiOperation(value = "更新文档规则配置", notes = "文档规则配置")
    @PostMapping("/update")
    public R update(@Validated @RequestBody DocumentRuleConfigurationDTO documentRuleConfigurationDTO) {
        return documentRuleConfigurationService.update(documentRuleConfigurationDTO);
    }

    @LogRemark(operate = "保存文档规则配置", module = "文档规则配置")
    @ApiOperation(value = "保存文档规则配置", notes = "文档规则配置")
    @PostMapping("/save")
    public R save(@Validated @RequestBody DocumentRuleConfigurationDTO documentRuleConfigurationDTO) {
        return documentRuleConfigurationService.save(documentRuleConfigurationDTO);
    }

    @LogRemark(operate = "标记完成文档规则配置", module = "文档规则配置")
    @ApiOperation(value = "标记完成文档规则配置", notes = "文档规则配置")
    @PostMapping("/markComplete")
    public R markComplete(@RequestBody DocumentRuleConfigurationQuery documentRuleConfigurationQuery) {
        return documentRuleConfigurationService.markComplete(documentRuleConfigurationQuery);
    }

    @ApiOperation(value = "查询文档规则配置(目录名称+目录编码+关联类型)", notes = "文档规则配置")
    @PostMapping("/query")
    public R query(@RequestBody DocumentRuleConfigurationQuery documentRuleConfiguration) {
        return documentRuleConfigurationService.query(documentRuleConfiguration);
    }

    @ApiOperation(value = "查询文档规则配置标题(目录名称+目录编码+关联类型)", notes = "文档规则配置")
    @PostMapping("/queryHeaderName")
    @Deprecated
    public R queryHeaderName(@RequestBody DocumentRuleConfigurationQuery documentRuleConfiguration) {
        return documentRuleConfigurationService.queryHeaderName(documentRuleConfiguration);
    }

    @ApiOperation(value = "通过名称查询项目", notes = "文档规则配置")
    @PostMapping("/queryProjectByName")
    public R queryProject(@RequestBody DocumentRuleConfigurationQueryProject documentRuleConfiguration) {
        return documentRuleConfigurationService.queryProject(documentRuleConfiguration);
    }

    /**
     * 获取表
     *
     * @param structureEmrQuery MetadataStructureEmrQuery
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "获取表", notes = "文档规则配置")
    @PostMapping(value = "/getStructures")
    public R getStructures(@RequestBody MetadataStructureEmrQuery structureEmrQuery) {
        return documentRuleConfigurationService.getStructures(structureEmrQuery);
    }


    @ApiOperation(value = "获取时间条件SQL", notes = "文档规则配置")
    @GetMapping("/getTimeConditionSql")
    public R getTimeConditionSql(@RequestParam(value = "dbType", required = true) String dbType,
                                 @RequestParam(value = "startDate", required = false) String startDate,
                                 @RequestParam(value = "endDate", required = false) String endDate) {
        String timeConditionSql = documentRuleConfigurationService.getTimeConditionSql(dbType, startDate, endDate);
        String timeTemplateSql = " BETWEEN #startDate AND #endDate";
        Map<String, String> result = new HashMap<>();
        result.put("timeConditionSql", timeConditionSql);
        result.put("timeTemplateSql", timeTemplateSql);
        return RUtil.success(result);
    }
}
