package com.jykj.dqm.emr.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryRuleConfiguration;
import com.jykj.dqm.emr.service.DataDictionaryDirectoryRuleConfigurationService;
import com.jykj.dqm.emr.service.RulePermissionConfigurationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 评级文档指标统计规则配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/9/26 11:56:26
 */

@Api(tags = {"评级文档指标统计规则配置"})
@RestController
@RequestMapping("/emr/dataDictionaryDirectoryRuleConfiguration")
public class DataDictionaryDirectoryRuleConfigurationController {
    @Autowired
    private DataDictionaryDirectoryRuleConfigurationService dataDictionaryDirectoryRuleConfigurationService;

    @Autowired
    private RulePermissionConfigurationService rulePermissionConfigurationService;

    @LogRemark(operate = "批量更新数据规则配置", module = "数据规则配置")
    @ApiOperation(value = "批量更新数据规则配置（注意只传修改的，不是全部）", notes = "基础数据字典")
    @PostMapping("/batchUpdateBase")
    public R batchUpdateBase(@Validated @RequestBody List<DataDictionaryDirectoryRuleConfiguration> dataDictionaryDirectoryConfigurations) {
        return dataDictionaryDirectoryRuleConfigurationService.batchUpdateBase(dataDictionaryDirectoryConfigurations);
    }

    @ApiOperation(value = "获取某条病历|质量数据字典", notes = "数据规则配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "directoryCode", value = "目录编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "directoryName", value = "目录名称", required = true, dataType = "String"),
            @ApiImplicitParam(name = "directoryType", value = "目录类型：2、病历数据，3、质量数据", required = true, dataType = "String"),
            @ApiImplicitParam(name = "projectId", value = "项目ID", required = true, dataType = "String")
    })
    @GetMapping("/getOneMedicaRecordsOrQuality")
    public R getOneMedicaRecordsOrQuality(@RequestParam(value = "directoryCode") String directoryCode,
                                          @RequestParam(value = "directoryName") String directoryName,
                                          @RequestParam(value = "directoryType") String configType,
                                          @RequestParam(value = "projectId") String projectId) {
        return dataDictionaryDirectoryRuleConfigurationService.getOneMedicaRecordsOrQuality(configType, directoryCode, directoryName, projectId);
    }

    @ApiOperation(value = "获取病历|质量数据字典规则", notes = "数据规则配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "directoryCode", value = "目录编码，为空查左侧列表，不为空查子级", required = true, dataType = "String"),
            @ApiImplicitParam(name = "directoryType", value = "目录类型：2、病历数据，3、质量数据", required = true, dataType = "String"),
            @ApiImplicitParam(name = "projectId", value = "项目ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userAccount", value = "用户账号，没有传则查全部", required = false, dataType = "String"),
            @ApiImplicitParam(name = "levelCode", value = "等级", required = true, dataType = "String")
    })
    @GetMapping("/getMedicaRecordsOrQualityRule")
    public R getMedicaRecordsOrQualityRule(@RequestParam(value = "directoryCode") String directoryCode,
                                           @RequestParam(value = "directoryType") String configType,
                                           @RequestParam(value = "projectId") String projectId,
                                           @RequestParam(value = "userAccount", required = false) String userAccount,
                                           @RequestParam(value = "levelCode") String levelCode) {
        return rulePermissionConfigurationService.getMedicaRecordsOrQualityRule(configType, directoryCode, userAccount, levelCode, projectId);
    }
}
