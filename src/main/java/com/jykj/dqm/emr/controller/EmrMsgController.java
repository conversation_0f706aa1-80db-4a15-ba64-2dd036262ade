package com.jykj.dqm.emr.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.emr.entity.EmrMsg;
import com.jykj.dqm.emr.entity.EmrMsgQueryDTO;
import com.jykj.dqm.emr.service.EmrMsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 电子病历评级相关信息管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/1/10 16:17:03
 */
@Api(tags = {"电子病历评级相关信息"})
@RestController
@RequestMapping("/message")
public class EmrMsgController {
    @Autowired
    private EmrMsgService emrMsgService;

    /**
     * 列表分页查询
     *
     * @param emrMsgQueryDTO EmrMsgQueryDTO
     * @return R
     * <AUTHOR>
     */
    @ApiOperation(value = "列表分页查询", notes = "")
    @PostMapping("/queryAllMsgList")
    public R queryAllMsgList(@RequestBody EmrMsgQueryDTO emrMsgQueryDTO) {
        return emrMsgService.queryAllMsgList(emrMsgQueryDTO);
    }


    /**
     * 新增消息
     *
     * @param emrMsg EmrMsg
     * @return R
     * <AUTHOR>
     */
    @ApiOperation(value = "新增消息", notes = "")
    @LogRemark(operate = "新增消息", module = "电子病历评级相关信息")
    @PostMapping("/addMsg")
    public R addMsg(@Validated EmrMsg emrMsg, @RequestPart @RequestParam(value = "files", required = false) List<MultipartFile> files) {
        return emrMsgService.addMsg(emrMsg, files);
    }

    /**
     * 修改消息
     *
     * @param emrMsg EmrMsg
     * @return R
     * <AUTHOR>
     */
    @ApiOperation(value = "修改消息", notes = "")
    @LogRemark(operate = "修改消息", module = "电子病历评级相关信息")
    @PostMapping("/updateMsg")
    public R updateMsg(@Validated EmrMsg emrMsg, @RequestPart @RequestParam(value = "files", required = false) List<MultipartFile> files) {
        return emrMsgService.updateMsg(emrMsg, files);
    }

    /**
     * 删除消息
     *
     * @param msgId msgId
     * @return R
     * <AUTHOR>
     */
    @ApiOperation(value = "删除消息", notes = "")
    @LogRemark(operate = "删除消息", module = "电子病历评级相关信息")
    @DeleteMapping("/deleteMsg")
    public R deleteMsg(@NotBlank @RequestParam("msgId") Integer msgId) {
        return emrMsgService.deleteMsg(msgId);
    }

    @ApiOperation(value = "上传附件（支持同时上传多个文件）", notes = "")
    @LogRemark(operate = "上传附件", module = "电子病历评级相关信息")
    @PostMapping("/uploadMultipleFiles")
    public R uploadMultipleFiles(@RequestParam("files") List<MultipartFile> files, @NotBlank @RequestParam("msgId") Integer msgId) {
        return emrMsgService.uploadMultipleFiles(files, msgId);
    }

    @ApiOperation(value = "下载附件（支持下载单个和下载所有）,fileName为空就是下载所有附件，也可以指定一个文件", httpMethod = "GET", produces = "application/octet-stream")
    @LogRemark(operate = "下载附件", module = "电子病历评级相关信息")
    @GetMapping("/download")
    public void downloadFile(HttpServletResponse response, @RequestParam("msgId") String msgId, @RequestParam(value = "fileName", required = false) String fileName) {
        emrMsgService.downloadFile(response, fileName, msgId);
    }
}
