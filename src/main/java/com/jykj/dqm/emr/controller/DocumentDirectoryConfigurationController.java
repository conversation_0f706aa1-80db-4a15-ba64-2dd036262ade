package com.jykj.dqm.emr.controller;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfigurationQuery;
import com.jykj.dqm.emr.entity.DocumentRuleConfigurationQuery;
import com.jykj.dqm.emr.service.DocumentDirectoryConfigurationService;
import com.jykj.dqm.emr.service.DocumentRuleConfigurationService;
import com.jykj.dqm.utils.MapperUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 文档目录配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 15:58:25
 */
@Api(tags = {"文档目录配置"})
@RestController
@RequestMapping("/emr/documentDirectoryConfiguration")
public class DocumentDirectoryConfigurationController {
    @Autowired
    private DocumentDirectoryConfigurationService documentDirectoryConfigurationService;

    @Autowired
    private DocumentRuleConfigurationService documentRuleConfigurationService;

    @LogRemark(operate = "新增文档目录", module = "文档目录配置")
    @ApiOperation(value = "新增文档目录", notes = "文档目录配置")
    @PostMapping("/add")
    public R add(@Validated @RequestBody DocumentDirectoryConfiguration documentDirectoryConfiguration) {
        return documentDirectoryConfigurationService.add(documentDirectoryConfiguration);
    }

    @LogRemark(operate = "更新文档目录", module = "文档目录配置")
    @ApiOperation(value = "更新文档目录", notes = "文档目录配置")
    @PostMapping("/update")
    public R update(@Validated @RequestBody DocumentDirectoryConfiguration documentDirectoryConfiguration) {
        return documentDirectoryConfigurationService.update(documentDirectoryConfiguration);
    }

    @LogRemark(operate = "删除文档目录", module = "文档目录配置")
    @ApiOperation(value = "删除文档目录", notes = "文档目录配置")
    @DeleteMapping("/delete")
    public R delete(@RequestParam("ids") List<Long> ids) {
        return documentDirectoryConfigurationService.delete(ids);
    }

    @ApiOperation(value = "查询文档目录", notes = "文档目录配置")
    @PostMapping("/query")
    public R query(@RequestBody DocumentDirectoryConfigurationQuery documentDirectoryConfiguration) {
        return documentDirectoryConfigurationService.query(documentDirectoryConfiguration);
    }

    @ApiOperation(value = "文档目录配置使用，查询所有文档目录以及二级的要求项目（选择数据及运算公式）", notes = "文档目录配置")
    @PostMapping("/queryDirectoryAndProject")
    public R queryDirectoryAndProject(@RequestBody DocumentDirectoryConfigurationQuery documentDirectoryConfiguration) {
        R r;
        if (StrUtil.isNotBlank(documentDirectoryConfiguration.getDirectoryCode())
                && StrUtil.isNotBlank(documentDirectoryConfiguration.getDirectoryName())
                && StrUtil.isNotBlank(documentDirectoryConfiguration.getEmrRuleType())) {
            DocumentRuleConfigurationQuery documentRuleConfigurationQuery = MapperUtils.INSTANCE.map(DocumentRuleConfigurationQuery.class, documentDirectoryConfiguration);
            r = documentRuleConfigurationService.query(documentRuleConfigurationQuery);
        } else {
            r = documentDirectoryConfigurationService.queryAll(documentDirectoryConfiguration);
        }
        return r;
    }

    @LogRemark(operate = "批量更新文档目录", module = "文档目录配置")
    @ApiOperation(value = "批量更新文档目录(根据ID只是更新，没有新增)", notes = "文档目录配置")
    @PostMapping("/batchUpdate")
    public R batchUpdate(@Validated @RequestBody List<DocumentDirectoryConfiguration> documentDirectoryConfiguration) {
        return documentDirectoryConfigurationService.batchUpdateMy(documentDirectoryConfiguration);
    }
}
