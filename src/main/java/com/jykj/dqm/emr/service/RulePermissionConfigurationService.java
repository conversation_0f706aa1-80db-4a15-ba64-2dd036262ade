package com.jykj.dqm.emr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.emr.entity.RulePermissionConfiguration;
import com.jykj.dqm.emr.entity.RulePermissionConfigurationSaveDTO;

import java.util.Map;

/**
 * 规则配置权限
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/7/4 16:20:48
 */
public interface RulePermissionConfigurationService extends IService<RulePermissionConfiguration> {
    R saveRulePermission(RulePermissionConfigurationSaveDTO rulePermissionConfigurationSaveDTO);

    RulePermissionConfigurationSaveDTO getRulePermission(String userAccount, String projectId);

    Map<String, Object> getMyTasksAndProgress(String levelCode, String userName, String userAccount, String configType, String projectId);

    R queryDirectoryTree(String configType, String userAccount, String levelCode, boolean needNotAllocationTask, String projectId);

    RulePermissionConfigurationSaveDTO getTask(String configType, String userAccount, String levelCode, String directoryCode, String projectId);

    R getMedicaRecordsOrQualityRule(String configType, String directoryCode, String userAccount, String levelCode, String projectId);
}
