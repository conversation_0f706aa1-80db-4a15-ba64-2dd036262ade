package com.jykj.dqm.emr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryRuleConfiguration;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_DATA_DICTIONARY_DIRECTORY_RULE_CONFIGURATION(数据字典规则配置)】的数据库操作Service
 * @createDate 2024-04-10 17:57:26
 */
public interface DataDictionaryDirectoryRuleConfigurationService extends IService<DataDictionaryDirectoryRuleConfiguration> {

    R batchUpdateBase(List<DataDictionaryDirectoryRuleConfiguration> dataDictionaryDirectoryConfigurations);

    R getOneMedicaRecordsOrQuality(String configType, String directoryCode, String directoryName, String projectId);
}
