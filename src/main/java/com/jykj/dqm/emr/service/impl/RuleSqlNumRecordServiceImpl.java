package com.jykj.dqm.emr.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.emr.entity.RuleSqlNumRecord;
import com.jykj.dqm.emr.service.RuleSqlNumRecordService;
import com.jykj.dqm.emr.dao.RuleSqlNumRecordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DQM_EMR_RULE_SQL_NUM_RECORD(数据质量SQL结果记录)】的数据库操作Service实现
* @createDate 2024-02-06 16:54:00
*/
@Service
public class RuleSqlNumRecordServiceImpl extends ServiceImpl<RuleSqlNumRecordMapper, RuleSqlNumRecord>
    implements RuleSqlNumRecordService{

}
