package com.jykj.dqm.emr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryConfiguration;

import java.util.List;

/**
 * 评级文档指标统计配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/9/26 11:56:26
 */
public interface DataDictionaryDirectoryConfigurationService extends IService<DataDictionaryDirectoryConfiguration> {
    R addBase(DataDictionaryDirectoryConfiguration dataDictionaryDirectoryConfiguration);

    R batchUpdateBase(List<DataDictionaryDirectoryConfiguration> dataDictionaryDirectoryConfigurations);

    R deleteBase(List<Long> ids);

    R getBase();

    R getMedicaRecordsAndQuality(Integer directoryType, String code);

    /**
     * 获取所有病历|质量数据字典
     *
     * @param directoryType 目录类型：2、病历数据，3、质量数据
     * @return List<DataDictionaryDirectoryConfiguration>
     */
    List<DataDictionaryDirectoryConfiguration> getAllMedicaRecordsOrQuality(Integer directoryType);
}
