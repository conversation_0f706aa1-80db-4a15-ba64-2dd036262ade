package com.jykj.dqm.emr.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.emr.dao.DocumentExportRecordRuleDetailMapper;
import com.jykj.dqm.emr.entity.DocumentExportRecordRuleDetail;
import com.jykj.dqm.emr.service.DocumentExportRecordRuleDetailService;
import org.springframework.stereotype.Service;

/**
 * 文档导出记录规则详情
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/29 15:42:09
 */
@Service
public class DocumentExportRecordRuleDetailServiceImpl extends ServiceImpl<DocumentExportRecordRuleDetailMapper, DocumentExportRecordRuleDetail> implements DocumentExportRecordRuleDetailService {

}
