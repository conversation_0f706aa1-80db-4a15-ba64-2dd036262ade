package com.jykj.dqm.emr.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.dao.EmrMsgMapper;
import com.jykj.dqm.emr.entity.EmrMsg;
import com.jykj.dqm.emr.entity.EmrMsgQueryDTO;
import com.jykj.dqm.emr.service.EmrMsgService;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.utils.SystemUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_MSG(电子病历评级相关信息)】的数据库操作Service实现
 * @createDate 2024-03-15 14:09:21
 */
@Service
public class EmrMsgServiceImpl extends ServiceImpl<EmrMsgMapper, EmrMsg> implements EmrMsgService {
    @Override
    public R queryAllMsgList(EmrMsgQueryDTO emrMsgQueryDTO) {
        PageHelper.startPage(emrMsgQueryDTO.getPageNum(), emrMsgQueryDTO.getPageSize());
        LambdaQueryWrapper<EmrMsg> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StrUtil.isNotBlank(emrMsgQueryDTO.getMsgType()), EmrMsg::getMsgType, emrMsgQueryDTO.getMsgType());
        lambdaQueryWrapper.like(StrUtil.isNotBlank(emrMsgQueryDTO.getMsgTitle()), EmrMsg::getMsgTitle, emrMsgQueryDTO.getMsgType());
        lambdaQueryWrapper.like(StrUtil.isNotBlank(emrMsgQueryDTO.getMsgContent()), EmrMsg::getMsgContent, emrMsgQueryDTO.getMsgContent());
        if (StrUtil.isNotBlank(emrMsgQueryDTO.getBeginTime())) {
            lambdaQueryWrapper.ge(EmrMsg::getSendingTime, DateUtil.parse(emrMsgQueryDTO.getBeginTime()));
        }
        if (StrUtil.isNotBlank(emrMsgQueryDTO.getEndTime())) {
            lambdaQueryWrapper.lt(EmrMsg::getSendingTime, DateUtil.parse(emrMsgQueryDTO.getBeginTime()));
        }
        lambdaQueryWrapper.orderByDesc(EmrMsg::getSendingTime);
        List<EmrMsg> list = this.list(lambdaQueryWrapper);
        PageInfo<EmrMsg> msgPageInfo = new PageInfo<>(list);
        return RUtil.success(msgPageInfo);
    }

    @Override
    public R addMsg(EmrMsg emrMsg, List<MultipartFile> files) {
        saveMsg(emrMsg, files);
        return RUtil.success();
    }

    private void saveMsg(EmrMsg emrMsg, List<MultipartFile> files) {
        //设置文件名称
        if (CollUtil.isNotEmpty(files)) {
            Set<String> fileNames = files.stream().map(item -> item.getOriginalFilename()).collect(Collectors.toSet());
            emrMsg.setAttachment(String.join(",", fileNames));
        }
        if (emrMsg.getId() != null) {
            this.updateById(emrMsg);
        } else {
            this.save(emrMsg);
        }
        //上传附件
        if (CollUtil.isNotEmpty(files)) {
            uploadMultipleFiles(files, emrMsg.getId());
        }
    }

    @Override
    public R updateMsg(EmrMsg emrMsg, List<MultipartFile> files) {
        saveMsg(emrMsg, files);
        return RUtil.success();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R deleteMsg(Integer msgId) {
        this.removeById(msgId);
        String filePath = SystemUtils.getFilePath() + "/msgfiles/" + msgId;
        FileUtil.del(Paths.get(filePath));
        return RUtil.success();
    }

    @Override
    public R uploadMultipleFiles(List<MultipartFile> files, Integer msgId) {
        String filePath = SystemUtils.getFilePath() + "/msgfiles/" + msgId;
        // 目标目录
        Path targetDirectory = Paths.get(filePath + "temp");
        if (!Files.exists(targetDirectory)) {
            FileUtil.mkdir(targetDirectory);
        }

        // 遍历文件列表并上传
        List<File> uploadedFiles = new ArrayList<>();
        List<String> fileNames = new ArrayList<>();
        for (MultipartFile file : files) {
            try {
                Path targetPath = targetDirectory.resolve(file.getOriginalFilename());
                File targetFile = targetPath.toFile();
                file.transferTo(targetFile);
                uploadedFiles.add(targetFile);
                fileNames.add(file.getOriginalFilename());
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                FileUtil.del(targetDirectory);
                throw new BusinessException("上传文件失败: " + file.getOriginalFilename());
            }
        }
        FileUtil.del(Paths.get(filePath));
        FileUtil.rename(targetDirectory, msgId + "", true);
        return RUtil.success(fileNames);
    }

    @Override
    public void downloadFile(HttpServletResponse response, String fileName, String msgId) {
        String filePath = SystemUtils.getFilePath();
        String rootPath = filePath + "/msgfiles/";
        String path = rootPath + msgId;
        if (StrUtil.isNotBlank(fileName)) {
            path = path + "/" + fileName;
        }
        File file = new File(path);
        boolean isZip = false;
        if (!file.exists()) {
            throw new BusinessException("附件失效!");
        }
        if (file.isDirectory()) {
            response.setContentType("application/zip;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;Filename=" + URLUtil.encode(msgId) + ".zip");
            file = ZipUtil.zip(rootPath + "/" + msgId + "/");
            isZip = true;
        } else {
            response.setContentType("application/octet-stream;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;Filename=" + URLUtil.encode(fileName));
        }
        response.setHeader("Content-Transfer-Encoding", "binary");
        try (FileInputStream fis = new FileInputStream(file);
             OutputStream os = response.getOutputStream()) {
            // 将文件写入response流中
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            log.error("导出失败：" + e.getMessage());
            throw new BusinessException("导出失败：" + e.getMessage());
        }
        //删除压缩文件
        if (isZip) {
            FileUtil.del(file);
        }
    }
}
