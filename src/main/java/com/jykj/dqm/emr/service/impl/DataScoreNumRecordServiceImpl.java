package com.jykj.dqm.emr.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.emr.entity.DataScoreNumRecord;
import com.jykj.dqm.emr.service.DataScoreNumRecordService;
import com.jykj.dqm.emr.dao.DataScoreNumRecordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DQM_EMR_DATA_SCORE_NUM_RECORD(数据评分数据量记录)】的数据库操作Service实现
* @createDate 2024-02-06 16:39:41
*/
@Service
public class DataScoreNumRecordServiceImpl extends ServiceImpl<DataScoreNumRecordMapper, DataScoreNumRecord>
    implements DataScoreNumRecordService{

}
