package com.jykj.dqm.emr.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.dao.DocumentExportRecordErrorDetailMapper;
import com.jykj.dqm.emr.dao.DocumentExportRecordMapper;
import com.jykj.dqm.emr.entity.DocumentExporQueryDTO;
import com.jykj.dqm.emr.entity.DocumentExportEachDoc;
import com.jykj.dqm.emr.entity.DocumentExportRecord;
import com.jykj.dqm.emr.entity.DocumentExportRecordErrorDetail;
import com.jykj.dqm.emr.entity.DocumentExportRecordQuery;
import com.jykj.dqm.emr.entity.DocumentExportRecordRuleDetail;
import com.jykj.dqm.emr.entity.DocumentPreview;
import com.jykj.dqm.emr.service.DocumentExportRecordRuleDetailService;
import com.jykj.dqm.emr.service.DocumentExportRecordService;
import com.jykj.dqm.emr.utils.PathNameUtils;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.utils.DateUtils;
import com.jykj.dqm.utils.SystemUtils;
import com.jykj.dqm.utils.WordUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 文档导出记录
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/23 14:47:11
 */
@Service
public class DocumentExportRecordServiceImpl extends ServiceImpl<DocumentExportRecordMapper, DocumentExportRecord> implements DocumentExportRecordService {

    @Autowired
    private DocumentExportRecordRuleDetailService documentExportRecordRuleDetailService;

    @Autowired
    private DocumentExportRecordErrorDetailMapper documentExportRecordErrorDetailMapper;

    @Autowired
    HttpServletResponse response;

    @Override
    public R query(DocumentExportRecordQuery documentExportRecordQuery) {
        PageHelper.startPage(documentExportRecordQuery.getPageNum(), documentExportRecordQuery.getPageSize());
        LambdaQueryWrapper<DocumentExportRecord> lambdaQueryWrapper = Wrappers.lambdaQuery();
        //导出状态：1、预览；2、导出
        //lambdaQueryWrapper.eq(DocumentExportRecord::getExportStatus, "2");
        lambdaQueryWrapper.eq(DocumentExportRecord::getProjectId, documentExportRecordQuery.getProjectId());
        //单独写if，因为Children lt(boolean condition, R column, Object val);就算condition==false，后面的var表达式也会运算
        if (StrUtil.isNotBlank(documentExportRecordQuery.getStartTime())) {
            lambdaQueryWrapper.ge(DocumentExportRecord::getCreateTime, DateUtil.parse(documentExportRecordQuery.getStartTime(), DateUtils.YYYY_MM_DD));
        }
        if (StrUtil.isNotBlank(documentExportRecordQuery.getEndTime())) {
            documentExportRecordQuery.setEndTime(documentExportRecordQuery.getEndTime() + " 23:59:59");
            lambdaQueryWrapper.le(DocumentExportRecord::getUpdateTime, DateUtil.parse(documentExportRecordQuery.getEndTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        lambdaQueryWrapper.eq(StrUtil.isNotBlank(documentExportRecordQuery.getExportDocumentLevel()), DocumentExportRecord::getExportDocumentLevel, documentExportRecordQuery.getExportDocumentLevel())
                .like(StrUtil.isNotBlank(documentExportRecordQuery.getCreateBy()), DocumentExportRecord::getCreateBy, documentExportRecordQuery.getCreateBy());
        lambdaQueryWrapper.orderByDesc(DocumentExportRecord::getCreateTime);
        List<DocumentExportRecord> list = this.list(lambdaQueryWrapper);
        PageInfo<DocumentExportRecord> pageInfo = new PageInfo<>(list);
        return RUtil.success(pageInfo);
    }

    @Override
    public void previewWord(DocumentPreview documentPreview) {
        //根据ID找到路径
        String path = SystemUtils.getFilePath() + "/word/" + documentPreview.getExportRecordId() + "/";
        //然后目录下文件传输到页面
        String filePath = PathNameUtils.getPreviewFullPathFileName(path, documentPreview.getDirectoryCode(), documentPreview.getDirectoryName(), documentPreview.getEmrRuleType()) + ".docx";
        if (!FileUtil.exist(filePath)) {
            throw new BusinessException("文件不存在！");
        }
        try {
            InputStream input = new FileInputStream(filePath);
            WordUtil.docxToHtml(input, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R delete(String exportRecordId) {
        //删除记录表
        this.removeById(exportRecordId);
        //删除文档导出记录规则详情表（备注信息）
        documentExportRecordRuleDetailService.remove(new LambdaQueryWrapper<DocumentExportRecordRuleDetail>().eq(DocumentExportRecordRuleDetail::getExportRecordId, exportRecordId));
        documentExportRecordErrorDetailMapper.delete(new LambdaQueryWrapper<DocumentExportRecordErrorDetail>().eq(DocumentExportRecordErrorDetail::getExportRecordId, exportRecordId));
        String path = SystemUtils.getFilePath() + "/word/" + exportRecordId + "/";
        FileUtil.del(path);
        return RUtil.success();
    }

    @Override
    public R getErrorDetail(String exportRecordId) {
        List<DocumentExportRecordErrorDetail> documentExportRecordErrorDetails = documentExportRecordErrorDetailMapper.selectList(new LambdaQueryWrapper<DocumentExportRecordErrorDetail>().eq(DocumentExportRecordErrorDetail::getExportRecordId, exportRecordId));
        Map<String, List<DocumentExportRecordErrorDetail>> collect = documentExportRecordErrorDetails.stream().collect(Collectors.groupingBy(DocumentExportRecordErrorDetail::getErrorType));
        Map<String, Object> result = new HashMap<>();
        result.put("告警", Optional.ofNullable(collect.get("告警")).orElse(new ArrayList<>()));
        result.put("失败", Optional.ofNullable(collect.get("失败")).orElse(new ArrayList<>()));
        List<DocumentExportRecordRuleDetail> list = documentExportRecordRuleDetailService.list(new LambdaQueryWrapper<DocumentExportRecordRuleDetail>().eq(DocumentExportRecordRuleDetail::getExportRecordId, exportRecordId));
        result.put("批注", list.stream().filter(item -> StrUtil.isNotBlank(item.getProblemDataRemarks1()) || StrUtil.isNotBlank(item.getProblemDataRemarks2()) || StrUtil.isNotBlank(item.getProblemDataRemarks3()) || StrUtil.isNotBlank(item.getProblemDataRemarks4())).collect(Collectors.toList()));
        return RUtil.success(result);
    }

    @Override
    public R getProblemDataRemarks(String exportRecordId) {
        List<DocumentExportRecordRuleDetail> list = documentExportRecordRuleDetailService.list(new LambdaQueryWrapper<DocumentExportRecordRuleDetail>().eq(DocumentExportRecordRuleDetail::getExportRecordId, exportRecordId));
        return RUtil.success(list);
    }

    @Override
    public R getOneProblemDataRemark(DocumentExportEachDoc documentExportEachDoc) {
        DocumentExportRecordRuleDetail recordRuleDetailServiceOne = documentExportRecordRuleDetailService.getOne(
                new LambdaQueryWrapper<DocumentExportRecordRuleDetail>()
                        .eq(DocumentExportRecordRuleDetail::getExportRecordId, documentExportEachDoc.getExportRecordId())
                        .eq(DocumentExportRecordRuleDetail::getDirectoryCode, documentExportEachDoc.getDirectoryCode())
                        .eq(DocumentExportRecordRuleDetail::getDirectoryName, documentExportEachDoc.getDirectoryName())
                        .eq(DocumentExportRecordRuleDetail::getEmrRuleType, documentExportEachDoc.getEmrRuleType()));
        return RUtil.success(recordRuleDetailServiceOne);
    }

    @Override
    public R getProblemDataHistoryRemarks(DocumentExporQueryDTO documentExportEachDoc) {
        List<DocumentExportRecordRuleDetail> recordRuleDetailServices = documentExportRecordRuleDetailService.list(
                new LambdaQueryWrapper<DocumentExportRecordRuleDetail>()
                        .eq(StrUtil.isNotBlank(documentExportEachDoc.getExportRecordId()), DocumentExportRecordRuleDetail::getExportRecordId, documentExportEachDoc.getExportRecordId())
                        .eq(DocumentExportRecordRuleDetail::getDirectoryCode, documentExportEachDoc.getDirectoryCode())
                        .eq(DocumentExportRecordRuleDetail::getDirectoryName, documentExportEachDoc.getDirectoryName())
                        .eq(DocumentExportRecordRuleDetail::getEmrRuleType, documentExportEachDoc.getEmrRuleType()));
        if ("problemDataRemarks1".equals(documentExportEachDoc.getType())) {
            recordRuleDetailServices = recordRuleDetailServices.stream().filter(item -> StrUtil.isNotBlank(item.getProblemDataRemarks1())).collect(Collectors.toList());
        } else if ("problemDataRemarks2".equals(documentExportEachDoc.getType())) {
            recordRuleDetailServices = recordRuleDetailServices.stream().filter(item -> StrUtil.isNotBlank(item.getProblemDataRemarks2())).collect(Collectors.toList());
        } else if ("problemDataRemarks3".equals(documentExportEachDoc.getType())) {
            recordRuleDetailServices = recordRuleDetailServices.stream().filter(item -> StrUtil.isNotBlank(item.getProblemDataRemarks3())).collect(Collectors.toList());
        } else if ("problemDataRemarks4".equals(documentExportEachDoc.getType())) {
            recordRuleDetailServices = recordRuleDetailServices.stream().filter(item -> StrUtil.isNotBlank(item.getProblemDataRemarks4())).collect(Collectors.toList());
        }
        return RUtil.success(recordRuleDetailServices);
    }
}
