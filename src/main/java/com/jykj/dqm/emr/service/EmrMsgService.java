package com.jykj.dqm.emr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.emr.entity.EmrMsg;
import com.jykj.dqm.emr.entity.EmrMsgQueryDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_MSG(电子病历评级相关信息)】的数据库操作Service
 * @createDate 2024-03-15 14:09:21
 */
public interface EmrMsgService extends IService<EmrMsg> {

    R queryAllMsgList(EmrMsgQueryDTO emrMsgQueryDTO);

    R addMsg(EmrMsg emrMsg, List<MultipartFile> files);

    R updateMsg(EmrMsg emrMsg, List<MultipartFile> files);

    R deleteMsg(Integer msgId);

    R uploadMultipleFiles(List<MultipartFile> files, Integer msgId);

    void downloadFile(HttpServletResponse response, String fileName, String msgId);
}
