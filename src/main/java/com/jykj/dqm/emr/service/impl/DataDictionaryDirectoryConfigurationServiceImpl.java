package com.jykj.dqm.emr.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.dao.DataDictionaryDirectoryConfigurationMapper;
import com.jykj.dqm.emr.entity.DataDictionaryAssociated;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryRuleConfiguration;
import com.jykj.dqm.emr.entity.RulePermissionConfiguration;
import com.jykj.dqm.emr.service.DataDictionaryAssociatedService;
import com.jykj.dqm.emr.service.DataDictionaryDirectoryConfigurationService;
import com.jykj.dqm.emr.service.DataDictionaryDirectoryRuleConfigurationService;
import com.jykj.dqm.emr.service.RulePermissionConfigurationService;
import com.jykj.dqm.emr.utils.CommonUtils;
import com.jykj.dqm.utils.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 评级文档指标统计配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/9/26 11:56:26
 */
@Service
public class DataDictionaryDirectoryConfigurationServiceImpl extends ServiceImpl<DataDictionaryDirectoryConfigurationMapper, DataDictionaryDirectoryConfiguration> implements DataDictionaryDirectoryConfigurationService {
    @Autowired
    private DataDictionaryAssociatedService dataDictionaryAssociatedService;
    @Autowired
    private RulePermissionConfigurationService rulePermissionConfigurationService;
    @Autowired
    private DataDictionaryDirectoryRuleConfigurationService dataDictionaryDirectoryRuleConfigurationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R addBase(DataDictionaryDirectoryConfiguration dataDictionaryDirectoryConfiguration) {
        //共用一二级目录，可能已经有ID
        if (StrUtil.isBlank(dataDictionaryDirectoryConfiguration.getId())) {
            dataDictionaryDirectoryConfiguration.setId(IdUtils.getID());
        }
        this.save(dataDictionaryDirectoryConfiguration);
        return RUtil.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R batchUpdateBase(List<DataDictionaryDirectoryConfiguration> dataDictionaryDirectoryConfigurations) {
        if (CollUtil.isEmpty(dataDictionaryDirectoryConfigurations)) {
            return RUtil.error("数据为空");
        }
        DataDictionaryDirectoryConfiguration dbConfiguration;
        for (DataDictionaryDirectoryConfiguration configuration : dataDictionaryDirectoryConfigurations) {
            dbConfiguration = this.getById(configuration.getId());
            if (dbConfiguration != null && (dbConfiguration.getDirectoryCode().length() >= 7 || "1".equals(dbConfiguration.getDirectoryType()))) {
                //更新任务分配
                rulePermissionConfigurationService.update(
                        Wrappers.lambdaUpdate(RulePermissionConfiguration.class)
                                .set(RulePermissionConfiguration::getDirectoryCode, configuration.getDirectoryCode())
                                .set(RulePermissionConfiguration::getDirectoryName, configuration.getDirectoryName())
                                .eq(RulePermissionConfiguration::getDirectoryCode, dbConfiguration.getDirectoryCode())
                                .eq(RulePermissionConfiguration::getDirectoryName, dbConfiguration.getDirectoryName())
                );
                //更新规则配置
                dataDictionaryDirectoryRuleConfigurationService.update(Wrappers.lambdaUpdate(DataDictionaryDirectoryRuleConfiguration.class)
                        .set(DataDictionaryDirectoryRuleConfiguration::getDirectoryCode, configuration.getDirectoryCode())
                        .set(DataDictionaryDirectoryRuleConfiguration::getDirectoryName, configuration.getDirectoryName())
                        .eq(DataDictionaryDirectoryRuleConfiguration::getDirectoryCode, dbConfiguration.getDirectoryCode())
                        .eq(DataDictionaryDirectoryRuleConfiguration::getDirectoryName, dbConfiguration.getDirectoryName())
                );
                //更新关联表：DQM_EMR_DATA_DICTIONARY_ASSOCIATED
                dataDictionaryAssociatedService.update(Wrappers.lambdaUpdate(DataDictionaryAssociated.class)
                        .set(DataDictionaryAssociated::getDirectoryCode, configuration.getDirectoryCode())
                        .set(DataDictionaryAssociated::getDirectoryName, configuration.getDirectoryName())
                        .eq(DataDictionaryAssociated::getDirectoryCode, dbConfiguration.getDirectoryCode())
                        .eq(DataDictionaryAssociated::getDirectoryName, dbConfiguration.getDirectoryName())
                );

            }
        }
        //只更新修改的
        this.updateBatchById(dataDictionaryDirectoryConfigurations);
        return RUtil.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R deleteBase(List<Long> ids) {
        //删除相关的规则配置
        List<DataDictionaryDirectoryConfiguration> dataDictionaryDirectoryConfigurations = this.listByIds(ids);
        for (DataDictionaryDirectoryConfiguration dbConfiguration : dataDictionaryDirectoryConfigurations) {
            rulePermissionConfigurationService.remove(
                    Wrappers.lambdaUpdate(RulePermissionConfiguration.class)
                            .eq(RulePermissionConfiguration::getDirectoryCode, dbConfiguration.getDirectoryCode())
                            .eq(RulePermissionConfiguration::getDirectoryName, dbConfiguration.getDirectoryName())
            );

            dataDictionaryDirectoryRuleConfigurationService.remove(Wrappers.lambdaUpdate(DataDictionaryDirectoryRuleConfiguration.class)
                    .eq(DataDictionaryDirectoryRuleConfiguration::getDirectoryCode, dbConfiguration.getDirectoryCode())
                    .eq(DataDictionaryDirectoryRuleConfiguration::getDirectoryName, dbConfiguration.getDirectoryName())
            );
        }
        this.removeBatchByIds(ids);
        //先删除之前的关联关系
        dataDictionaryAssociatedService.remove(new LambdaQueryWrapper<DataDictionaryAssociated>().in(DataDictionaryAssociated::getDirectoryId, ids));
        return RUtil.success();
    }

    @Override
    public R getBase() {
        //根据编号排序，查询所有
        List<DataDictionaryDirectoryConfiguration> list = this.list(
                new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>().eq(DataDictionaryDirectoryConfiguration::getDirectoryType, 1));
        CommonUtils.sortByDirectoryCode(list);
        return RUtil.success(list);
    }

    @Override
    public R getMedicaRecordsAndQuality(Integer directoryType, String directoryCode) {
        //根据编号排序，查询所有  同时按照一二级目录分组（树形）
        List<DataDictionaryDirectoryConfiguration> list = this.list(
                new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>()
                        .eq(DataDictionaryDirectoryConfiguration::getDirectoryType, directoryType)
                        .likeRight(StrUtil.isNotBlank(directoryCode), DataDictionaryDirectoryConfiguration::getDirectoryCode, directoryCode)
                        .orderByAsc(DataDictionaryDirectoryConfiguration::getDirectoryCode)
        );
        //树形
        List<DataDictionaryDirectoryConfiguration> directorys;
        if (StrUtil.isBlank(directoryCode)) {
            directorys = list.stream().filter(entity -> entity.getDirectoryCode().length() == 2).collect(Collectors.toList());
        } else if (directoryCode.length() == 2) {
            directorys = list.stream().filter(entity -> entity.getDirectoryCode().length() <= 5 && entity.getDirectoryCode().length() > 2).collect(Collectors.toList());
        } else {
            directorys = list.stream().filter(entity -> entity.getDirectoryCode().length() > 5).collect(Collectors.toList());
        }
        CommonUtils.sortByDirectoryCode(directorys);
        return RUtil.success(directorys);
    }

    @Override
    public List<DataDictionaryDirectoryConfiguration> getAllMedicaRecordsOrQuality(Integer directoryType) {
        //根据编号排序，查询所有  同时按照一二级目录分组（树形）
        List<DataDictionaryDirectoryConfiguration> list = this.list(new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>().eq(DataDictionaryDirectoryConfiguration::getDirectoryType, directoryType));
        CommonUtils.sortByDirectoryCode(list);
        return list;
    }
}
