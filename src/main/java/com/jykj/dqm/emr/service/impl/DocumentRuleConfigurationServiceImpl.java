package com.jykj.dqm.emr.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.auth.dao.SysUserMapper;
import com.jykj.dqm.auth.dao.SysUserRoleMapper;
import com.jykj.dqm.auth.entity.EmrSysUserGroup;
import com.jykj.dqm.auth.entity.SysRole;
import com.jykj.dqm.auth.entity.SysUser;
import com.jykj.dqm.auth.service.EmrSysUserGroupService;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.dao.DocumentRuleConfigurationMapper;
import com.jykj.dqm.emr.dao.RequireProjectDictionaryMapper;
import com.jykj.dqm.emr.entity.*;
import com.jykj.dqm.emr.manager.PermissionLevelCodeUtil;
import com.jykj.dqm.emr.service.DataDictionaryDirectoryConfigurationService;
import com.jykj.dqm.emr.service.DocumentDirectoryConfigurationService;
import com.jykj.dqm.emr.service.DocumentRuleConfigurationService;
import com.jykj.dqm.emr.service.EmrProjectManagerService;
import com.jykj.dqm.emr.service.RequireProjectDictionaryService;
import com.jykj.dqm.emr.service.RulePermissionConfigurationService;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.metadata.dao.MetadataStructureInfoMapper;
import com.jykj.dqm.metadata.entity.MetadataStructureInfo;
import com.jykj.dqm.metadata.entity.SchemaTable;
import com.jykj.dqm.quality.manager.rulesql.BaseCheckSqlGenerator;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.IdUtils;
import com.jykj.dqm.utils.MapperUtils;
import com.jykj.dqm.utils.RedisUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 文档规则配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/22 15:50:15
 */
@Service
public class DocumentRuleConfigurationServiceImpl extends ServiceImpl<DocumentRuleConfigurationMapper, DocumentRuleConfiguration> implements DocumentRuleConfigurationService {
    @Autowired
    private DocumentDirectoryConfigurationService documentDirectoryConfigurationService;

    @Autowired
    private RequireProjectDictionaryService requireProjectDictionaryService;

    @Autowired
    private RequireProjectDictionaryMapper requireProjectDictionaryMapper;

    @Autowired
    private MetadataStructureInfoMapper metadataStructureInfoMapper;

    @Autowired
    RulePermissionConfigurationService rulePermissionConfigurationService;

    @Autowired
    DataDictionaryDirectoryConfigurationService dataDictionaryDirectoryConfigurationService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private EmrProjectManagerService emrProjectManagerService;

    @Autowired
    private EmrSysUserGroupService emrSysUserGroupService;

    @Override
    public R add(DocumentRuleConfigurationDTO documentRuleConfiguration) {
        addInfo(documentRuleConfiguration);
        this.saveBatch(documentRuleConfiguration.getDocumentRuleConfigurationList());
        return RUtil.success();
    }

    /**
     * 将总的信息添加到每一行中，不用单独存储
     *
     * @param documentRuleConfiguration DocumentRuleConfigurationDTO
     * <AUTHOR>
     */
    private void addInfo(DocumentRuleConfigurationDTO documentRuleConfiguration) {
        for (DocumentRuleConfiguration ruleConfiguration : documentRuleConfiguration.getDocumentRuleConfigurationList()) {
            if (StrUtil.isBlank(ruleConfiguration.getDataSourceId())) {
                ruleConfiguration.setDataSourceId(documentRuleConfiguration.getDataSourceId());
            }
            if (StrUtil.isBlank(ruleConfiguration.getDataSourceId2())) {
                //【 数据质量文档 】——【 质量规则配置 】——数据源需要保持独立
                if (StrUtil.isNotBlank(documentRuleConfiguration.getDataSourceId2())) {
                    ruleConfiguration.setDataSourceId2(documentRuleConfiguration.getDataSourceId2());
                } else {
                    //同时兼容之前的场景
                    ruleConfiguration.setDataSourceId2(documentRuleConfiguration.getDataSourceId());
                }
            }
            ruleConfiguration.setDirectoryCode(documentRuleConfiguration.getDirectoryCode());
            ruleConfiguration.setDirectoryName(documentRuleConfiguration.getDirectoryName());
            ruleConfiguration.setEmrRuleType(documentRuleConfiguration.getEmrRuleType());
            ruleConfiguration.setHeaderName1(documentRuleConfiguration.getHeaderName1());
            ruleConfiguration.setHeaderName2(documentRuleConfiguration.getHeaderName2());
            ruleConfiguration.setProjectId(documentRuleConfiguration.getProjectId());
            if ("1".equals(ruleConfiguration.getTableAndFiledType())) {
                ruleConfiguration.setStructureName1("");
                ruleConfiguration.setStructureName2("");
            }
            if (StrUtil.isNotBlank(ruleConfiguration.getRuleConfigurationRemark())) {
                ruleConfiguration.setRuleConfigurationRemark(ruleConfiguration.getRuleConfigurationRemark());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R update(DocumentRuleConfigurationDTO documentRuleConfiguration) {
        addInfo(documentRuleConfiguration);
        this.updateBatchById(documentRuleConfiguration.getDocumentRuleConfigurationList());
        return RUtil.success();
    }

    @Override
    public R query(DocumentRuleConfigurationQuery documentRuleConfiguration) {
        LambdaQueryWrapper<DocumentRuleConfiguration> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(DocumentRuleConfiguration::getDirectoryCode, documentRuleConfiguration.getDirectoryCode())
                .eq(DocumentRuleConfiguration::getDirectoryName, documentRuleConfiguration.getDirectoryName())
                .eq(DocumentRuleConfiguration::getEmrRuleType, documentRuleConfiguration.getEmrRuleType())
                .eq(DocumentRuleConfiguration::getProjectId, documentRuleConfiguration.getProjectId());
        List<DocumentRuleConfiguration> list = this.list(lambdaQueryWrapper);
        DocumentRuleConfigurationDTO dto = MapperUtils.INSTANCE.map(DocumentRuleConfigurationDTO.class, documentRuleConfiguration);

        //初始化要求项目和医院项目
        RequireProjectDictionaryQuery parma = MapperUtils.INSTANCE.map(RequireProjectDictionaryQuery.class, documentRuleConfiguration);
        List<RequireProjectDictionary> query = requireProjectDictionaryMapper.query(parma);
        if (("整合性".equals(documentRuleConfiguration.getEmrRuleType()) || "及时性".equals(documentRuleConfiguration.getEmrRuleType())) && CollUtil.isNotEmpty(query)) {
            RequireProjectDictionary requireProjectDictionary1 = query.get(0);
            dto.setHeaderName1(requireProjectDictionary1.getHeaderName1());
            dto.setHeaderName2(requireProjectDictionary1.getHeaderName2());
        }
        if (CollUtil.isNotEmpty(list) || CollUtil.isEmpty(query)) {
            if (list.size() > 0) {
                addParentInfo(list, dto);
            }
            return RUtil.success(dto);
        }
        String requireProjectNameStr = Optional.ofNullable(query.get(0)).orElse(new RequireProjectDictionary()).getRequireProjectName();
        if (StrUtil.isNotBlank(requireProjectNameStr)) {
            // 创建一个正则表达式，用于匹配'、'符号，但忽略括号'（'和'）'之间的'、'符号
            /*
            、：这个是被寻找的目标字符'、'。
            (?!...)：这是一个否定前瞻断言，它要求接下来的字符不匹配括号中的正则表达式。
            [^（]*）[^（]*$：这个部分有两部分组成，分别为：
            [^（]*）：匹配不包含'（'的任意字符零或多次，直到'）'。
            [^（]*$：匹配不包含'（'的任意字符零或多次，直到字符串的末尾。
             */
            Pattern pattern = Pattern.compile(Constant.REQUIRE_PROJECT_NAME_SEPARATOR + "(?!([^（]*）[^（]*$))");
            // 使用正则表达式分割字符串
            String[] requireProjectNames = pattern.split(requireProjectNameStr);
            String hospitalProjectNameStr = Optional.ofNullable(query.get(0)).map(RequireProjectDictionary::getHospitalProjectName).orElse("");
            String[] hospitalProjectNames = pattern.split(hospitalProjectNameStr);
            Map<String, List<DocumentRuleConfiguration>> collect = list.stream().collect(Collectors.groupingBy(DocumentRuleConfiguration::getRequiredProject));
            int count = 0;
            String hospitalProjectName;
            boolean isExist = hospitalProjectNames.length > 0 && requireProjectNames.length == hospitalProjectNames.length;
            for (String requireProjectName : requireProjectNames) {
                if (!collect.containsKey(requireProjectName)) {
                    hospitalProjectName = isExist ? hospitalProjectNames[count] : requireProjectName;
                    String nc = requireProjectName.endsWith("NC") ? "1" : "0";
                    list.add(new DocumentRuleConfiguration(IdUtils.getID(), requireProjectName.replace("NC", ""), hospitalProjectName.replace("NC", ""), nc));
                }
                count++;
            }
        }
        if (list.size() > 0) {
            addParentInfo(list, dto);
        }
        return RUtil.success(dto);
    }

    private void addParentInfo(List<DocumentRuleConfiguration> list, DocumentRuleConfigurationDTO dto) {
        DocumentRuleConfiguration documentRuleConfiguration1 = list.get(0);
        dto.setDataSourceId(documentRuleConfiguration1.getDataSourceId());
        //【 数据质量文档 】——【 质量规则配置 】-数据源需要保持独立
        if (StrUtil.isNotBlank(documentRuleConfiguration1.getDataSourceId2())) {
            dto.setDataSourceId2(documentRuleConfiguration1.getDataSourceId2());
        } else {
            //同时兼容之前的场景
            dto.setDataSourceId2(documentRuleConfiguration1.getDataSourceId());
        }
        //dto.setDirectoryCode(documentRuleConfiguration1.getDirectoryCode());
        //dto.setDirectoryName(documentRuleConfiguration1.getDirectoryName());
        //dto.setEmrRuleType(documentRuleConfiguration1.getEmrRuleType());
        //dto.setHeaderName1(documentRuleConfiguration1.getHeaderName1());
        //dto.setHeaderName2(documentRuleConfiguration1.getHeaderName2());
        dto.setDocumentRuleConfigurationList(list);
    }

    private boolean isNeedUserAccount(String userAccount) {
        boolean needUserAccount = true;
        if (StrUtil.isBlank(userAccount)) {
            return false;
        }
        SysConfig sysConfigByName = RedisUtil.getSysConfigByName("emr.admin.role");
        if (sysConfigByName != null && StrUtil.isNotBlank(sysConfigByName.getConfigValue())) {
            String[] admins = sysConfigByName.getConfigValue().split(",");
            SysUser info = sysUserMapper.getUserInfoByLoginId(userAccount);
            if (info == null) {
                throw new BusinessException("用户不存在！");
            }
            List<SysRole> sysRoles = sysUserRoleMapper.getAllRoleInfoList(info.getUserId() + "");
            List<SysRole> roleNames = sysRoles.stream().filter(item -> Arrays.asList(admins).contains(item.getRoleName())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(roleNames)) {
                needUserAccount = false;
            }
        }
        return needUserAccount;
    }

    private boolean isNeedUserAccountNew(String userAccount, String projectId) {
        boolean needUserAccount = true;
        if (StrUtil.isBlank(userAccount) || StrUtil.isBlank(projectId)) {
            return false;
        }
        EmrProjectManager projectManager = emrProjectManagerService.getById(projectId);
        if (projectManager == null) {
            return false;
        }
        String personInCharge = projectManager.getPersonInCharge();
        //数据库中personInCharge字段用逗号分隔
        //String[] personInChargeArr = personInCharge.split(",");
        //List<String> personInChargeList = Arrays.asList(personInChargeArr);
        //数据库中personInCharge字段用json数组
        List<String> personInChargeList = JSONObject.parseArray(personInCharge, String.class);
        //判断personInChargeArr是否包含当前登录用户
        if (personInChargeList.contains(userAccount)) {
            needUserAccount = false;
        }
        return needUserAccount;
    }

    @Override
    public R queryLeftTree(boolean needNotAllocationTask, String levelCode, String userAccount, String projectId) {
        String configType = "0";
        Integer allowLevelCode = PermissionLevelCodeUtil.getLevelCode();
        if (allowLevelCode < Integer.parseInt(levelCode)) {
            throw new BusinessException("权限不足，只能操作" + allowLevelCode + "级文档！");
        }
        //当前类型全部的目录
        List<DocumentDirectoryConfiguration> list = documentDirectoryConfigurationService.list(
                Wrappers.<DocumentDirectoryConfiguration>lambdaQuery()
                        .and(StrUtil.isNotBlank(levelCode),
                                item -> item.le(DocumentDirectoryConfiguration::getLevelCode, levelCode)
                                        .or()
                                        .apply("length(DIRECTORY_CODE) <= {0}", "6"))
                        .orderByAsc(DocumentDirectoryConfiguration::getDirectoryCode)
        );

        //逻辑  所有人的过滤（保留第三级目录自己已分配的）  再加未分配的目录
        //所有人已分配的目录
        List<RulePermissionConfiguration> rulePermissionConfigurations = rulePermissionConfigurationService.list(
                new LambdaQueryWrapper<RulePermissionConfiguration>()
                        .eq(RulePermissionConfiguration::getConfigType, configType)
                        .eq(StrUtil.isNotBlank(projectId), RulePermissionConfiguration::getProjectId, projectId)
                        .le(StrUtil.isNotBlank(levelCode), RulePermissionConfiguration::getLevelCode, levelCode));

        Map<String, RulePermissionConfiguration> allAllocatedDirectoryMap = rulePermissionConfigurations.stream().collect(Collectors.toMap(item -> item.getDirectoryCode() + item.getDirectoryName() + item.getLevelCode(), item -> item, (k1, k2) -> k1));
        List<DocumentDirectoryConfiguration> listFilter = list;
        //保留第三级目录自己 已分配的
        if (!StrUtil.isBlank(userAccount)) {
            Set<String> userAllocatedDirectoryCodeSet = rulePermissionConfigurations.stream().filter(item -> item.getUserAccount().equals(userAccount)).map(item -> item.getDirectoryCode() + item.getDirectoryName() + item.getLevelCode()).collect(Collectors.toSet());
            listFilter = list.stream().filter(item -> item.getDirectoryCode().length() < 6 || userAllocatedDirectoryCodeSet.contains(item.getDirectoryCode() + item.getDirectoryName() + item.getLevelCode())).collect(Collectors.toList());
        }

        listFilter = listFilter.stream().filter(item -> item.getDirectoryCode().length() < 6 || allAllocatedDirectoryMap.containsKey(item.getDirectoryCode() + item.getDirectoryName() + item.getLevelCode())).peek(item -> {
            RulePermissionConfiguration rulePermissionConfiguration = allAllocatedDirectoryMap.get(item.getDirectoryCode() + item.getDirectoryName() + item.getLevelCode());
            if (rulePermissionConfiguration != null) {
                item.setTaskStatus(rulePermissionConfiguration.getTaskStatus());
                item.setPersonInCharge(rulePermissionConfiguration.getUserAccount());
            }
        }).collect(Collectors.toList());

        // 再加未分配的目录
        if (needNotAllocationTask) {
            List<DocumentDirectoryConfiguration> notllocatedDirectoryCodeList = list.stream()
                    .filter(item -> item.getDirectoryCode().length() < 6 || !allAllocatedDirectoryMap.containsKey(item.getDirectoryCode() + item.getDirectoryName() + item.getLevelCode()))
                    .collect(Collectors.toList());
            listFilter.addAll(notllocatedDirectoryCodeList);
        }
        //list去重排序
        listFilter = listFilter.stream().distinct().sorted(Comparator.comparing(DocumentDirectoryConfiguration::getDirectoryCode)).collect(Collectors.toList());

        List<DocumentDirectoryFirst> result = getDocumentDirectorysNew(listFilter);
        return RUtil.success(result);
    }

    @NotNull
    public List<DocumentDirectoryFirst> getDocumentDirectorysNew(List<DocumentDirectoryConfiguration> listFilter) {
        List<DocumentDirectoryConfiguration> firstLevelList = listFilter.stream().filter(directoryConfiguration -> directoryConfiguration.getDirectoryCode().length() <= 2).collect(Collectors.toList());
        List<DocumentDirectoryFirst> documentDirectoryFirstList = new ArrayList<>();
        DocumentDirectoryFirst documentDirectoryFirst;
        List<DocumentDirectorySecond> secondLevels;
        DocumentDirectorySecond documentDirectorySecond;
        Map<String, String> resultMap = getEmrTaskUserMap();
        String personInCharge;
        Map<String, DocumentDirectoryConfiguration> secondMap = listFilter.stream().filter(directoryConfiguration -> directoryConfiguration.getDirectoryCode().length() == 5).distinct().collect(Collectors.toMap(item -> item.getDirectoryCode(), item -> item, (k1, k2) -> k1));
        Map<String, List<DocumentDirectoryConfiguration>> listMap = listFilter.stream().filter(directoryConfiguration -> directoryConfiguration.getDirectoryCode().length() > 5)
                .collect(Collectors.groupingBy(item -> item.getDirectoryCode() + item.getEmrRuleType()));

        Map<String, List<String>> emrRuleTypeMap = listFilter.stream()
                .filter(directoryConfiguration -> directoryConfiguration.getDirectoryCode().length() > 5)
                //.sorted(Comparator.comparing(DocumentDirectoryConfiguration::getSerialNum))
                .sorted((o1, o2) -> {
                    if (StrUtil.isNotBlank(o1.getSerialNum()) && StrUtil.isNotBlank(o2.getSerialNum())) {
                        return o1.getSerialNum().compareTo(o2.getSerialNum());
                    } else {
                        return o1.getDirectoryCode().compareTo(o2.getDirectoryCode());
                    }
                })
                .collect(Collectors.groupingBy(item -> item.getDirectoryCode(),
                        //Collectors.collectingAndThen(Collectors.toList(), list -> list.stream().sorted(Comparator.comparing(DocumentDirectoryConfiguration::getSerialNum)).map(item -> item.getEmrRuleType()).distinct().collect(Collectors.toList()))));
                        Collectors.mapping(DocumentDirectoryConfiguration::getEmrRuleType, Collectors.toList())));
        for (DocumentDirectoryConfiguration directoryConfiguration : firstLevelList) {
            documentDirectoryFirst = new DocumentDirectoryFirst();
            String directoryCode = directoryConfiguration.getDirectoryCode();
            documentDirectoryFirst.setDirectoryCode(directoryCode);
            documentDirectoryFirst.setDirectoryName(directoryConfiguration.getDirectoryName());
            documentDirectoryFirst.setSerialNum(directoryConfiguration.getSerialNum());
            documentDirectoryFirst.setEmrRuleType(directoryConfiguration.getEmrRuleType());
            secondLevels = new ArrayList<>();
            for (DocumentDirectoryConfiguration item : listFilter) {
                if (item.getDirectoryCode().startsWith(directoryCode) && item.getDirectoryCode().length() > 5) {
                    String secondLevelKey = item.getDirectoryCode().substring(0, item.getDirectoryCode().lastIndexOf("."));
                    documentDirectorySecond = new DocumentDirectorySecond();
                    documentDirectorySecond.setDirectoryCode(item.getDirectoryCode());
                    documentDirectorySecond.setDirectoryName(secondMap.get(secondLevelKey).getDirectoryName());
                    documentDirectorySecond.setSerialNum(item.getSerialNum());
                    secondLevels.add(documentDirectorySecond);
                }
            }
            //secondLevels 根据目录编码和目录名称去重
            secondLevels = secondLevels.stream().distinct().collect(Collectors.toList());
            documentDirectoryFirst.setSecondLevels(secondLevels);
            documentDirectoryFirstList.add(documentDirectoryFirst);
        }

        for (DocumentDirectoryFirst directoryFirst : documentDirectoryFirstList) {
            for (DocumentDirectorySecond secondLevel : directoryFirst.getSecondLevels()) {
                //          完整性
                //			 1
                //			 2
                //			一致性
                //			 1
                //			 2
                List<String> emrRuleTypeList = emrRuleTypeMap.get(secondLevel.getDirectoryCode()).stream().distinct().collect(Collectors.toList());
                Map<String, List<DocumentDirectoryConfiguration>> structures;
                Map<String, List<DocumentDirectoryConfiguration>> result = new LinkedHashMap<>();
                for (String emrRuleType : emrRuleTypeList) {
                    List<DocumentDirectoryConfiguration> documentDirectoryConfigurations = listMap.get(secondLevel.getDirectoryCode() + emrRuleType);
                    structures = documentDirectoryConfigurations.stream()
                            //.sorted(Comparator.comparing(DocumentDirectoryConfiguration::getSerialNum))
                            .sorted((o1, o2) -> {
                                if (StrUtil.isNotBlank(o1.getSerialNum()) && StrUtil.isNotBlank(o2.getSerialNum())) {
                                    return o1.getSerialNum().compareTo(o2.getSerialNum());
                                } else {
                                    return o1.getDirectoryCode().compareTo(o2.getDirectoryCode());
                                }
                            })
                            .collect(Collectors.groupingBy(item -> item.getEmrRuleType()));
                    List<DocumentDirectoryConfiguration> configurations = structures.get(emrRuleType);
                    //根据目录编码排序
                    configurations.sort(
                            //Comparator.comparing(DocumentDirectoryConfiguration::getSerialNum)
                            (o1, o2) -> {
                                if (StrUtil.isNotBlank(o1.getSerialNum()) && StrUtil.isNotBlank(o2.getSerialNum())) {
                                    return o1.getSerialNum().compareTo(o2.getSerialNum());
                                } else {
                                    return o1.getDirectoryCode().compareTo(o2.getDirectoryCode());
                                }
                            });
                    for (DocumentDirectoryConfiguration config : configurations) {
                        //设置负责人
                        personInCharge = config.getPersonInCharge();
                        if (StrUtil.isNotBlank(personInCharge)) {
                            config.setPersonInCharge(resultMap.get(personInCharge) + "(" + personInCharge + ")");
                        }
                    }
                    // 将处理后的配置加入到最终结果中
                    result.put(emrRuleType, configurations);
                }
                secondLevel.setThirdLevels(result);
            }
        }

        //过滤没有三级菜单的一二级目录
        //java8实现
        List<DocumentDirectoryFirst> result = documentDirectoryFirstList.stream().filter(directoryFirst -> {
            List<DocumentDirectorySecond> directorySecondList = directoryFirst.getSecondLevels().stream().filter(secondLevel -> !secondLevel.getThirdLevels().isEmpty()).collect(Collectors.toList());
            directoryFirst.setSecondLevels(directorySecondList);
            return !directorySecondList.isEmpty();
        }).collect(Collectors.toList());
        return result;
    }

    private Map<String, String> getEmrTaskUserMap() {
        List<EmrSysUserGroup> mapList = emrSysUserGroupService.queryByGroupName(null, null, null);
        Map<String, String> resultMap = mapList.stream()
                .collect(Collectors.toMap(
                        record -> record.getLoginId(),
                        record -> record.getUserName(),
                        (oldValue, newValue) -> oldValue
                ));
        return resultMap;
    }

    @Override
    public R queryProject(DocumentRuleConfigurationQueryProject documentRuleConfiguration) {
        LambdaQueryWrapper<RequireProjectDictionary> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.like(StrUtil.isNotBlank(documentRuleConfiguration.getProjectName()), RequireProjectDictionary::getRequireProjectName, documentRuleConfiguration.getProjectName());
        List<RequireProjectDictionary> list = requireProjectDictionaryService.list(lambdaQueryWrapper);
        return RUtil.success(list);
    }

    @Override
    public R getStructures(MetadataStructureEmrQuery structureEmrQuery) {
        //MetadataDatasource metadataDatasource = metadataDatasourceMapper.selectById(structureEmrQuery.getDataSourceId());
        LambdaQueryWrapper<MetadataStructureInfo> wrapper = Wrappers.lambdaQuery();
        //if (!"PRESTO".equalsIgnoreCase(metadataDatasource.getDatabaseType())) {
        wrapper.eq(MetadataStructureInfo::getDataSourceId, structureEmrQuery.getDataSourceId());
        //}
        String structureName = structureEmrQuery.getStructureName();
        wrapper.like(StrUtil.isNotBlank(structureName), MetadataStructureInfo::getName, structureName);
        wrapper.orderByAsc(MetadataStructureInfo::getName);
        List<MetadataStructureInfo> metadataStructureInfos = metadataStructureInfoMapper.selectList(wrapper);
        List<MetadataStructureInfo> structureInfos = metadataStructureInfos.stream().filter(item -> {
            if (StrUtil.isBlank(structureName) || !structureName.contains("_")) {
                return true;
            }
            return item.getName().contains(structureName);
        }).collect(Collectors.toList());
        Set<String> dbNames = structureInfos.stream().map(MetadataStructureInfo::getDatabaseName).collect(Collectors.toSet());
        List<StructureBaseInfo> dataBaseInfoList = new ArrayList<>();
        StructureBaseInfo structureBaseInfo = new StructureBaseInfo();
        Map<String, List<String>> structureTableMap;
        Map<String, List<String>> structureViewMap;
        SchemaTable schemaTableTmp;
        Set<String> schemaSet = new HashSet<>();
        List<String> tables = new ArrayList<>();
        List<String> views = new ArrayList<>();
        for (String dbname : dbNames) {
            structureTableMap = new LinkedHashMap<>();
            structureViewMap = new LinkedHashMap<>();
            for (MetadataStructureInfo metadataStructureInfo : structureInfos) {
                schemaSet.add(metadataStructureInfo.getSchema());
                if (dbname.equalsIgnoreCase(metadataStructureInfo.getDatabaseName())) {
                    if ("table".equalsIgnoreCase(metadataStructureInfo.getType())) {
                        collectStructures(structureTableMap, metadataStructureInfo);
                    } else if ("view".equalsIgnoreCase(metadataStructureInfo.getType())) {
                        collectStructures(structureViewMap, metadataStructureInfo);
                    }
                }
            }
            structureBaseInfo.setDataBaseName(dbname);
            for (String schemaTmp : schemaSet) {
                schemaTableTmp = new SchemaTable();
                schemaTableTmp.setSchema(schemaTmp);
                tables.addAll(structureTableMap.getOrDefault(schemaTmp, Collections.emptyList()));
                views.addAll(structureViewMap.getOrDefault(schemaTmp, Collections.emptyList()));
            }
            structureBaseInfo.setTables(tables);
            structureBaseInfo.setViews(views);
            dataBaseInfoList.add(structureBaseInfo);
        }
        return RUtil.success(dataBaseInfoList);
    }

    @Override
    public String getTimeConditionSql(String dbType, String startDate, String endDate) {
        if (StrUtil.isBlank(startDate) || StrUtil.isBlank(endDate)) {
            //获取当前月的起始和结束日期
            startDate = DateUtil.beginOfMonth(new Date()).toDateStr();
            endDate = DateUtil.endOfMonth(new Date()).toDateStr() + " 23:59:59";
        }
        BaseCheckSqlGenerator baseCheckSqlGenerator = new BaseCheckSqlGenerator();
        String startTimeSql = baseCheckSqlGenerator.getSqlDateFunction(dbType, startDate);
        String endTimeSql = baseCheckSqlGenerator.getSqlDateFunction(dbType, endDate);
        return "BETWEEN " + startTimeSql + " AND " + endTimeSql;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R save(DocumentRuleConfigurationDTO documentRuleConfiguration) {
        LambdaQueryWrapper<DocumentRuleConfiguration> removeWrapper = Wrappers.lambdaQuery();
        removeWrapper.eq(DocumentRuleConfiguration::getDirectoryCode, documentRuleConfiguration.getDirectoryCode())
                .eq(DocumentRuleConfiguration::getDirectoryName, documentRuleConfiguration.getDirectoryName())
                .eq(DocumentRuleConfiguration::getProjectId, documentRuleConfiguration.getProjectId())
                .eq(DocumentRuleConfiguration::getEmrRuleType, documentRuleConfiguration.getEmrRuleType());
        this.remove(removeWrapper);
        addInfo(documentRuleConfiguration);
        this.saveBatch(documentRuleConfiguration.getDocumentRuleConfigurationList());
        //更新状态：完成-》已分配
        rulePermissionConfigurationService.update(Wrappers.<RulePermissionConfiguration>lambdaUpdate()
                .set(RulePermissionConfiguration::getTaskStatus, "1")
                .eq(RulePermissionConfiguration::getDirectoryCode, documentRuleConfiguration.getDirectoryCode())
                .eq(RulePermissionConfiguration::getDirectoryName, documentRuleConfiguration.getDirectoryName())
                .eq(RulePermissionConfiguration::getEmrRuleType, documentRuleConfiguration.getEmrRuleType())
                .eq(RulePermissionConfiguration::getConfigType, "0")
                .eq(RulePermissionConfiguration::getProjectId, documentRuleConfiguration.getProjectId())
        );
        return RUtil.success();
    }

    @Override
    public R queryHeaderName(DocumentRuleConfigurationQuery documentRuleConfiguration) {
        //初始化要求项目和医院项目
        RequireProjectDictionaryQuery parma = MapperUtils.INSTANCE.map(RequireProjectDictionaryQuery.class, documentRuleConfiguration);
        List<RequireProjectDictionary> query = requireProjectDictionaryMapper.query(parma);
        return RUtil.success(query);
    }

    @Override
    public R markComplete(DocumentRuleConfigurationQuery documentRuleConfigurationQuery) {
        rulePermissionConfigurationService.update(Wrappers.<RulePermissionConfiguration>lambdaUpdate()
                .set(RulePermissionConfiguration::getTaskStatus, "2")
                .eq(RulePermissionConfiguration::getDirectoryCode, documentRuleConfigurationQuery.getDirectoryCode())
                .eq(RulePermissionConfiguration::getDirectoryName, documentRuleConfigurationQuery.getDirectoryName())
                .eq(StrUtil.isNotBlank(documentRuleConfigurationQuery.getEmrRuleType()), RulePermissionConfiguration::getEmrRuleType, documentRuleConfigurationQuery.getEmrRuleType())
                //.eq(RulePermissionConfiguration::getUserAccount, documentRuleConfigurationQuery.getUserAccount())
                .eq(RulePermissionConfiguration::getConfigType, documentRuleConfigurationQuery.getConfigType())
                .eq(RulePermissionConfiguration::getProjectId, documentRuleConfigurationQuery.getProjectId())
        );
        return RUtil.success();
    }

    private void collectStructures(Map<String, List<String>> structureMap, MetadataStructureInfo metadataStructureInfo) {
        List<String> structures = structureMap.computeIfAbsent(metadataStructureInfo.getSchema(), k -> new LinkedList<>());
        structures.add(metadataStructureInfo.getName());
    }
}
