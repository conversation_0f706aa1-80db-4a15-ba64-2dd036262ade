package com.jykj.dqm.emr.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.emr.dao.DocumentExportRecordErrorDetailMapper;
import com.jykj.dqm.emr.entity.DocumentExportRecordErrorDetail;
import com.jykj.dqm.emr.service.DocumentExportRecordErrorDetailService;
import org.springframework.stereotype.Service;

/**
 * 文档导出异常详情
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/4/25 15:01:24
 */
@Service
public class DocumentExportRecordErrorDetailServiceImpl extends ServiceImpl<DocumentExportRecordErrorDetailMapper, DocumentExportRecordErrorDetail> implements DocumentExportRecordErrorDetailService {
}
