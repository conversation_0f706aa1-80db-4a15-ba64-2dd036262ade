package com.jykj.dqm.emr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfigurationQuery;

import java.util.List;

/**
 * 文档目录配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 15:48:52
 */
public interface DocumentDirectoryConfigurationService extends IService<DocumentDirectoryConfiguration> {
    R add(DocumentDirectoryConfiguration documentDirectoryConfiguration);

    R update(DocumentDirectoryConfiguration documentDirectoryConfiguration);

    R delete(List<Long> ids);

    R query(DocumentDirectoryConfigurationQuery documentDirectoryConfiguration);

    R batchUpdateMy(List<DocumentDirectoryConfiguration> documentDirectoryConfiguration);

    R queryAll(DocumentDirectoryConfigurationQuery documentDirectoryConfiguration);
}
