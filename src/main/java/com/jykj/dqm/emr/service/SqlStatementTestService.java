package com.jykj.dqm.emr.service;

import com.jykj.dqm.common.R;
import com.jykj.dqm.emr.entity.SqlStatementQuery;
import com.jykj.dqm.emr.entity.SqlStatementQueryOneRule;

/**
 * sql语句测试
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/23 14:27:24
 */
public interface SqlStatementTestService {
    R execute(SqlStatementQuery sqlStatementQuery);

    R executeOneRule(SqlStatementQueryOneRule sqlStatementQueryOneRule);

    void asyncExecuteOneRule(SqlStatementQueryOneRule sqlStatementQueryOneRule);

    R getSqlExecStatus(SqlStatementQueryOneRule sqlStatementQueryOneRule);

    R getSqlExecResult(SqlStatementQueryOneRule sqlStatementQueryOneRule);
}
