package com.jykj.dqm.emr.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.auth.dao.SysUserMapper;
import com.jykj.dqm.auth.dao.SysUserRoleMapper;
import com.jykj.dqm.auth.entity.EmrSysUserGroup;
import com.jykj.dqm.auth.entity.SysRole;
import com.jykj.dqm.auth.entity.SysUser;
import com.jykj.dqm.auth.service.EmrSysUserGroupService;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.dao.RulePermissionConfigurationMapper;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryFirst;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryRuleConfiguration;
import com.jykj.dqm.emr.entity.DataDictionaryDirectorySecond;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import com.jykj.dqm.emr.entity.EmrProjectManager;
import com.jykj.dqm.emr.entity.RulePermissionConfiguration;
import com.jykj.dqm.emr.entity.RulePermissionConfigurationSaveDTO;
import com.jykj.dqm.emr.manager.PermissionLevelCodeUtil;
import com.jykj.dqm.emr.service.DataDictionaryDirectoryConfigurationService;
import com.jykj.dqm.emr.service.DataDictionaryDirectoryRuleConfigurationService;
import com.jykj.dqm.emr.service.DocumentDirectoryConfigurationService;
import com.jykj.dqm.emr.service.DocumentRuleConfigurationService;
import com.jykj.dqm.emr.service.EmrProjectManagerService;
import com.jykj.dqm.emr.service.RulePermissionConfigurationService;
import com.jykj.dqm.emr.utils.CommonUtils;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 规则配置权限
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/7/4 16:20:48
 */
@Service
public class RulePermissionConfigurationServiceImpl extends ServiceImpl<RulePermissionConfigurationMapper, RulePermissionConfiguration> implements RulePermissionConfigurationService {
    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private DataDictionaryDirectoryConfigurationService dataDictionaryDirectoryConfigurationService;

    @Autowired
    private DataDictionaryDirectoryRuleConfigurationService dataDictionaryDirectoryRuleConfigurationService;

    @Resource
    private DocumentRuleConfigurationService documentRuleConfigurationService;

    @Autowired
    private EmrSysUserGroupService emrSysUserGroupService;

    @Autowired
    private DocumentDirectoryConfigurationService documentDirectoryConfigurationService;

    @Autowired
    private EmrProjectManagerService emrProjectManagerService;

    @Override
    public R saveRulePermission(RulePermissionConfigurationSaveDTO rulePermissionConfigurationSaveDTO) {
        String userAccount = rulePermissionConfigurationSaveDTO.getUserAccount();
        List<RulePermissionConfiguration> rulePermissionList = rulePermissionConfigurationSaveDTO.getRulePermissionList();
        List<RulePermissionConfiguration> rulePermissionConfigurationsDb = this.list(new LambdaQueryWrapper<RulePermissionConfiguration>()
                .eq(RulePermissionConfiguration::getUserAccount, userAccount)
                .eq(RulePermissionConfiguration::getConfigType, rulePermissionConfigurationSaveDTO.getConfigType())
                .eq(RulePermissionConfiguration::getProjectId, rulePermissionConfigurationSaveDTO.getProjectId())
                .and(!"1".equals(rulePermissionConfigurationSaveDTO.getConfigType()), item -> item.le(!"1".equals(rulePermissionConfigurationSaveDTO.getConfigType()), RulePermissionConfiguration::getLevelCode, rulePermissionConfigurationSaveDTO.getLevelCode())
                        .or()
                        .apply("2".equals(rulePermissionConfigurationSaveDTO.getConfigType()), "length(DIRECTORY_CODE) <= {0}", "6"))
        );
        this.remove(new LambdaQueryWrapper<RulePermissionConfiguration>()
                .eq(RulePermissionConfiguration::getUserAccount, userAccount)
                .eq(RulePermissionConfiguration::getConfigType, rulePermissionConfigurationSaveDTO.getConfigType())
                .eq(RulePermissionConfiguration::getProjectId, rulePermissionConfigurationSaveDTO.getProjectId())
                .and(!"1".equals(rulePermissionConfigurationSaveDTO.getConfigType()), item -> item.le(!"1".equals(rulePermissionConfigurationSaveDTO.getConfigType()), RulePermissionConfiguration::getLevelCode, rulePermissionConfigurationSaveDTO.getLevelCode())
                        .or()
                        .apply("2".equals(rulePermissionConfigurationSaveDTO.getConfigType()), "length(DIRECTORY_CODE) <= {0}", "6"))
        );
        if (rulePermissionList == null || rulePermissionList.size() == 0) {
            return RUtil.success();
        }
        Map<String, RulePermissionConfiguration> permissionConfigurationMap = rulePermissionConfigurationsDb.stream().collect(Collectors.toMap(item -> item.getDirectoryCode() + "_" + item.getDirectoryName() + "_" + item.getEmrRuleType(), item -> item, (k1, k2) -> k1, LinkedHashMap::new));
        rulePermissionList.stream().forEach(item -> {
            item.setUserAccount(userAccount);
            item.setProjectId(rulePermissionConfigurationSaveDTO.getProjectId());

            RulePermissionConfiguration rulePermissionConfiguration = permissionConfigurationMap.get(item.getDirectoryCode() + "_" + item.getDirectoryName() + "_" + item.getEmrRuleType());
            if (rulePermissionConfiguration != null) {
                item.setTaskStatus(rulePermissionConfiguration.getTaskStatus());
            } else {
                //已分配
                item.setTaskStatus("1");
            }
            if (StrUtil.isNotBlank(item.getDirectoryCode()) && item.getDirectoryCode().length() >= 7) {
                item.setLevelCode(item.getDirectoryCode().substring(6, 7));
            }
        });
        this.saveBatch(rulePermissionList);
        return RUtil.success();
    }

    @Override
    public RulePermissionConfigurationSaveDTO getRulePermission(String userAccount, String projectId) {
        List<RulePermissionConfiguration> list = this.list(
                new LambdaQueryWrapper<RulePermissionConfiguration>()
                        .eq(RulePermissionConfiguration::getUserAccount, userAccount)
                        .eq(RulePermissionConfiguration::getProjectId, projectId)
                        .eq(RulePermissionConfiguration::getConfigType, "0")
        );
        RulePermissionConfigurationSaveDTO permissionConfigurationSaveDTO = RulePermissionConfigurationSaveDTO.builder().userAccount(userAccount).build();
        if (list == null || list.size() == 0) {
            return permissionConfigurationSaveDTO;
        }
        permissionConfigurationSaveDTO.setRulePermissionList(list);
        return permissionConfigurationSaveDTO;
    }

    @Override
    public Map<String, Object> getMyTasksAndProgress(String levelCode, String userName, String queryUserAccount, String configType, String projectId) {
        //List<Map<String, Object>> mapList = sysUserMapper.selectMaps(new LambdaQueryWrapper<SysUser>().select(SysUser::getLoginId, SysUser::getUserName).eq(SysUser::getSysId, Constant.SYS_NAME).eq(StrUtil.isNotBlank(queryUserAccount), SysUser::getLoginId, queryUserAccount).like(StrUtil.isNotBlank(userName), SysUser::getUserName, userName));
        List<EmrSysUserGroup> emrSysUserGroups;
        Map<String, Object> resultMap = new HashMap<>();
        List<RulePermissionConfiguration> rulePermissionConfigurations = this.list(
                new LambdaQueryWrapper<RulePermissionConfiguration>()
                        .apply("1=1")
                        .eq(StrUtil.isNotBlank(configType), RulePermissionConfiguration::getConfigType, configType)
                        .eq(StrUtil.isNotBlank(projectId), RulePermissionConfiguration::getProjectId, projectId)
                        .and(StrUtil.isNotBlank(levelCode),
                                item -> item.le(RulePermissionConfiguration::getLevelCode, levelCode)
                                        .or()
                                        .apply("length(DIRECTORY_CODE) <= {0}", "6"))
                        .orderByAsc(RulePermissionConfiguration::getDirectoryCode)
        );

        if ("2".equals(configType)) {
            rulePermissionConfigurations = rulePermissionConfigurations.stream().filter(item -> StrUtil.isNotBlank(item.getLevelCode()) && item.getLevelCode().equals(levelCode)).collect(Collectors.toList());
        }

        Map<String, List<RulePermissionConfiguration>> listMap = rulePermissionConfigurations.stream().collect(Collectors.groupingBy(RulePermissionConfiguration::getUserAccount));
        //configType为null是首页，需要获取全部
        //配置类型：0-文档字典，1-基础数据，2-病历数据，3-质量数据
        long allNum = 0;
        if ("0".equals(configType)) {
            emrSysUserGroups = emrSysUserGroupService.queryByGroupName("病历文档", queryUserAccount, projectId);
            dealUserTaskByConfigType(emrSysUserGroups, configType, listMap, resultMap);
            allNum = documentDirectoryConfigurationService.count(
                    new LambdaQueryWrapper<DocumentDirectoryConfiguration>()
                            .le(DocumentDirectoryConfiguration::getLevelCode, levelCode)
                            .apply("length(DIRECTORY_CODE) > {0}", "5")
            );
        }
        if ("1".equals(configType)) {
            emrSysUserGroups = emrSysUserGroupService.queryByGroupName("基础数据", queryUserAccount, projectId);
            dealUserTaskByConfigType(emrSysUserGroups, configType, listMap, resultMap);
            allNum = dataDictionaryDirectoryConfigurationService.count(
                    new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>().eq(DataDictionaryDirectoryConfiguration::getDirectoryType, configType)
            );
        }
        if ("2".equals(configType)) {
            emrSysUserGroups = emrSysUserGroupService.queryByGroupName("病历数据", queryUserAccount, projectId);
            dealUserTaskByConfigType(emrSysUserGroups, configType, listMap, resultMap);
            allNum = dataDictionaryDirectoryConfigurationService.count(
                    new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>()
                            .eq(DataDictionaryDirectoryConfiguration::getDirectoryType, configType)
                            .apply("length(DIRECTORY_CODE) > {0}", "5")
                            .and(StrUtil.isNotBlank(levelCode),
                                    item -> item.eq(DataDictionaryDirectoryConfiguration::getAssociationLevel, levelCode)
                                            .or()
                                            .apply("length(DIRECTORY_CODE) = {0}", "6")) //01.01.  这种特殊情况
            );
        }
        if ("3".equals(configType)) {
            emrSysUserGroups = emrSysUserGroupService.queryByGroupName("质量数据", queryUserAccount, projectId);
            dealUserTaskByConfigType(emrSysUserGroups, configType, listMap, resultMap);
            allNum = dataDictionaryDirectoryConfigurationService.count(
                    new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>()
                            .isNotNull(DataDictionaryDirectoryConfiguration::getAssociationLevel).ne(DataDictionaryDirectoryConfiguration::getAssociationLevel, "")
                            .le(DataDictionaryDirectoryConfiguration::getAssociationLevel, levelCode)
                            .eq(DataDictionaryDirectoryConfiguration::getDirectoryType, configType)
                            .apply("length(DIRECTORY_CODE) > {0}", "5")
            );
        }
        if (configType == null) {
            emrSysUserGroups = emrSysUserGroupService.queryByGroupName(null, queryUserAccount, projectId);
            if (CollUtil.isNotEmpty(emrSysUserGroups)) {
                emrSysUserGroups = Arrays.asList(emrSysUserGroups.get(0));
            }
            dealUserTaskByConfigType(emrSysUserGroups, "ALL", listMap, resultMap);
        }
        resultMap.put("allNum", allNum);
        return resultMap;

    }

    @Override
    public R queryDirectoryTree(String configType, String userAccount, String levelCode, boolean needNotAllocationTask, String projectId) {
        if ("0".equals(configType)) {
            //补充状态
            return documentRuleConfigurationService.queryLeftTree(needNotAllocationTask, levelCode, userAccount, projectId);
        }
        Integer allowLevelCode = PermissionLevelCodeUtil.getLevelCode();
        if (allowLevelCode < Integer.parseInt(levelCode)) {
            throw new BusinessException("权限不足，只能操作" + allowLevelCode + "级文档！");
        }
        //当前类型全部的目录
        List<DataDictionaryDirectoryConfiguration> list = dataDictionaryDirectoryConfigurationService.list(
                new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>()
                        .eq(DataDictionaryDirectoryConfiguration::getDirectoryType, configType)
                        .and(StrUtil.isNotBlank(levelCode),
                                item -> item.isNotNull(DataDictionaryDirectoryConfiguration::getAssociationLevel).ne(DataDictionaryDirectoryConfiguration::getAssociationLevel, "").le(!"1".equals(configType), DataDictionaryDirectoryConfiguration::getAssociationLevel, levelCode)
                                        .or()
                                        .apply("length(DIRECTORY_CODE) <= {0}", "6"))
        );

        //逻辑  所有人的过滤（保留第三级目录自己已分配的）  再加未分配的目录
        //所有人已分配的目录
        List<RulePermissionConfiguration> rulePermissionConfigurations = this.list(
                new LambdaQueryWrapper<RulePermissionConfiguration>()
                        .eq(RulePermissionConfiguration::getConfigType, configType)
                        .eq(RulePermissionConfiguration::getProjectId, projectId)
                        .and(item -> item.le(StrUtil.isNotBlank(levelCode), RulePermissionConfiguration::getLevelCode, levelCode).or().apply(StrUtil.isNotBlank(levelCode), "length(DIRECTORY_CODE) <= {0}", "6"))
        );
        Map<String, RulePermissionConfiguration> allAllocatedDirectoryMap = rulePermissionConfigurations.stream().collect(Collectors.toMap(item -> item.getDirectoryCode() + item.getDirectoryName(), item -> item, (k1, k2) -> k1));
        if ("2".equals(configType)) {
            list = list.stream().filter(item -> item.getDirectoryCode().length() <= 6 || (StrUtil.isNotBlank(item.getAssociationLevel()) && item.getAssociationLevel().equals(levelCode))).collect(Collectors.toList());
        }
        List<DataDictionaryDirectoryConfiguration> listFilter = list;
        //保留第三级目录自己 已分配的
        if (StrUtil.isNotBlank(userAccount)) {
            Set<String> userAllocatedDirectoryCodeSet = rulePermissionConfigurations.stream().filter(item -> item.getUserAccount().equals(userAccount)).map(item -> item.getDirectoryCode() + item.getDirectoryName()).collect(Collectors.toSet());
            if ("1".equals(configType)) {
                listFilter = list.stream().filter(item -> userAllocatedDirectoryCodeSet.contains(item.getDirectoryCode() + item.getDirectoryName())).collect(Collectors.toList());
            } else {
                listFilter = list.stream().filter(item -> item.getDirectoryCode().length() < 6 || userAllocatedDirectoryCodeSet.contains(item.getDirectoryCode() + item.getDirectoryName())).collect(Collectors.toList());
            }
        }

        listFilter = listFilter.stream().filter(item -> item.getDirectoryCode().length() < 6 || allAllocatedDirectoryMap.containsKey(item.getDirectoryCode() + item.getDirectoryName())).peek(item -> {
            RulePermissionConfiguration rulePermissionConfiguration = allAllocatedDirectoryMap.get(item.getDirectoryCode() + item.getDirectoryName());
            if (rulePermissionConfiguration != null) {
                item.setTaskStatus(rulePermissionConfiguration.getTaskStatus());
                item.setPersonInCharge(rulePermissionConfiguration.getUserAccount());
            }
        }).collect(Collectors.toList());

        // 再加未分配的目录
        if (needNotAllocationTask) {
            List<DataDictionaryDirectoryConfiguration> notllocatedDirectoryCodeList;
            if ("1".equals(configType)) {
                notllocatedDirectoryCodeList = list.stream()
                        .filter(item -> !allAllocatedDirectoryMap.containsKey(item.getDirectoryCode() + item.getDirectoryName()))
                        .collect(Collectors.toList());
            } else {
                notllocatedDirectoryCodeList = list.stream()
                        .filter(item -> item.getDirectoryCode().length() < 6 || !allAllocatedDirectoryMap.containsKey(item.getDirectoryCode() + item.getDirectoryName()))
                        .collect(Collectors.toList());
            }
            listFilter.addAll(notllocatedDirectoryCodeList);
        }

        //list去重排序
        listFilter = listFilter.stream().distinct().collect(Collectors.toList());
        CommonUtils.sortByDirectoryCode(listFilter);
        //配置界面才添加规则配置（SQL+关联）
        if (!needNotAllocationTask) {
            dealRuleConfig(configType, levelCode, list, projectId, null);
        }
        if ("1".equals(configType)) {
            Map<String, String> resultMap = getEmrTaskUserMap();
            listFilter.stream().forEach(item -> {
                String personInCharge = item.getPersonInCharge();
                if (StrUtil.isNotBlank(personInCharge)) {
                    item.setPersonInCharge(resultMap.get(personInCharge) + "(" + personInCharge + ")");
                }
            });
            return RUtil.success(listFilter);
        }
        //另外两种需要弄成树形
        Map<String, List<RulePermissionConfiguration>> listMap = (rulePermissionConfigurations == null ? new ArrayList<RulePermissionConfiguration>() : rulePermissionConfigurations).stream().collect(Collectors.groupingBy(task -> task.getDirectoryCode() + "_" + task.getDirectoryName()));
        List<DataDictionaryDirectoryConfiguration> firstLevelList = listFilter.stream().filter(directoryConfiguration -> directoryConfiguration.getDirectoryCode().length() <= 2).collect(Collectors.toList());
        List<DataDictionaryDirectoryFirst> documentDirectoryFirstList = new ArrayList<>();
        DataDictionaryDirectoryFirst documentDirectoryFirst;
        List<DataDictionaryDirectorySecond> secondLevels;
        DataDictionaryDirectorySecond documentDirectorySecond;
        Map<String, List<DataDictionaryDirectoryConfiguration>> map = new LinkedHashMap<>();

        Map<String, String> resultMap = getEmrTaskUserMap();
        List<RulePermissionConfiguration> permissionConfigurations;
        String personInCharge;
        for (DataDictionaryDirectoryConfiguration directoryConfiguration : firstLevelList) {
            documentDirectoryFirst = new DataDictionaryDirectoryFirst();
            String directoryCode = directoryConfiguration.getDirectoryCode();
            documentDirectoryFirst.setDirectoryCode(directoryCode);
            documentDirectoryFirst.setDirectoryName(directoryConfiguration.getDirectoryName());
            secondLevels = new ArrayList<>();
            for (DataDictionaryDirectoryConfiguration item : listFilter) {
                if (!item.getDirectoryCode().equals(directoryCode) && item.getDirectoryCode().startsWith(directoryCode)) {
                    if (item.getDirectoryCode().length() <= 5) {
                        documentDirectorySecond = new DataDictionaryDirectorySecond();
                        documentDirectorySecond.setDirectoryCode(item.getDirectoryCode());
                        documentDirectorySecond.setDirectoryName(item.getDirectoryName());
                        secondLevels.add(documentDirectorySecond);
                    } else {
                        //01.03.3
                        item.setTaskStatus("0");
                        permissionConfigurations = listMap.get(item.getDirectoryCode() + "_" + item.getDirectoryName());
                        if (permissionConfigurations != null && permissionConfigurations.get(0) != null) {
                            personInCharge = permissionConfigurations.get(0).getUserAccount();
                            String taskStatus = permissionConfigurations.get(0).getTaskStatus();
                            item.setTaskStatus(StrUtil.isBlank(taskStatus) ? "0" : taskStatus);
                            item.setPersonInCharge(resultMap.get(personInCharge) + "(" + personInCharge + ")");
                        }
                        String secondLevelKey = item.getDirectoryCode().substring(0, 5);
                        List<DataDictionaryDirectoryConfiguration> structures = map.computeIfAbsent(secondLevelKey, k -> new LinkedList<>());
                        structures.add(item);
                    }
                }
            }
            documentDirectoryFirst.setSecondLevels(secondLevels);
            documentDirectoryFirstList.add(documentDirectoryFirst);
        }

        for (DataDictionaryDirectoryFirst directoryFirst : documentDirectoryFirstList) {
            for (DataDictionaryDirectorySecond secondLevel : directoryFirst.getSecondLevels()) {
                secondLevel.setThirdLevels(map.getOrDefault(secondLevel.getDirectoryCode(), new ArrayList<>()));
            }
        }

        //过滤没有三级菜单的一二级目录
        List<DataDictionaryDirectoryFirst> result = documentDirectoryFirstList.stream().filter(directoryFirst -> {
            List<DataDictionaryDirectorySecond> directorySecondList = directoryFirst.getSecondLevels().stream().filter(secondLevel -> !secondLevel.getThirdLevels().isEmpty()).collect(Collectors.toList());
            directoryFirst.setSecondLevels(directorySecondList);
            return !directorySecondList.isEmpty();
        }).collect(Collectors.toList());
        return RUtil.success(result);
    }

    @Override
    public R getMedicaRecordsOrQualityRule(String configType, String directoryCode, String userAccount, String levelCode, String projectId) {
        //当前类型全部的目录
        List<DataDictionaryDirectoryConfiguration> list = dataDictionaryDirectoryConfigurationService.list(
                new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>()
                        .eq(DataDictionaryDirectoryConfiguration::getDirectoryType, configType)
                        .likeRight(StrUtil.isNotBlank(directoryCode), DataDictionaryDirectoryConfiguration::getDirectoryCode, directoryCode)
                        .and(StrUtil.isNotBlank(levelCode),
                                item -> item.le(!"1".equals(configType), DataDictionaryDirectoryConfiguration::getAssociationLevel, levelCode)
                                        .or()
                                        .apply("length(DIRECTORY_CODE) = {0}", "6"))
        );

        //所有人已分配的目录
        List<RulePermissionConfiguration> rulePermissionConfigurations = this.list(
                new LambdaQueryWrapper<RulePermissionConfiguration>()
                        .eq(RulePermissionConfiguration::getConfigType, configType)
                        .likeRight(StrUtil.isNotBlank(directoryCode), RulePermissionConfiguration::getDirectoryCode, directoryCode)
                        .eq(RulePermissionConfiguration::getProjectId, projectId)
                        .and(StrUtil.isNotBlank(levelCode) && !"1".equals(configType),
                                item -> item.le(RulePermissionConfiguration::getLevelCode, levelCode)
                                        .or()
                                        .apply("length(DIRECTORY_CODE) = {0}", "6"))
        );
        Map<String, RulePermissionConfiguration> allAllocatedDirectoryMap = rulePermissionConfigurations.stream().collect(Collectors.toMap(item -> item.getDirectoryCode() + item.getDirectoryName(), item -> item, (k1, k2) -> k1));
        if ("2".equals(configType)) {
            list = list.stream().filter(item -> item.getDirectoryCode().length() < 6 || (StrUtil.isNotBlank(item.getAssociationLevel()) && item.getAssociationLevel().equals(levelCode))).collect(Collectors.toList());
        }
        List<DataDictionaryDirectoryConfiguration> listFilter = list;
        if (StrUtil.isNotBlank(userAccount)) {
            Set<String> userAllocatedDirectoryCodeSet = rulePermissionConfigurations.stream().filter(item -> item.getUserAccount().equals(userAccount)).map(item -> item.getDirectoryCode() + item.getDirectoryName()).collect(Collectors.toSet());
            listFilter = list.stream().filter(item -> userAllocatedDirectoryCodeSet.contains(item.getDirectoryCode() + item.getDirectoryName())).collect(Collectors.toList());
        }

        listFilter = listFilter.stream().filter(item -> allAllocatedDirectoryMap.containsKey(item.getDirectoryCode() + item.getDirectoryName())).peek(item -> {
            RulePermissionConfiguration rulePermissionConfiguration = allAllocatedDirectoryMap.get(item.getDirectoryCode() + item.getDirectoryName());
            if (rulePermissionConfiguration != null) {
                item.setTaskStatus(rulePermissionConfiguration.getTaskStatus());
                item.setPersonInCharge(rulePermissionConfiguration.getUserAccount());
            }
        }).collect(Collectors.toList());

        CommonUtils.sortByDirectoryCode(listFilter);

        //配置界面才添加规则配置（SQL+关联）
        dealRuleConfig(configType, levelCode, list, projectId, directoryCode);
        return RUtil.success(listFilter);
    }

    private void dealRuleConfig(String configType, String levelCode, List<DataDictionaryDirectoryConfiguration> list, String projectId, String directoryCode) {
        List<DataDictionaryDirectoryRuleConfiguration> dataDictionaryDirectoryRuleConfigurations = dataDictionaryDirectoryRuleConfigurationService.list(
                new LambdaQueryWrapper<DataDictionaryDirectoryRuleConfiguration>()
                        .eq(DataDictionaryDirectoryRuleConfiguration::getDirectoryType, configType)
                        .eq(DataDictionaryDirectoryRuleConfiguration::getProjectId, projectId)
                        .likeRight(StrUtil.isNotBlank(directoryCode), DataDictionaryDirectoryRuleConfiguration::getDirectoryCode, directoryCode)
                        .and(StrUtil.isNotBlank(levelCode),
                                item -> item.le(!"1".equals(configType), DataDictionaryDirectoryRuleConfiguration::getAssociationLevel, levelCode)
                                        .or()
                                        .apply("length(DIRECTORY_CODE) <= {0}", "6"))
        );
        Map<String, DataDictionaryDirectoryRuleConfiguration> directoryRuleConfigurationMap = dataDictionaryDirectoryRuleConfigurations.stream().collect(Collectors.toMap(item -> (item.getDirectoryCode() + "_" + item.getDirectoryName()).trim(), item -> item, (k1, k2) -> k1));
        DataDictionaryDirectoryRuleConfiguration dataDictionaryDirectoryRuleConfiguration;
        for (DataDictionaryDirectoryConfiguration directoryConfiguration : list) {
            dataDictionaryDirectoryRuleConfiguration = directoryRuleConfigurationMap.get((directoryConfiguration.getDirectoryCode() + "_" + directoryConfiguration.getDirectoryName()).trim());
            dataDictionaryDirectoryRuleConfiguration = dataDictionaryDirectoryRuleConfiguration == null ? new DataDictionaryDirectoryRuleConfiguration() : dataDictionaryDirectoryRuleConfiguration;
            //directoryConfiguration设置这些属性associatedType、conditionalAssociatedType、whetherCrossDbQuery、crossDbQueryDataSourceId、dataSourceId、conditionalDataSourceId、dataSql、conditionalDataSql、associatedList、conditionalAssociatedList、taskStatus
            directoryConfiguration.setAssociatedType(dataDictionaryDirectoryRuleConfiguration.getAssociatedType());
            directoryConfiguration.setConditionalAssociatedType(dataDictionaryDirectoryRuleConfiguration.getConditionalAssociatedType());
            directoryConfiguration.setWhetherCrossDbQuery(dataDictionaryDirectoryRuleConfiguration.getWhetherCrossDbQuery());
            directoryConfiguration.setCrossDbQueryDataSourceId(dataDictionaryDirectoryRuleConfiguration.getCrossDbQueryDataSourceId());
            directoryConfiguration.setDataSourceId(dataDictionaryDirectoryRuleConfiguration.getDataSourceId());
            directoryConfiguration.setConditionalDataSourceId(dataDictionaryDirectoryRuleConfiguration.getConditionalDataSourceId());
            directoryConfiguration.setDataSql(dataDictionaryDirectoryRuleConfiguration.getDataSql());
            directoryConfiguration.setConditionalDataSql(dataDictionaryDirectoryRuleConfiguration.getConditionalDataSql());
            directoryConfiguration.setAssociatedList(dataDictionaryDirectoryRuleConfiguration.getAssociatedList());
            directoryConfiguration.setConditionalAssociatedList(dataDictionaryDirectoryRuleConfiguration.getConditionalAssociatedList());
            //directoryConfiguration.setTaskStatus(dataDictionaryDirectoryRuleConfiguration.getTaskStatus());
        }
    }

    private Map<String, String> getUserMap() {
        List<Map<String, Object>> mapList = sysUserMapper.selectMaps(new LambdaQueryWrapper<SysUser>().select(SysUser::getLoginId, SysUser::getUserName).eq(SysUser::getSysId, Constant.SYS_NAME));
        Map<String, String> resultMap = mapList.stream().collect(Collectors.toMap(record -> (String) record.get("LOGIN_ID"), record -> (String) record.get("USER_NAME"), (oldValue, newValue) -> oldValue));
        return resultMap;
    }

    private Map<String, String> getEmrTaskUserMap() {
        List<EmrSysUserGroup> mapList = emrSysUserGroupService.queryByGroupName(null, null, null);
        Map<String, String> resultMap = mapList.stream()
                .collect(Collectors.toMap(
                        record -> record.getLoginId(),
                        record -> record.getUserName(),
                        (oldValue, newValue) -> oldValue
                ));
        return resultMap;
    }

    @Override
    public RulePermissionConfigurationSaveDTO getTask(String configType, String userAccount, String levelCode, String directoryCode, String projectId) {
        List<RulePermissionConfiguration> list = this.list(
                new LambdaQueryWrapper<RulePermissionConfiguration>()
                        .eq(RulePermissionConfiguration::getUserAccount, userAccount)
                        .eq(RulePermissionConfiguration::getConfigType, configType)
                        .eq(RulePermissionConfiguration::getProjectId, projectId)
                        .likeRight(StrUtil.isNotBlank(directoryCode), RulePermissionConfiguration::getDirectoryCode, directoryCode)
                        .and(StrUtil.isNotBlank(levelCode),
                                item -> item.le(RulePermissionConfiguration::getLevelCode, levelCode)
                                        .or()
                                        .apply("length(DIRECTORY_CODE) <= {0}", "6")));
        RulePermissionConfigurationSaveDTO permissionConfigurationSaveDTO = RulePermissionConfigurationSaveDTO.builder().userAccount(userAccount).build();
        if (list == null || list.size() == 0) {
            return permissionConfigurationSaveDTO;
        }
        permissionConfigurationSaveDTO.setRulePermissionList(list);
        return permissionConfigurationSaveDTO;
    }

    private boolean isNeedUserAccount(String userAccount) {
        boolean needUserAccount = true;
        if (StrUtil.isBlank(userAccount)) {
            return false;
        }
        SysConfig sysConfigByName = RedisUtil.getSysConfigByName("emr.admin.role");
        if (sysConfigByName != null && StrUtil.isNotBlank(sysConfigByName.getConfigValue())) {
            String[] admins = sysConfigByName.getConfigValue().split(",");
            SysUser info = sysUserMapper.getUserInfoByLoginId(userAccount);
            if (info == null) {
                throw new BusinessException("用户不存在！");
            }
            List<SysRole> sysRoles = sysUserRoleMapper.getAllRoleInfoList(info.getUserId() + "");
            List<SysRole> roleNames = sysRoles.stream().filter(item -> Arrays.asList(admins).contains(item.getRoleName())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(roleNames)) {
                needUserAccount = false;
            }
        }
        return needUserAccount;
    }

    private boolean isNeedUserAccountNew(String userAccount, String projectId) {
        boolean needUserAccount = true;
        if (StrUtil.isBlank(userAccount) || StrUtil.isBlank(projectId)) {
            return false;
        }
        EmrProjectManager projectManager = emrProjectManagerService.getById(projectId);
        if (projectManager == null) {
            return false;
        }
        String personInCharge = projectManager.getPersonInCharge();
        //数据库中personInCharge字段用逗号分隔
        //String[] personInChargeArr = personInCharge.split(",");
        //List<String> personInChargeList = Arrays.asList(personInChargeArr);
        //数据库中personInCharge字段用json数组
        List<String> personInChargeList = JSONObject.parseArray(personInCharge, String.class);
        //判断personInChargeArr是否包含当前登录用户
        if (personInChargeList.contains(userAccount)) {
            needUserAccount = false;
        }
        return needUserAccount;
    }


    private static void dealUserTaskByConfigType(List<EmrSysUserGroup> list, String configType, Map<String, List<RulePermissionConfiguration>> listMap, Map<String, Object> resultMap) {
        String userAccount;
        long allocatedNum;
        long finishedNum;
        Map<String, Object> result;
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (EmrSysUserGroup emrSysUserGroup : list) {
            result = new HashMap<>();
            userAccount = emrSysUserGroup.getLoginId();
            allocatedNum = 0;
            finishedNum = 0;
            //已分配+已完成+级别
            result.put("loginId", userAccount);
            result.put("userName", emrSysUserGroup.getUserName());
            result.put("gender", emrSysUserGroup.getGender());
            List<RulePermissionConfiguration> permissionConfigurations = listMap.get(userAccount);
            if (permissionConfigurations != null && permissionConfigurations.size() > 0) {
                for (RulePermissionConfiguration rulePermissionConfiguration : permissionConfigurations) {
                    //保证是同一类型configType的任务，其中all不判断相等
                    if (!"ALL".equals(configType) && !configType.equals(rulePermissionConfiguration.getConfigType())) {
                        continue;
                    }
                    allocatedNum++;
                    if ("2".equals(rulePermissionConfiguration.getTaskStatus())) {
                        finishedNum++;
                    }
                }
            }
            result.put("allocatedNum", allocatedNum);
            result.put("finishedNum", finishedNum);
            resultList.add(result);
        }
        resultMap.put(configType, resultList);
    }
}
