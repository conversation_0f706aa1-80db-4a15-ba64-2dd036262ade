package com.jykj.dqm.emr.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.dao.DataDictionaryDirectoryRuleConfigurationMapper;
import com.jykj.dqm.emr.entity.DataDictionaryAssociated;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryRuleConfiguration;
import com.jykj.dqm.emr.entity.RulePermissionConfiguration;
import com.jykj.dqm.emr.service.DataDictionaryAssociatedService;
import com.jykj.dqm.emr.service.DataDictionaryDirectoryRuleConfigurationService;
import com.jykj.dqm.emr.service.RulePermissionConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_DATA_DICTIONARY_DIRECTORY_RULE_CONFIGURATION(数据字典规则配置)】的数据库操作Service实现
 * @createDate 2024-04-10 17:57:26
 */
@Service
public class DataDictionaryDirectoryRuleConfigurationServiceImpl extends ServiceImpl<DataDictionaryDirectoryRuleConfigurationMapper, DataDictionaryDirectoryRuleConfiguration>
        implements DataDictionaryDirectoryRuleConfigurationService {
    @Autowired
    private DataDictionaryAssociatedService dataDictionaryAssociatedService;

    @Autowired
    private RulePermissionConfigurationService rulePermissionConfigurationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R batchUpdateBase(List<DataDictionaryDirectoryRuleConfiguration> dataDictionaryDirectoryConfigurations) {
        if (CollUtil.isEmpty(dataDictionaryDirectoryConfigurations)) {
            return RUtil.success();
        }
        for (DataDictionaryDirectoryRuleConfiguration dataDictionaryDirectoryConfiguration : dataDictionaryDirectoryConfigurations) {
            String associatedType = dataDictionaryDirectoryConfiguration.getAssociatedType();
            if ("1".equals(associatedType) && CollUtil.isNotEmpty(dataDictionaryDirectoryConfiguration.getAssociatedList())) {
                dataDictionaryDirectoryConfiguration.setDataSourceId("");
                dataDictionaryDirectoryConfiguration.setDataSql("");
            }
            String conditionalAssociatedType = dataDictionaryDirectoryConfiguration.getConditionalAssociatedType();
            if ("1".equals(conditionalAssociatedType) && CollUtil.isNotEmpty(dataDictionaryDirectoryConfiguration.getConditionalAssociatedList())) {
                dataDictionaryDirectoryConfiguration.setConditionalDataSourceId("");
                dataDictionaryDirectoryConfiguration.setConditionalDataSql("");
            }
            //更新状态：完成-》已分配
            rulePermissionConfigurationService.update(Wrappers.<RulePermissionConfiguration>lambdaUpdate()
                    .set(RulePermissionConfiguration::getTaskStatus, "1")
                    .eq(RulePermissionConfiguration::getDirectoryCode, dataDictionaryDirectoryConfiguration.getDirectoryCode())
                    .eq(RulePermissionConfiguration::getDirectoryName, dataDictionaryDirectoryConfiguration.getDirectoryName())
                    .eq(RulePermissionConfiguration::getConfigType, dataDictionaryDirectoryConfiguration.getDirectoryType())
                    .eq(RulePermissionConfiguration::getProjectId, dataDictionaryDirectoryConfiguration.getProjectId())
            );
        }
        //只更新修改的
        this.saveOrUpdateBatch(dataDictionaryDirectoryConfigurations);
        for (DataDictionaryDirectoryRuleConfiguration dataDictionaryDirectoryConfiguration : dataDictionaryDirectoryConfigurations) {
            if (!"1".equals(dataDictionaryDirectoryConfiguration.getDirectoryType()) && dataDictionaryDirectoryConfiguration.getDirectoryCode().length() <= 5) {
                continue;
            }
            dataDictionaryAssociatedService.remove(new LambdaQueryWrapper<DataDictionaryAssociated>().eq(DataDictionaryAssociated::getDirectoryId, dataDictionaryDirectoryConfiguration.getId()));
            addAssociated(dataDictionaryDirectoryConfiguration);
        }
        return RUtil.success();
    }

    /**
     * 添加关联关系
     *
     * @param dataDictionaryDirectoryConfiguration DataDictionaryDirectoryConfiguration
     * <AUTHOR>
     */
    private void addAssociated(DataDictionaryDirectoryRuleConfiguration dataDictionaryDirectoryConfiguration) {
        //如果关联类型（0：编辑SQL，1：选择数据及运算公式）是1时，需要同步新增表
        String associatedType = dataDictionaryDirectoryConfiguration.getAssociatedType();
        if ("1".equals(associatedType) && CollUtil.isNotEmpty(dataDictionaryDirectoryConfiguration.getAssociatedList())) {
            List<DataDictionaryAssociated> associateds = dataDictionaryDirectoryConfiguration.getAssociatedList().stream().peek(item -> {
                item.setDataType("0");
                item.setDirectoryId(dataDictionaryDirectoryConfiguration.getId());
            }).collect(Collectors.toList());
            dataDictionaryAssociatedService.saveBatch(associateds);
        }
        String conditionalAssociatedType = dataDictionaryDirectoryConfiguration.getConditionalAssociatedType();
        if ("1".equals(conditionalAssociatedType) && CollUtil.isNotEmpty(dataDictionaryDirectoryConfiguration.getConditionalAssociatedList())) {
            List<DataDictionaryAssociated> associateds = dataDictionaryDirectoryConfiguration.getConditionalAssociatedList().stream().peek(item -> {
                item.setDataType("1");
                item.setDirectoryId(dataDictionaryDirectoryConfiguration.getId());
            }).collect(Collectors.toList());
            dataDictionaryAssociatedService.saveBatch(associateds);
        }
    }

    @Override
    public R getOneMedicaRecordsOrQuality(String configType, String directoryCode, String directoryName, String projectId) {
        DataDictionaryDirectoryRuleConfiguration directoryConfiguration = this.getOne(
                new LambdaQueryWrapper<DataDictionaryDirectoryRuleConfiguration>()
                        .eq(DataDictionaryDirectoryRuleConfiguration::getDirectoryType, configType)
                        .eq(DataDictionaryDirectoryRuleConfiguration::getDirectoryCode, directoryCode)
                        .eq(DataDictionaryDirectoryRuleConfiguration::getDirectoryName, directoryName)
                        .eq(DataDictionaryDirectoryRuleConfiguration::getProjectId, projectId)
        );
        if (directoryConfiguration == null) {
            DataDictionaryDirectoryRuleConfiguration dataDictionaryDirectoryRuleConfiguration = new DataDictionaryDirectoryRuleConfiguration();
            dataDictionaryDirectoryRuleConfiguration.setDirectoryType(configType);
            dataDictionaryDirectoryRuleConfiguration.setDirectoryCode(directoryCode);
            dataDictionaryDirectoryRuleConfiguration.setDirectoryName(directoryName);
            dataDictionaryDirectoryRuleConfiguration.setProjectId(projectId);
            return RUtil.success(dataDictionaryDirectoryRuleConfiguration);
        }
        List<DataDictionaryAssociated> dataDictionaryAssociateds = dataDictionaryAssociatedService.list(new LambdaQueryWrapper<DataDictionaryAssociated>().eq(DataDictionaryAssociated::getDirectoryId, directoryConfiguration.getId()));
        directoryConfiguration.setAssociatedList(dataDictionaryAssociateds.stream().filter(item -> "0".equals(item.getDataType())).collect(Collectors.toList()));
        directoryConfiguration.setConditionalAssociatedList(dataDictionaryAssociateds.stream().filter(item -> "1".equals(item.getDataType())).collect(Collectors.toList()));
        return RUtil.success(directoryConfiguration);
    }
}
