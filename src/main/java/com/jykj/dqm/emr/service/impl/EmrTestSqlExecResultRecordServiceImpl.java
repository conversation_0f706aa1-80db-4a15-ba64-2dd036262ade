package com.jykj.dqm.emr.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.emr.dao.EmrTestSqlExecResultRecordMapper;
import com.jykj.dqm.emr.entity.EmrTestSqlExecResultRecord;
import com.jykj.dqm.emr.service.EmrTestSqlExecResultRecordService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_TEST_SQL_EXEC_RESULT_RECORD(测试SQL执行结果（只保留一次数据）)】的数据库操作Service实现
 * @createDate 2024-03-29 15:19:03
 */
@Service
public class EmrTestSqlExecResultRecordServiceImpl extends ServiceImpl<EmrTestSqlExecResultRecordMapper, EmrTestSqlExecResultRecord>
        implements EmrTestSqlExecResultRecordService {

}
