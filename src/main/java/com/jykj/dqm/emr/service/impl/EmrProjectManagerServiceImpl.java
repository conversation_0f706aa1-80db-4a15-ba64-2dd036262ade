package com.jykj.dqm.emr.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.auth.dao.SysRoleMapper;
import com.jykj.dqm.auth.dao.SysUserMapper;
import com.jykj.dqm.auth.dao.SysUserRoleMapper;
import com.jykj.dqm.auth.entity.EmrSysUserGroup;
import com.jykj.dqm.auth.entity.SysRole;
import com.jykj.dqm.auth.entity.SysUser;
import com.jykj.dqm.auth.entity.SysUserRole;
import com.jykj.dqm.auth.service.EmrSysUserGroupService;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialTaskAllocation;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialTaskAllocationService;
import com.jykj.dqm.emr.dao.EmrProjectManagerMapper;
import com.jykj.dqm.emr.entity.EmrProjectManager;
import com.jykj.dqm.emr.entity.EmrProjectManagerQuery;
import com.jykj.dqm.emr.entity.EmrProjectManagerVO;
import com.jykj.dqm.emr.entity.LevelDictionary;
import com.jykj.dqm.emr.entity.RulePermissionConfiguration;
import com.jykj.dqm.emr.service.EmrProjectManagerService;
import com.jykj.dqm.emr.service.LevelDictionaryService;
import com.jykj.dqm.emr.service.RulePermissionConfigurationService;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.MapperUtils;
import com.jykj.dqm.utils.NumberMyUtil;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.StpUtilMy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_PROJECT_MANAGER(项目管理)】的数据库操作Service实现
 * @createDate 2024-04-08 14:19:41
 */
@Service
public class EmrProjectManagerServiceImpl extends ServiceImpl<EmrProjectManagerMapper, EmrProjectManager>
        implements EmrProjectManagerService {
    @Autowired
    private RulePermissionConfigurationService rulePermissionConfigurationService;

    @Autowired
    private EmpiricalMaterialTaskAllocationService empiricalMaterialTaskAllocationService;

    @Autowired
    private LevelDictionaryService levelDictionaryService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private EmrSysUserGroupService emrSysUserGroupService;

    @Override
    public R addProject(EmrProjectManager projectManager) {
        this.save(projectManager);
        return RUtil.success("新增项目成功");
    }

    @Override
    public R updateProject(EmrProjectManager projectManager) {
        //判断项目成员是否有变动，如果有变动，要看是否已经分配了任务，如果已经分配了任务，则不能修改成员
        EmrProjectManager emrProjectManagerDB = this.getById(projectManager.getId());
        if (!projectManager.getProjectMembers().equals(emrProjectManagerDB.getProjectMembers())) {
            String projectMembers = projectManager.getProjectMembers();
            String projectMembersDB = emrProjectManagerDB.getProjectMembers();
            if (StrUtil.isNotBlank(projectMembers) && StrUtil.isNotBlank(projectMembersDB)) {
                JSONArray jsonArray = JSONObject.parseArray(projectMembers);
                JSONArray jsonArrayDB = JSONObject.parseArray(projectMembersDB);
                jsonArrayDB.removeAll(jsonArray);
                if (CollUtil.isNotEmpty(jsonArrayDB)) {
                    List<String> loginIds = IntStream.range(0, jsonArrayDB.size())
                            .mapToObj(i -> jsonArrayDB.getJSONObject(i).getString("loginId"))
                            .collect(Collectors.toList());
                    long userGroups = emrSysUserGroupService.count(
                            new LambdaQueryWrapper<EmrSysUserGroup>()
                                    .eq(EmrSysUserGroup::getProjectId, projectManager.getId())
                                    .in(EmrSysUserGroup::getLoginId, loginIds)
                    );
                    if (userGroups > 0) {
                        throw new RuntimeException("该项目成员已分配任务，不能修改成员！");
                    }
                }
            }
        }
        this.updateById(projectManager);
        return RUtil.success("更新项目成功");
    }

    @Override
    public R deleteProjectById(String projectId) {
        EmrProjectManagerVO emrProjectManagerVO = new EmrProjectManagerVO();
        emrProjectManagerVO.setId(projectId);
        EmrProjectManager projectManager = this.getById(projectId);
        addProgressAndMember(projectManager.getProjectType(), emrProjectManagerVO);
        if (StrUtil.isNotBlank(emrProjectManagerVO.getProgress()) && !"0".equals(emrProjectManagerVO.getProgress())) {
            throw new RuntimeException("该项目正在进行中无法删除！");
        }
        this.removeById(projectId);
        return RUtil.success("删除项目成功");
    }

    private void addProgressAndMember(String projectType, EmrProjectManagerVO emrProjectManagerVO) {
        emrProjectManagerVO.setProgress("0");
        emrProjectManagerVO.setMemberCount(0);
        if ("0".equals(projectType)) {
            List<RulePermissionConfiguration> rulePermissionConfigurations = rulePermissionConfigurationService.list(
                    new LambdaQueryWrapper<RulePermissionConfiguration>()
                            .eq(RulePermissionConfiguration::getProjectId, emrProjectManagerVO.getId())
                            .eq(RulePermissionConfiguration::getConfigType, "0")
            );

            if (CollUtil.isNotEmpty(rulePermissionConfigurations)) {
                emrProjectManagerVO.setProgress("0");
            }

            String projectMembers = emrProjectManagerVO.getProjectMembers();
            if (StrUtil.isNotBlank(projectMembers)) {
                JSONArray jsonArray = JSONObject.parseArray(projectMembers);
                emrProjectManagerVO.setMemberCount(jsonArray.size());
                emrProjectManagerVO.setMembers(jsonArray.toJavaList(EmrSysUserGroup.class));
            }

            //已分配数据量
            long allocationNum = rulePermissionConfigurations.size();
            //已完成数据量
            long finishNum = rulePermissionConfigurations.stream().filter(item -> "2".equals(item.getTaskStatus())).count();
            String progress = NumberMyUtil.calculationRatio(allocationNum, finishNum, 4);
            emrProjectManagerVO.setProgress(progress);
        } else {
            List<EmpiricalMaterialTaskAllocation> empiricalMaterialTaskAllocations = empiricalMaterialTaskAllocationService.list(
                    new LambdaQueryWrapper<EmpiricalMaterialTaskAllocation>()
                            .eq(EmpiricalMaterialTaskAllocation::getProjectId, emrProjectManagerVO.getId())
            );

            if (CollUtil.isNotEmpty(empiricalMaterialTaskAllocations)) {
                emrProjectManagerVO.setProgress("文档配置");
            }

            String projectMembers = emrProjectManagerVO.getProjectMembers();
            if (StrUtil.isNotBlank(projectMembers)) {
                JSONArray jsonArray = JSONObject.parseArray(projectMembers);
                emrProjectManagerVO.setMemberCount(jsonArray.size());
                emrProjectManagerVO.setMembers(jsonArray.toJavaList(EmrSysUserGroup.class));
            }

            //已分配数据量
            long allocationNum = empiricalMaterialTaskAllocations.size();
            //已完成数据量
            long finishNum = empiricalMaterialTaskAllocations.stream().filter(item -> "2".equals(item.getTaskStatus())).count();
            String progress = NumberMyUtil.calculationRatio(allocationNum, finishNum, 4);
            emrProjectManagerVO.setProgress(progress);
        }
    }

    @Override
    public R queryProject(EmrProjectManagerQuery emrProjectManagerQuery) {
        LambdaQueryWrapper<EmrProjectManager> queryWrapper = Wrappers.<EmrProjectManager>lambdaQuery()
                .like(StrUtil.isNotBlank(emrProjectManagerQuery.getProjectName()), EmrProjectManager::getProjectName, emrProjectManagerQuery.getProjectName())
                .eq(StrUtil.isNotBlank(emrProjectManagerQuery.getLevelCode()), EmrProjectManager::getLevelCode, emrProjectManagerQuery.getLevelCode())
                .like(StrUtil.isNotBlank(emrProjectManagerQuery.getPersonInCharge()), EmrProjectManager::getPersonInCharge, "\"" + emrProjectManagerQuery.getPersonInCharge() + "\"")
                .eq(StrUtil.isNotBlank(emrProjectManagerQuery.getProjectType()), EmrProjectManager::getProjectType, emrProjectManagerQuery.getProjectType())
                .orderByDesc(EmrProjectManager::getCreateTime);
        if (StrUtil.isNotBlank(emrProjectManagerQuery.getStartTime())) {
            queryWrapper.ge(EmrProjectManager::getCreateTime, DateUtil.parse(emrProjectManagerQuery.getStartTime()));
        }
        if (StrUtil.isNotBlank(emrProjectManagerQuery.getEndTime())) {
            emrProjectManagerQuery.setEndTime(emrProjectManagerQuery.getEndTime() + " 23:59:59");
            queryWrapper.le(EmrProjectManager::getCreateTime, DateUtil.parse(emrProjectManagerQuery.getEndTime()));
        }
        List<EmrProjectManager> list = this.list(queryWrapper);
        List<EmrProjectManagerVO> emrProjectManagerVOS = MapperUtils.INSTANCE.mapAsList(EmrProjectManagerVO.class, list);

        List<LevelDictionary> levelDictionaries = levelDictionaryService.list();
        Map<String, String> levelDictionaryMap = levelDictionaries.stream().collect(Collectors.toMap(LevelDictionary::getLevelCode, item -> item.getLevelName()));

        //添加当前项目进度
        //添加项目成员数量
        List<EmrProjectManagerVO> result = new ArrayList<>();
        for (EmrProjectManagerVO emrProjectManagerVO : emrProjectManagerVOS) {
            emrProjectManagerVO.setLevelName(levelDictionaryMap.get(emrProjectManagerVO.getLevelCode()));
            //默认进度为任务分配
            addProgressAndMember(emrProjectManagerVO.getProjectType(), emrProjectManagerVO);
            if (StrUtil.isBlank(emrProjectManagerVO.getProjectMembers())) {
                if (!emrProjectManagerQuery.isMyProject()) {
                    result.add(emrProjectManagerVO);
                }
                continue;
            }
            JSONArray jsonArray = JSONObject.parseArray(emrProjectManagerVO.getProjectMembers());
            emrProjectManagerVO.setMembers(jsonArray.toJavaList(EmrSysUserGroup.class));
            if (emrProjectManagerQuery.isMyProject() && emrProjectManagerVO.getMembers().stream().noneMatch(item -> item.getLoginId().equals(StpUtilMy.getUserAccount()))) {
                continue;
            }
            result.add(emrProjectManagerVO);
        }
        return RUtil.success(result);
    }

    @Override
    public R queryProjectAdmin(String projectType) {
        //具有项目管理权限的用户
        SysConfig sysConfigByName = RedisUtil.getSysConfigByName("emr.admin.role");
        if (sysConfigByName == null || StrUtil.isBlank(sysConfigByName.getConfigValue())) {
            return RUtil.error("没有在设置处配置项目管理角色");
        }
        String[] admins = sysConfigByName.getConfigValue().split(",");
        List<SysRole> sysRoles = sysRoleMapper.selectList(Wrappers.<SysRole>lambdaQuery().in(SysRole::getRoleName, admins).eq(SysRole::getSysId, Constant.SYS_NAME));
        if (CollUtil.isEmpty(sysRoles)) {
            return RUtil.error("没有找到项目管理角色");
        }
        List<String> roleIds = sysRoles.stream().map(SysRole::getRoleId).distinct().collect(Collectors.toList());
        List<SysUserRole> userInfos = sysUserRoleMapper.selectList(
                Wrappers.<SysUserRole>lambdaQuery().in(SysUserRole::getRoleId, roleIds).eq(SysUserRole::getSysId, Constant.SYS_NAME)
        );
        if (CollUtil.isEmpty(userInfos)) {
            return RUtil.error("没有找到项目管理用户");
        }
        List<Integer> userIds = userInfos.stream().map(SysUserRole::getUserId).distinct().collect(Collectors.toList());
        List<Map<String, Object>> sysUsers = sysUserMapper.selectMaps(
                new QueryWrapper<SysUser>().in("USER_ID", userIds)
                        .eq("SYS_ID", Constant.SYS_NAME)
                        .select("USER_NAME as userName", "LOGIN_ID as loginId", "USER_ID as userId")
        ).stream().collect(Collectors.toList());
        return RUtil.success(sysUsers);
    }
}
