package com.jykj.dqm.emr.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jykj.dqm.emr.entity.DocumentExportRecord;
import com.jykj.dqm.emr.service.DocumentExportRecordService;
import com.jykj.dqm.emr.service.DocumentExportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 定时任务
 * 1、EMR检查更新预览文档生成状态
 * 2、兜底，执行未开始的预览文档生成任务
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/16 14:50
 */
@Slf4j
@Configuration
@EnableScheduling
public class EmrScheduleTask {
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private DocumentExportRecordService documentExportRecordService;

    @Autowired
    private DocumentExportService documentExportService;

    @Value("${system.enable.emrm}")
    private boolean enableEmr;

    //每2分钟检查一次，EMR导出任务状态，成功就更新,主要针对定时执行任务
    @Scheduled(fixedRate = 2 * 60 * 1000, initialDelay = 20000)
    void doCheckExportStatusTasks() {
        if (!enableEmr) {
            return;
        }
        try {
            log.info("开始检查EMR导出任务状态的任务" + LocalDateTime.now());
            LambdaQueryWrapper<DocumentExportRecord> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(DocumentExportRecord::getExportStatus, "2");
            List<DocumentExportRecord> documentExportRecords = documentExportRecordService.list(queryWrapper);
            String id;
            for (DocumentExportRecord documentExportRecord : documentExportRecords) {
                id = documentExportRecord.getId();
                Object finishNumObj = redisTemplate.opsForValue().get(documentExportRecord.getId());
                if (finishNumObj == null) {
                    continue;
                }
                Integer finishNum = finishNumObj == null ? 0 : (Integer) finishNumObj;
                Object allDocxNumStr = redisTemplate.opsForValue().get(id + "_ALLNUM");
                Integer allDocxNum = allDocxNumStr == null ? 0 : (Integer) allDocxNumStr;
                if (finishNum.equals(allDocxNum)) {
                    documentExportRecord.setExportStatus("3");
                    documentExportRecordService.updateById(documentExportRecord);
                    log.info("EMR导出任务状态的任务{}，已完成", LocalDateTime.now(), id);
                    documentExportService.dealConditional(documentExportRecord);
                }
            }
        } catch (Throwable e) {
            //保证定时任务不会因为异常退出
            log.error("检查EMR导出任务状态的任务", e);
        }
    }


    //兜底，执行未开始的任务  10分钟执行一次
    @Scheduled(fixedRate = 10 * 60 * 1000, initialDelay = 20000)
    void doExport() {
        try {
            log.info("开始执行未开始的任务" + LocalDateTime.now());
            LambdaQueryWrapper<DocumentExportRecord> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(DocumentExportRecord::getExportStatus, "1");
            queryWrapper.lt(DocumentExportRecord::getTimedExportTime, new Date());
            List<DocumentExportRecord> documentExportRecords = documentExportRecordService.list(queryWrapper);
            String id;
            for (DocumentExportRecord documentExportRecord : documentExportRecords) {
                id = documentExportRecord.getId();
                documentExportService.previewExport(id);
                log.info("执行未开始的任务:{}", documentExportRecord);
            }
        } catch (Throwable e) {
            //保证定时任务不会因为异常退出
            log.error("检查EMR导出任务状态的任务", e);
        }
    }
}
