package com.jykj.dqm.emr.task;

import lombok.Data;

@Data
public class TaskBaseEntity {
    //任务参数，根据业务需求多少都行
    private String identifier;
    //任务类型，不传默认删除redis中到导出进度及状态信息，previewExport：预览文档生成
    private String type;

    public TaskBaseEntity(String identifier) {
        this.identifier = identifier;
    }

    public TaskBaseEntity(String identifier, String type) {
        this.identifier = identifier;
        this.type = type;
    }
}