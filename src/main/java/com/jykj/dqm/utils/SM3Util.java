package com.jykj.dqm.utils;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.digest.SM3;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SM3工具类--》国密SM3加密
 *
 * <AUTHOR>
 * @version 1.00
 */
public class SM3Util implements CryptoUtil {
    /**
     * 日志工厂
     */
    private static final Logger logger = LoggerFactory.getLogger(SM3Util.class);

    /**
     * SM3加密
     *
     * @param str 指定字符串
     * @return 加密后的字符串
     */
    @Override
    public String encrypt(String str) {
        str = (str == null ? "" : str);
        try {
            SM3 sm3 = SmUtil.sm3();
            return sm3.digestHex(str);
        } catch (Exception e) {
            logger.error("SM3加密失败！", e);
            throw new RuntimeException("SM3加密失败！");
        }
    }

    /**
     * 获取加密算法名称
     *
     * @return 加密算法名称
     */
    @Override
    public String getAlgorithmName() {
        return "SM3";
    }
}