package com.jykj.dqm.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.jykj.dqm.common.UploadDriverFile;
import com.jykj.dqm.common.UploadFile;
import com.jykj.dqm.common.UploadImageFile;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 判断系统
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/23 13:45:00
 */
public class SystemUtils {
    /**
     * 判断操作系统是否是 Windows
     *
     * @return true：操作系统是 Windows
     * false：其它操作系统
     */
    public static boolean isWindows() {
        String osName = getOsName();

        return osName != null && osName.startsWith("Windows");
    }

    /**
     * 判断操作系统是否是 MacOS
     *
     * @return true：操作系统是 MacOS
     * false：其它操作系统
     */
    public static boolean isMacOs() {
        String osName = getOsName();

        return osName != null && osName.startsWith("Mac");
    }

    /**
     * 判断操作系统是否是 Linux
     *
     * @return true：操作系统是 Linux
     * false：其它操作系统
     */
    public static boolean isLinux() {
        String osName = getOsName();

        return (osName != null && osName.startsWith("Linux")) || (!isWindows() && !isMacOs());
    }

    /**
     * 获取操作系统名称
     *
     * @return os.name 属性值
     */
    public static String getOsName() {
        return System.getProperty("os.name");
    }

    /**
     * 根据当前系统获取路径
     *
     * @return DQM文件存储路径
     * <AUTHOR>
     */
    public static String getFilePath() {
        //方式一：通过Hutool Props获取配置信息
        //Props props = new Props("application-testmysql.yml");
        //props.getProperty("win-upload-path");
        //props.getProperty("linux-upload-path");
        //方式二：根据属性注入实体类获取，但是无法注入该属性对象，需要从ApplicationContext获取bean对象
        UploadDriverFile uploadDriverFile = SpringUtil.getBean(UploadDriverFile.class);
        if (isWindows()) {
            return uploadDriverFile.getWinUploadPath();
        } else {
            return uploadDriverFile.getLinuxUploadPath();
        }
    }


    /**
     * 根据当前系统获取路径
     *
     * @return DQM文件存储路径
     * <AUTHOR>
     */
    public static String getImagePath() {
        UploadImageFile uploadDriverFile = SpringUtil.getBean(UploadImageFile.class);
        if (isWindows()) {
            return uploadDriverFile.getWinUploadPath();
        } else {
            return uploadDriverFile.getLinuxUploadPath();
        }
    }

    /**
     * 根据当前系统获取路径
     *
     * @param clazz 用于获取文件路径的类对象，可以是UploadDriverFile.class或UploadImageFile.class
     * @return 文件存储路径
     * <AUTHOR>
     */
    public static String getPath(Class<?> clazz) {
        Object uploadFile = SpringUtil.getBean(clazz);
        if (isWindows()) {
            //也可以采用uploadFile instanceof UploadDriverFile
            return ((UploadFile) uploadFile).getWinUploadPath();
        } else {
            return ((UploadFile) uploadFile).getLinuxUploadPath();
        }
    }
}
