package com.jykj.dqm.utils;

import cn.hutool.core.date.DateUtil;

import java.util.Date;

/**
 * 日期时间工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/28 14:50:51
 */
public class DateTimeUtils {
    /**
     * 获取endTime和startTime相差的月份（所在的月份数）
     *
     * @param startTime
     * @param endTime
     * @return 相差的月份
     */
    public static String getMonthBetween(String startTime, String endTime) {
        Date date1 = DateUtil.parse(startTime);
        Date date2 = DateUtil.parse(endTime);
        long between = DateUtil.betweenMonth(date1, date2, false) + 1;
        return between + "";
    }

    /**
     * 获取endTime和startTime相差的月份
     *
     * @param startTime Date
     * @param endTime   Date
     * @return 相差的月份
     */
    public static String getMonthBetween(Date startTime, Date endTime) {
        long between = DateUtil.betweenMonth(startTime, endTime, false) + 1;
        return between + "";
    }

    public static void main(String[] args) {
        System.out.println(getMonthBetween("2020-03-01", "2020-04-30"));
    }
}
