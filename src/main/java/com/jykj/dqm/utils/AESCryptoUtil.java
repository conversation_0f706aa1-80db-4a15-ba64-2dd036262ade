package com.jykj.dqm.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AES对称加密工具类
 *
 * <AUTHOR>
 * @version 1.00
 */
public class AESCryptoUtil implements SymmetricCryptoUtil {
    /**
     * 日志工厂
     */
    private static final Logger logger = LoggerFactory.getLogger(AESCryptoUtil.class);

    /**
     * base64格式的默认秘钥
     * 也可以每次生成一个随机的秘钥,使用如下代码
     * byte[] key =
     * SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded();
     * String secret = Base64.encode(key);
     */
    private static final String BASE64_SECRET = "SnlrajE5OTRKeWtqMTk5NA==";

    /**
     * aes用来加密解密的byte[]
     */
    private final static byte[] SECRET_BYTES = Base64.decode(BASE64_SECRET);

    /**
     * 根据这个秘钥得到一个aes对象
     */
    private final static AES aes = SecureUtil.aes(SECRET_BYTES);

    /**
     * 存储解码的值
     */
    private final static Map<String, String> map = new ConcurrentHashMap<>();

    /**
     * 使用aes加密
     *
     * @param str 待加密的字符串
     * @return 加密后的字符串
     */
    @Override
    public String encrypt(String str) {
        // 加密完以后是十六进制的
        return aes.encryptHex(str);
    }

    /**
     * 使用aes算法,进行解密
     *
     * @param str 待解密的字符串
     * @return 解密后的字符串
     */
    @Override
    public String decrypt(String str) {
        if (!isEncrypted(str)) {
            return str;
        }
        if (map.containsKey(str)) {
            return map.get(str);
        } else {
            String password = aes.decryptStr(str);
            map.put(str, password);
            return password;
        }
    }

    /**
     * 获取加密算法名称
     *
     * @return 加密算法名称
     */
    @Override
    public String getAlgorithmName() {
        return "AES";
    }

    /**
     * 判断字符串是否为加密内容
     *
     * @param str 待判断的字符串
     * @return 是否为加密内容
     */
    @Override
    public boolean isEncrypted(String str) {
        boolean result = true;
        try {
            aes.decryptStr(str);
        } catch (Exception e) {
            result = false;
        }
        return result;
    }
}