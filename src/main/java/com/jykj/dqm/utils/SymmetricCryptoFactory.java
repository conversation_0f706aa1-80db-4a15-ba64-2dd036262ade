package com.jykj.dqm.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 对称加密工厂类，用于统一管理对称加密算法
 *
 * <AUTHOR>
 * @version 1.00
 */
@Component
public class SymmetricCryptoFactory {

    /**
     * 默认使用的对称加密算法，可通过配置文件指定
     */
    private static String defaultAlgorithm = "SM4";

    /**
     * 从配置文件中读取默认对称加密算法
     * 
     * @param algorithm 配置的算法名称
     */
    @Value("${crypto.symmetric-algorithm:SM4}")
    public void setDefaultAlgorithm(String algorithm) {
        defaultAlgorithm = algorithm;
    }

    /**
     * 获取默认对称加密工具
     *
     * @return 默认对称加密工具
     */
    public static SymmetricCryptoUtil getDefaultCrypto() {
        return getCrypto(defaultAlgorithm);
    }

    /**
     * 根据算法名称获取对称加密工具
     *
     * @param algorithm 算法名称（AES/SM4）
     * @return 对称加密工具
     */
    public static SymmetricCryptoUtil getCrypto(String algorithm) {
        if ("AES".equalsIgnoreCase(algorithm)) {
            return new AESCryptoUtil();
        } else if ("SM4".equalsIgnoreCase(algorithm)) {
            return new SM4CryptoUtil();
        } else {
            // 默认返回SM4
            return new SM4CryptoUtil();
        }
    }

    /**
     * 使用默认对称加密算法加密字符串
     *
     * @param str 待加密的字符串
     * @return 加密后的字符串
     */
    public static String encrypt(String str) {
        return getDefaultCrypto().encrypt(str);
    }

    /**
     * 使用默认对称加密算法解密字符串
     * 
     * @param str 待解密的字符串
     * @return 解密后的字符串
     */
    public static String decrypt(String str) {
        return getDefaultCrypto().decrypt(str);
    }

    /**
     * 判断字符串是否为加密内容
     *
     * @param str 待判断的字符串
     * @return 是否为加密内容
     */
    public static boolean isEncrypted(String str) {
        return getDefaultCrypto().isEncrypted(str);
    }
}