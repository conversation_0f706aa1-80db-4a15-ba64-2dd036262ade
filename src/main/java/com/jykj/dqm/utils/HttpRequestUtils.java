package com.jykj.dqm.utils;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.io.IOException;
import java.util.Map;

/**
 * HttpUtils
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/7/26 15:27
 */
public class HttpRequestUtils {
    /**
     * GET 请求,参数在URL里面
     */
    public static JSONObject httpRequestGet(String url) throws IOException {
        String s = HttpUtil.get(url);
        return JSON.parseObject(s);
    }

    /**
     * GET 请求，,参数单独通过params传
     */
    public static JSONObject httpRequestGet(String url, Map<String, Object> params) throws IOException {
        String s = HttpUtil.get(url, params);
        return JSON.parseObject(s);
    }
}
