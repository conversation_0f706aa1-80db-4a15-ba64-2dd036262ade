package com.jykj.dqm.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SM4;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SM4对称加密工具类
 *
 * <AUTHOR>
 * @version 1.00
 */
public class SM4CryptoUtil implements SymmetricCryptoUtil {
    /**
     * 日志工厂
     */
    private static final Logger logger = LoggerFactory.getLogger(SM4CryptoUtil.class);

    /**
     * base64格式的默认秘钥
     */
    private static final String BASE64_SECRET = "SnlrajE5OTRKeWtqMTk5NA==";

    /**
     * sm4用来加密解密的byte[]
     */
    private final static byte[] SECRET_BYTES = Base64.decode(BASE64_SECRET);

    /**
     * 根据这个秘钥得到一个sm4对象
     */
    private final static SM4 sm4 = SmUtil.sm4(SECRET_BYTES);

    /**
     * 存储解码的值
     */
    private final static Map<String, String> map = new ConcurrentHashMap<>();

    /**
     * 使用sm4加密
     *
     * @param str 待加密的字符串
     * @return 加密后的字符串
     */
    @Override
    public String encrypt(String str) {
        try {
            // 加密完以后是十六进制的
            return sm4.encryptHex(str);
        } catch (Exception e) {
            logger.error("SM4加密失败！", e);
            throw new RuntimeException("SM4加密失败！");
        }
    }

    /**
     * 使用sm4算法,进行解密
     *
     * @param str 待解密的字符串
     * @return 解密后的字符串
     */
    @Override
    public String decrypt(String str) {
        if (!isEncrypted(str)) {
            return str;
        }
        if (map.containsKey(str)) {
            return map.get(str);
        } else {
            try {
                String password = sm4.decryptStr(str);
                map.put(str, password);
                return password;
            } catch (Exception e) {
                logger.error("SM4解密失败！", e);
                return str;
            }
        }
    }

    /**
     * 获取加密算法名称
     *
     * @return 加密算法名称
     */
    @Override
    public String getAlgorithmName() {
        return "SM4";
    }

    /**
     * 判断字符串是否为加密内容
     *
     * @param str 待判断的字符串
     * @return 是否为加密内容
     */
    @Override
    public boolean isEncrypted(String str) {
        boolean result = true;
        try {
            sm4.decryptStr(str);
        } catch (Exception e) {
            result = false;
        }
        return result;
    }
}