package com.jykj.dqm.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.symmetric.SM4;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class SmCryptoUtil {
    /**
     * base64格式的默认秘钥
     * 也可以每次生成一个随机的秘钥,使用如下代码
     * byte[] key =
     * SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded();
     * String secret = Base64.encode(key);
     */
    private static final String BASE64_SECRET = "SnlrajE5OTRKeWtqMTk5NA==";

    /**
     * aes用来加密解密的byte[]
     */
    private final static byte[] SECRET_BYTES = Base64.decode(BASE64_SECRET);
    private static final SymmetricCrypto sm4;

    /**
     * 存储解码的值
     */
    private final static Map<String, String> map = new ConcurrentHashMap<>();

    static {
        sm4 = new SM4(SECRET_BYTES);
    }

    private SmCryptoUtil() {
        // Private constructor to prevent instantiation
    }

    /**
     * 国密4加密
     *
     * @param content 加密内容
     * @return 加密后的字符串
     */
    public static String encrypt(String content) {
        return sm4.encryptHex(content);
    }

    /**
     * 国密4解密
     *
     * @param encryptedContent 加密后的字符串
     * @return 解密后的内容
     */
    public static String decrypt(String encryptedContent) {
        String content = encryptedContent;
        if (map.containsKey(encryptedContent)) {
            content = map.get(encryptedContent);
            return content;
        }
        try {
            content = sm4.decryptStr(encryptedContent, StandardCharsets.UTF_8);
            map.put(encryptedContent, content);
        } catch (Exception e) {
            log.warn("解密失败, 密文: {}", encryptedContent, e);
            return content;
        }
        return content;
    }

    /**
     * 是否是SM4加密的CODE
     *
     * @param content
     * @return
     */
    public static boolean isSmEnCode(String content) {
        boolean result = true;
        try {
            sm4.decryptStr(content, StandardCharsets.UTF_8);
        } catch (Exception e) {
            // log.error(e.getMessage(), e);
            result = false;
        }
        return result;
    }
}
