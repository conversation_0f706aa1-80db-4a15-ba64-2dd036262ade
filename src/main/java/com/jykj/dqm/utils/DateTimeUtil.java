package com.jykj.dqm.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 日期时间工具类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Slf4j
public class DateTimeUtil {
    /**
     * 获取当前时间字符串
     *
     * @return 当前时间字符串
     * <AUTHOR>
     */
    public static String getNowDateTimeStr() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date());
    }

    /**
     * 获取当前时间毫秒字符串
     *
     * @return 当前时间毫秒字符串
     * <AUTHOR>
     */
    public static String getNowDateMilliSecondStr() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return sdf.format(new Date());
    }

    /**
     * 拼接Oracle现在日期时间字符串
     *
     * @return Oracle当前日期时间字符串
     * <AUTHOR>
     */
    public static String getOracleNowDateTimeStr() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return "TO_DATE('" + sdf.format(new Date()) + "','YYYY-MM-DD HH24:MI:SS'),";
    }

    /**
     * 检查是否是yyyy-MM-dd HH:mm:ss 这种格式，能不能，正常转换为时间
     *
     * @param date date String
     * @return 能成功转换就是true，不能就是false
     * <AUTHOR>
     */
    public static boolean checkDateString(String date) {
        try {
            getDateByTimeString(date);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断时间的格式是否为“yyyyMMddHHmmss”格式的合法日期字符串
     *
     * @param str 日期字符串
     * @return true有效，false无效
     * <AUTHOR>
     */
    public static boolean isValidDate(String str) {
        try {
            if (StrUtil.isNotEmpty(str)) {
                if (str.length() == 14) {
                    // 闰年标志
                    boolean isLeapYear = false;
                    String year = str.substring(0, 4);
                    String month = str.substring(4, 6);
                    String day = str.substring(6, 8);
                    String hour = str.substring(8, 10);
                    String minute = str.substring(10, 12);
                    String second = str.substring(12, 14);
                    int intYear = Integer.parseInt(year);
                    // 判断年份是否合法
                    if (intYear < 1900 || intYear > 2200) {
                        return false;
                    }
                    // 判断是否为闰年
                    if (intYear % 4 == 0 && intYear % 100 != 0 || intYear % 400 == 0) {
                        isLeapYear = true;
                    }
                    // 判断月份
                    // 1.判断月份
                    if (month.startsWith("0")) {
                        String unitMonth = month.substring(1, 2);
                        int intUnitMonth = Integer.parseInt(unitMonth);
                        if (intUnitMonth == 0) {
                            return false;
                        }
                        if (intUnitMonth == 2) {
                            // 获取2月的天数
                            int intDay4February = Integer.parseInt(day);
                            if (isLeapYear) {
                                if (intDay4February > 29) {
                                    return false;
                                }
                            } else {
                                if (intDay4February > 28) {
                                    return false;
                                }
                            }
                        }
                    } else {
                        // 2.判断非0打头的月份是否合法
                        int intMonth = Integer.parseInt(month);
                        if (intMonth != 10 && intMonth != 11 && intMonth != 12) {
                            return false;
                        }
                    }
                    // 判断日期
                    // 1.判断日期
                    if (day.startsWith("0")) {
                        String unit4Day = day.substring(1, 2);
                        int intUnit4Day = Integer.parseInt(unit4Day);
                        if (intUnit4Day == 0) {
                            return false;
                        }
                    } else {
                        // 2.判断非0打头的日期是否合法
                        int intDay = Integer.parseInt(day);
                        if (intDay < 10 || intDay > 31) {
                            return false;
                        }
                    }
                    // 判断时间
                    int intHour = Integer.parseInt(hour);
                    int intMinute = Integer.parseInt(minute);
                    int intSecond = Integer.parseInt(second);
                    if (intHour < 0 || intHour > 23 || intMinute < 0 || intMinute > 59 || intSecond < 0 || intSecond > 59) {
                        return false;
                    }
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据时间字符串获取日期yyyyMMddHHmmss
     *
     * @param dateTime yyyyMMddHHmmss--时间字符串
     * @return 日期
     * <AUTHOR>
     */
    public static Date getDateByTimeString(String dateTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date;
        try {
            date = sdf.parse(dateTime);
        } catch (Exception e) {
            log.error("时间转换异常", e);
            throw new RuntimeException("时间转换异常，请核对时间格式", e);
        }
        return date;
    }

    /**
     * 根据时间字符串获取日期yyyy-MM-dd HH:mm:ss
     *
     * @param dateTime yyyy-MM-dd HH:mm:ss--时间字符串
     * @return 日期
     * <AUTHOR>
     */
    public static Date getDateByTimeString2(String dateTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date;
        try {
            date = sdf.parse(dateTime);
        } catch (Exception e) {
            log.error("时间转换异常", e);
            throw new RuntimeException("时间转换异常，请核对时间格式", e);
        }
        return date;
    }

    /**
     * 将日期转换为yyyy-MM-dd HH:mm:ss日期字符串
     *
     * @param date 需要转换的日期-》yyyy-MM-dd HH:mm:ss
     * @return yyyy-MM-dd HH:mm:ss日期字符串
     * <AUTHOR>
     */
    public static String getDateTimeStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * 获取唯一编码
     *
     * @return 唯一编码
     * <AUTHOR>
     */
    public static String getUniqueCode() {
        return String.valueOf(System.nanoTime());
    }

    /**
     * localDate转Date
     */
    public static Date localDate2Date(LocalDate localDate) {
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        Instant instant1 = zonedDateTime.toInstant();
        Date from = Date.from(instant1);
        return from;
    }

    /**
     * Date 转 localDate
     */
    public static LocalDate date2LocalDate(Date date) {
        Instant instant = date.toInstant();
        ZonedDateTime zdt = instant.atZone(ZoneId.systemDefault());
        LocalDate localDate = zdt.toLocalDate();
        return localDate;
    }

    /**
     * LocalDate转String
     *
     * @param date LocalDate
     * @return dateString
     * <AUTHOR>
     */
    public static String LocalDateToString(LocalDate date) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return date.format(fmt);
    }

    /**
     * LocalDate转String
     *
     * @param date LocalDate
     * @return dateString
     * <AUTHOR>
     */
    public static String localDate2DateString(LocalDate date) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return date.format(fmt);
    }
}
