

package com.jykj.dqm.utils;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.system.entity.SysConfig;

import java.util.regex.Pattern;

/**
 * 密码等级校验
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/29 15:36
 */
public class PassWordUtil {
    //数字
    public static final String REG_NUMBER = ".*\\d+.*";
    //小写字母
    public static final String REG_UPPERCASE = ".*[A-Z]+.*";
    //大写字母
    public static final String REG_LOWERCASE = ".*[a-z]+.*";
    //特殊符号(~!@#$%^&*()_+|<>,.?/:;'[]{}\)
    public static final String REG_SYMBOL = ".*[~!@#$%^&*()_+|<>,.?/:;'\\[\\]{}\"]+.*";

    /**
     * 1) 长度 8-20 位，必须包括大写字母、小写字母、数字、特殊符号中的4种
     * 2) 密码不能包含用户名
     *
     * @param password 密码
     * @param username 用户名
     * @return 结果
     */
    public static boolean checkPasswordRuleHigh(String password, String username) {
        //密码为空及长度大于8位小于30位判断
        if (password == null || password.length() < 8 || password.length() > 30) {
            return false;
        }
        int i = 0;
        if (password.matches(REG_NUMBER)) {
            i++;
        }
        if (password.matches(REG_LOWERCASE)) {
            i++;
        }
        if (password.matches(REG_UPPERCASE)) {
            i++;
        }
        if (password.matches(REG_SYMBOL)) {
            i++;
        }
        boolean contains = password.equals(username) || password.contains(username);
        //此处可以判定包含几种强密码为4
        if (i < 4 || contains) {
            return false;
        }
        return true;
    }

    /**
     * 长度 8-20 位，必须包括大写字母、小写字母、数字、特殊符号中的2种
     *
     * @param password 密码
     * @return 结果
     */
    public static boolean checkPasswordRuleMedium(String password) {
        //密码为空及长度大于8位小于30位判断
        if (password == null || password.length() < 8 || password.length() > 20) {
            return false;
        }
        int i = 0;
        if (password.matches(REG_NUMBER)) {
            i++;
        }
        if (password.matches(REG_LOWERCASE)) {
            i++;
        }
        if (password.matches(REG_UPPERCASE)) {
            i++;
        }
        if (password.matches(REG_SYMBOL)) {
            i++;
        }
        if (i < 2) {
            return false;
        }
        return true;
    }

    /**
     * 检查登录密码是否符合要求：密码长度限制必须在8—20之间，不能是纯数字或字母
     *
     * @param password 传入的密码
     * @return 检查结果，符合要求true，不符合false
     * <AUTHOR>
     */
    public static boolean checkPasswordRuleMedium2(String password) {
        int length = password.length();
        if (length < 8 || length > 20) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[A-Za-z]+$");
        if (pattern.matcher(password).matches()) {
            return false;
        }
        pattern = Pattern.compile("^[0-9]+$");
        if (pattern.matcher(password).matches()) {
            return false;
        }
        return true;
    }

    /**
     * 检查密码是否合规
     *
     * @param loginId  登录账户（用户名）
     * @param password 密码
     * @return 结果
     * <AUTHOR>
     */
    public static R checkPassWord(String loginId, String password) {
        SysConfig sysConfig = RedisUtil.getSysConfigByName("password.level");
        if (sysConfig == null) {
            return null;
        }
        String level = sysConfig.getConfigValue();
        switch (level) {
            case "1": {
                if (!PassWordUtil.checkPasswordRuleHigh(password, loginId)) {
                    return RUtil.error("1、密码长度限制必须在8—20之间，必须包括大写字母、小写字母、数字、特殊符号中的4种;2、密码不能包含用户名");
                }
                break;
            }
            case "2": {
                if (!StringUtil.checkPassword(password)) {
                    return RUtil.error("密码长度限制必须在8—20之间，不能是纯数字或字母");
                }
                break;
            }
        }
        return null;
    }
}
