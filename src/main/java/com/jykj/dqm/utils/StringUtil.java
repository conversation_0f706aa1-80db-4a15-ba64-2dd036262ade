package com.jykj.dqm.utils;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.File;
import java.io.Reader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.sql.Clob;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串处理类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
public class StringUtil {
    /**
     * 日志工厂
     */
    private static final Logger logger = LoggerFactory.getLogger(StringUtil.class);

    /**
     * 默认每页数量
     */
    private static int defaultPageSize = 10;

    /**
     * 判断是否为空
     *
     * @param value 传入的字符串
     * @return true为空，fasle不为空
     */
    public static boolean isEmpty(String value) {
        return value == null || value.isEmpty();
    }

    /**
     * 判断是否为整数
     *
     * @param str 传入的字符串
     * @return 是整数返回true, 否则返回false
     * <AUTHOR>
     */
    public static boolean isInteger(String str) {
        if (str == null) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

    /**
     * 检查登录密码是否符合要求：密码长度限制必须在8—20之间，不能是纯数字或字母
     *
     * @param password 传入的密码
     * @return 检查结果，符合要求true，不符合false
     * <AUTHOR>
     */
    public static boolean checkPassword(String password) {
        int length = password.length();
        if (length < 8 || length > 20) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[A-Za-z]+$");
        if (pattern.matcher(password).matches()) {
            return false;
        }
        pattern = Pattern.compile("^[0-9]+$");
        if (pattern.matcher(password).matches()) {
            return false;
        }
        return true;
    }

    /**
     * 获取IP地址
     *
     * @param request HttpServletRequest
     * @return IP地址
     * <AUTHOR>
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ipAddress = request.getHeader("x-forwarded-for");
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
            if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
                //根据网卡取本机配置的IP
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    logger.error("获取IP出错", e);
                }
                ipAddress = inet.getHostAddress();
            }
        }
        //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ipAddress != null && ipAddress.length() > 15) { //"***.***.***.***".length() = 15
            if (ipAddress.indexOf(",") > 0) {
                ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
            }
        }
        return ipAddress;
    }

    /**
     * Object转String-》获取值
     *
     * @param object Object
     * @return String类型的值
     * <AUTHOR>
     */
    public static String getValue(Object object) {
        if (object == null) {
            return "";
        }
        return String.valueOf(object);
    }

    /**
     * Object转String-》获取值
     *
     * @param object Object
     * @return String类型的值
     * <AUTHOR>
     */
    public static Integer getIntegerValue(Object object) {
        if (object == null) {
            return 0;
        }
        return Integer.parseInt(object + "");
    }

    /**
     * Object转String-》获取值
     *
     * @param object Object
     * @return String类型的值
     * <AUTHOR>
     */
    public static Long getLongValue(Object object) {
        if (object == null) {
            return 0L;
        }
        return Long.parseLong(object + "");
    }

    /**
     * 获取占比（百分值）
     *
     * @param newValue 新值
     * @param oldValue 旧值
     * @return 占比
     * <AUTHOR>
     */
    public static BigDecimal getRatio(long newValue, long oldValue) {
        return getRatio(BigDecimal.valueOf(newValue), BigDecimal.valueOf(oldValue));
    }

    /**
     * 获取占比（百分值）
     * <pre>
     *     公式
     *     newValue/oldValue*100
     *     如果当前值为0，返回 0
     *     否则：
     *     如果以前的值为0 返回 100.00
     *     否则：
     *     如果当前值=以前的值  返回 100
     *     否则：
     *     按照公式计算
     * </pre>
     *
     * @param newValue 新值
     * @param oldValue 旧值
     * @return 占比
     * <AUTHOR>
     */
    public static BigDecimal getRatio(BigDecimal newValue, BigDecimal oldValue) {
        // 如果当前值为0，占比就为0
        if (newValue == null || newValue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        // 如果以前值为0，其结果表示无穷大 暂用100.00表示
        if (oldValue == null || oldValue.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal("1");
        }
        // 如果当前值=以前的值  返回 100
        if (newValue.compareTo(oldValue) == 0) {
            return BigDecimal.valueOf(1);
        }
        return newValue.divide(oldValue, 2, RoundingMode.HALF_UP);
    }

    /**
     * 根据异常获取异常信息
     *
     * @param e Exception
     * @return 异常信息
     * <AUTHOR>
     */
    public static String getErrorMsg(Exception e) {
        String msg = "";
        if (e.getCause() != null) {
            msg = StringUtil.getValue(e.getCause().getLocalizedMessage());
        } else {
            msg = e.getMessage();
        }
        return msg;
    }

    /**
     * Clob类型转String
     *
     * @param clob Clob类型值
     * @return String类型的值
     * <AUTHOR>
     */
    public static String ClobToString(Clob clob) {
        String reString = "";
        Reader is = null;
        BufferedReader br = null;
        try {
            is = clob.getCharacterStream();
            br = new BufferedReader(is);
            String s = br.readLine();
            StringBuffer sb = new StringBuffer();
            while (s != null) {
                sb.append(s);
                s = br.readLine();
            }
            reString = sb.toString();
            if (br != null) {
                br.close();
            }
            if (is != null) {
                is.close();
            }
            return reString;
        } catch (Exception e) {
            logger.error("转换失败", e);
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (Exception exception) {
                logger.error("关流失败！");
            }
        }
        return reString;
    }

    /**
     * 获取异常详细信息
     *
     * @param e Exception
     * @return 日志详细信息
     * <AUTHOR>
     */
    public static String getStackTrace(Exception e) {
        StringBuffer message = new StringBuffer();
        StackTraceElement[] exceptionStack = e.getStackTrace();
        message.append(e.toString());
        for (StackTraceElement ste : exceptionStack) {
            message.append("\n\tat " + ste);
        }
        return message.toString();
    }

    /**
     * 将路径转换为window和linux正常路径
     *
     * @param path 路径
     * @return window和linux正常路径
     */
    public static String normalPath(String path) {
        String normal = Matcher.quoteReplacement(File.separator);
        return path.replaceAll("/", normal).replaceAll("\\\\", normal);
    }

    /**
     * 去掉文本中的空格
     *
     * @param text 文本
     * @return 去掉文空格后的文本
     * <AUTHOR>
     */
    public static String removeSpacesFromText(String text) {
        if (text == null) {
            return "";
        }
        return text.replaceAll("\\s+", "");
    }


    public static Integer getValue(String[] codeArray, int index) {
        try {
            if (codeArray.length - 1 < index || StrUtil.isBlank(codeArray[index])) {
                return 0;
            }
            return Integer.parseInt(codeArray[index]);
        } catch (Exception e) {
            logger.error("解析目录编号异常", e);
        }
        return 0;
    }

    public static String getStringValue(String[] codeArray, int index) {
        try {
            if (codeArray.length - 1 < index) {
                return "0";
            }
            return codeArray[index];
        } catch (Exception e) {
            logger.error("解析目录编号异常", e);
        }
        return "";
    }

    public static void main(String[] args) {
        System.out.println(normalPath("\\abc"));
        System.out.println(removeSpacesFromText(" 黄 杰  O D "));
    }
}
