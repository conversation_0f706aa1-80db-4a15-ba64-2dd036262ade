package com.jykj.dqm.utils;

import com.jykj.dqm.metadata.dao.MetadataDatasourceMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.system.dao.SysConfigMapper;
import com.jykj.dqm.system.entity.SysConfig;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * redis访问类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/15 11:14
 */
@Component
public class RedisUtil {
    private static RedisTemplate redisTemplate;

    private static RedissonClient redissonClient;

    @Autowired
    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        RedisUtil.redisTemplate = redisTemplate;
    }

    private static SysConfigMapper sysConfigMapper;

    @Autowired
    public void setSysConfigMapper(SysConfigMapper sysConfigMapper) {
        this.sysConfigMapper = sysConfigMapper;
    }

    private static MetadataDatasourceMapper metadataDatasourceMapper;

    @Autowired
    public void setMetadataDatasourceMapper(MetadataDatasourceMapper metadataDatasourceMapper) {
        this.metadataDatasourceMapper = metadataDatasourceMapper;
    }

    /**
     * 根据命名空间前缀删除
     *
     * @param namespacePrefix 例如 Auth:
     * <AUTHOR>
     */
    public static void deleteByNamespacePrefix(String namespacePrefix) {
        Set<String> keys = redisTemplate.keys(namespacePrefix + "*");
        redisTemplate.delete(keys);
    }

    /**
     * 根据系统配置名称获取系统配置
     *
     * @param name CONFIG_CODE
     * @return SysConfig
     * <AUTHOR>
     */
    public static SysConfig getSysConfigByName(String name) {
        SysConfig config = new SysConfig();
        if (containSysConfig(name, config)) {
            return config;
        }
        RLock rLock = redissonClient.getLock("EMRM_SysConfig");
        try {
            rLock.lock(5, TimeUnit.SECONDS);
            //重新判断
            if (containSysConfig(name, config)) {
                return config;
            }
            List<SysConfig> sysConfigs = sysConfigMapper.querySysConfigAll();
            redisTemplate.opsForValue().set("EMRM:Sys:SysConfigs", sysConfigs);
            //返回值
            if (containSysConfig(name, config)) {
                return config;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (rLock != null && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
        return null;
    }

    public static String getSysConfigValue(String configName, String defaultVue) {
        SysConfig sysConfig = getSysConfigByName(configName);
        return Optional.ofNullable(sysConfig).map(item -> item.getConfigValue()).orElse(defaultVue);
    }

    private static boolean containSysConfig(String name, SysConfig config) {
        Object sysConfigsObject = redisTemplate.opsForValue().get("EMRM:Sys:SysConfigs");
        if (sysConfigsObject != null) {
            List<SysConfig> sysConfigs = (List<SysConfig>) sysConfigsObject;
            List<SysConfig> configOne = sysConfigs.stream().filter(sysConfig -> name.equalsIgnoreCase(sysConfig.getConfigCode())).collect(Collectors.toList());
            if (configOne.size() > 0) {
                BeanUtils.copyProperties(configOne.get(0), config);
                return true;
            }
        }
        return false;
    }

    public static MetadataDatasource getMetadataDatasourceById(String id) {
        MetadataDatasource metadataDatasource = new MetadataDatasource();
        if (containMetadataDatasource(id, metadataDatasource)) {
            return metadataDatasource;
        }
        RLock rLock = redissonClient.getLock("EMRM_Metadata");
        try {
            rLock.lock(5, TimeUnit.SECONDS);
            //重新判断
            if (containMetadataDatasource(id, metadataDatasource)) {
                return metadataDatasource;
            }
            List<MetadataDatasource> metadataDatasources = metadataDatasourceMapper.selectList(null);
            redisTemplate.opsForValue().set("EMRM:Metadata:MetadataDatasources", metadataDatasources);
            //返回值
            if (containMetadataDatasource(id, metadataDatasource)) {
                return metadataDatasource;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (rLock != null && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
        return null;
    }

    private static boolean containMetadataDatasource(String id, MetadataDatasource metadataDatasource) {
        Object metadataDatasources = redisTemplate.opsForValue().get("EMRM:Metadata:MetadataDatasources");
        if (metadataDatasources != null) {
            List<MetadataDatasource> metadataDatasourceList = (List<MetadataDatasource>) metadataDatasources;
            List<MetadataDatasource> configOne = metadataDatasourceList.stream().filter(entity -> id.equals(entity.getDataSourceId()+"")).collect(Collectors.toList());
            if (configOne.size() > 0) {
                BeanUtils.copyProperties(configOne.get(0), metadataDatasource);
                return true;
            }
        }
        return false;
    }
}
