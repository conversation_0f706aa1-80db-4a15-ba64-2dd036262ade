package com.jykj.dqm.utils;

import cn.hutool.core.codec.Base64;
import lombok.SneakyThrows;
import net.coobird.thumbnailator.Thumbnails;

import java.io.ByteArrayOutputStream;
import java.io.File;

/**
 * <AUTHOR>
 * @version 1.0
 * @Date 23/2/20 9:38:05
 */
public class FileUtil {
    /**
     * 判断操作系统是否是 Windows
     *
     * @return true：操作系统是 Windows
     * false：其它操作系统
     */
    public static boolean isWindows() {
        String osName = getOsName();

        return osName != null && osName.startsWith("Windows");
    }

    /**
     * 判断操作系统是否是 MacOS
     *
     * @return true：操作系统是 MacOS
     * false：其它操作系统
     */
    public static boolean isMacOs() {
        String osName = getOsName();

        return osName != null && osName.startsWith("Mac");
    }

    /**
     * 判断操作系统是否是 Linux
     *
     * @return true：操作系统是 Linux
     * false：其它操作系统
     */
    public static boolean isLinux() {
        String osName = getOsName();

        return (osName != null && osName.startsWith("Linux")) || (!isWindows() && !isMacOs());
    }

    /**
     * 获取操作系统名称
     *
     * @return os.name 属性值
     */
    public static String getOsName() {
        return System.getProperty("os.name");
    }

    /**
     * 将路径修正为当前操作系统所支持的形式.
     *
     * @param path 源路径.
     * @return 返回修正后的路径.
     */
    public static String fixPath(String path) {
        if (null == path || path.trim().length() == 0) {
            return path;
        }
        if ('/' == path.charAt(0) || '\\' == path.charAt(0)) {
            // 根目录, Windows下需补上盘符.
            if (isWindows()) {
                String userDir = System.getProperty("user.dir");
                if (null != userDir && userDir.length() >= 2) {
                    return userDir.substring(0, 2) + path;
                }
            }
        }
        return path;
    }

    @SneakyThrows
    public static String imageToBase64(String path) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Thumbnails.of(path)
                //.scale(0.01f)
                .size(90, 50)
                .outputQuality(0.05f)
                .outputFormat("png")
                .toOutputStream(baos);
        // 读取文件为byte数组
        byte[] bytes = baos.toByteArray();
        // 将byte数组转换为Base64编码
        String base64 = Base64.encode(bytes);
        return base64;
    }
}
