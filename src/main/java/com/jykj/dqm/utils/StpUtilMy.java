package com.jykj.dqm.utils;

import cn.dev33.satoken.stp.StpUtil;
import com.jykj.dqm.auth.entity.SysUser;

/**
 * 权限相关Util
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/23 15:28:40
 */
public class StpUtilMy extends StpUtil {
    public static String getUserName() {
        try {
            return ((SysUser) StpUtil.getSession().get("userInfo")).getUserName();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getUserAccount() {
        return ((SysUser) StpUtil.getSession().get("userInfo")).getLoginId();
    }

    public static Integer getUserId() {
        return ((SysUser) StpUtil.getSession().get("userInfo")).getUserId();
    }

    public static SysUser getUserInfo() {
        return ((SysUser) StpUtil.getSession().get("userInfo"));
    }
}
