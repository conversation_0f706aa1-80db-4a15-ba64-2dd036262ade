package com.jykj.dqm.utils;

public class CircledNumberConverter {
    // Unicode characters for circled numbers 1 to 10
    private static final char[] CIRCLED_NUMBERS = {
        '①', '②', '③', '④', '⑤',
        '⑥', '⑦', '⑧', '⑨', '⑩'
    };
    // Method to convert a number to its circled representation
    public static String convertToCircledNumber(int number) {
        if (number < 1 || number > 10) {
            throw new IllegalArgumentException("Number must be between 1 and 10");
        }
        return String.valueOf(CIRCLED_NUMBERS[number - 1]);
    }
    public static void main(String[] args) {
        for (int i = 1; i <= 10; i++) {
            System.out.println("Number: " + i + " -> Circled Number: " + convertToCircledNumber(i));
        }
    }
}