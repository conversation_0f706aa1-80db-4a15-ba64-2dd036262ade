package com.jykj.dqm.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jykj.dqm.emr.manager.generateword.RuleTypeEnum;
import com.jykj.dqm.system.entity.SysCodetabContent;
import com.jykj.dqm.system.service.SysCodetabContentService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 码值内容管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/4/20 9:13:27
 */
public class SysCodeContentUtils {
    private static Map<String, Map<String, String>> alarmCoefficientMap = new ConcurrentHashMap<>();

    private static Lock lock = new ReentrantLock();

    /**
     * 获取EMR规则告警系数
     *
     * @return Map<String, String>
     */
    public static Map<String, String> getAlarmCoefficientMap() {
        if (alarmCoefficientMap.containsKey("EMR_RULE_TYPE")) {
            return alarmCoefficientMap.get("EMR_RULE_TYPE");
        }
        lock.lock();
        try {
            //重新判断
            if (alarmCoefficientMap.containsKey("EMR_RULE_TYPE")) {
                return alarmCoefficientMap.get("EMR_RULE_TYPE");
            }
            SysCodetabContentService sysCodetabContentService = SpringUtil.getBean(SysCodetabContentService.class);
            List<SysCodetabContent> list = sysCodetabContentService.list(new LambdaQueryWrapper<SysCodetabContent>().eq(SysCodetabContent::getTypeCode, "EMR_RULE_TYPE"));
            HashMap<String, String> map = new HashMap<>();
            if (CollUtil.isEmpty(list)) {
                //返回默认值
                map.put(RuleTypeEnum.WZX.getValue(), RuleTypeEnum.WZX.getAlarmCoefficient());
                map.put(RuleTypeEnum.YZX.getValue(), RuleTypeEnum.YZX.getAlarmCoefficient());
                map.put(RuleTypeEnum.ZHX.getValue(), RuleTypeEnum.ZHX.getAlarmCoefficient());
                map.put(RuleTypeEnum.JSX.getValue(), RuleTypeEnum.JSX.getAlarmCoefficient());
                return map;
            }
            for (SysCodetabContent sysCodetabContent : list) {
                map.put(sysCodetabContent.getContentValue(), sysCodetabContent.getData1());
            }
            alarmCoefficientMap.put("EMR_RULE_TYPE", map);
            return map;
        } finally {
            lock.unlock();
        }
    }


    public synchronized static Map<String, String> getAlarmCoefficientMapByTypeCode(String typeCode) {
        if (alarmCoefficientMap.containsKey(typeCode)) {
            return alarmCoefficientMap.get(typeCode);
        }
        lock.lock();
        try {
            //重新判断
            if (alarmCoefficientMap.containsKey(typeCode)) {
                return alarmCoefficientMap.get(typeCode);
            }
            SysCodetabContentService sysCodetabContentService = SpringUtil.getBean(SysCodetabContentService.class);
            List<SysCodetabContent> list = sysCodetabContentService.list(new LambdaQueryWrapper<SysCodetabContent>().eq(SysCodetabContent::getTypeCode, typeCode));
            HashMap<String, String> map = new HashMap<>();
            for (SysCodetabContent sysCodetabContent : list) {
                map.put(sysCodetabContent.getContentValue(), sysCodetabContent.getData1());
            }
            alarmCoefficientMap.put(typeCode, map);
            return map;
        } finally {
            lock.unlock();
        }
    }
}
