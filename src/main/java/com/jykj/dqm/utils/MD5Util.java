package com.jykj.dqm.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;

/**
 * MD5工具类--》MD5加密
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
public class MD5Util implements CryptoUtil {
    /**
     * 日志工厂
     */
    private static final Logger logger = LoggerFactory.getLogger(MD5Util.class);

    /**
     * md5加密
     *
     * @param str 指定字符串
     * @return 加密后的字符串
     * <AUTHOR>
     */
    public static String md5(String str) {
        return new MD5Util().encrypt(str);
    }

    /**
     * 实现CryptoUtil接口的加密方法
     *
     * @param str 待加密的字符串
     * @return 加密后的字符串
     */
    @Override
    public String encrypt(String str) {
        str = (str == null ? "" : str);
        char[] hexDigits = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
        try {
            byte[] btInput = str.getBytes();
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            mdInst.update(btInput);
            byte[] md = mdInst.digest();
            int j = md.length;
            char[] strA = new char[j * 2];
            int k = 0;
            for (byte byte0 : md) {
                strA[k++] = hexDigits[byte0 >>> 4 & 0xf];
                strA[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(strA);
        } catch (Exception e) {
            logger.error("MD5加密失败！", e);
            throw new RuntimeException("MD5加密失败！");
        }
    }

    /**
     * 获取加密算法名称
     *
     * @return 加密算法名称
     */
    @Override
    public String getAlgorithmName() {
        return "MD5";
    }
}
