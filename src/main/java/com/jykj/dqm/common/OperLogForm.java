

package com.jykj.dqm.common;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 操作日志查询类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@Data
@ApiModel(value = "操作日志查询类")
public class OperLogForm {
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 分页参数 当前页
     */
    @ApiModelProperty(value = "分页参数 当前页")
    private Integer pageNum = 1;

    /**
     * 分页参数 每页数量
     */
    @ApiModelProperty(value = "分页参数 每页数量")
    private Integer pageSize = 10;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String username;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
}
