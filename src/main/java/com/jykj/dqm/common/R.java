package com.jykj.dqm.common;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;

/**
 * 返回给前端的结果实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/8/20 14:53
 */
@ApiModel(value = "返回给前端的结果实体类")
public class R<T>  implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * error_code 状态值：0 即为成功，其他数值代表失败
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String msg;

    /**
     * content 返回体报文的出参，使用泛型兼容不同的类型
     */
    private T data;

    /**
     * 获取状态码
     *
     * @return 状态码
     * <AUTHOR>
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 获取状态码 设置状态码
     *
     * @param code 状态码
     * <AUTHOR>
     */
    public void setStatus(Integer code) {
        this.status = code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     * <AUTHOR>
     */
    public String getMsg() {
        return msg;
    }

    /**
     * 设置描述信息
     *
     * @param msg 描述信息
     * <AUTHOR>
     */
    public void setMsg(String msg) {
        this.msg = msg;
    }

    /**
     * 获取数据对象
     *
     * @param object 数据对象
     * @return 数据对象
     * <AUTHOR>
     */
    public T getData(Object object) {
        return data;
    }

    /**
     * 设置数据对象
     *
     * @param data 数据对象
     * <AUTHOR>
     */
    public void setData(T data) {
        this.data = data;
    }

    /**
     * 获取数据对象
     *
     * @return 数据对象
     * <AUTHOR>
     */
    public T getData() {
        return data;
    }

    /**
     * 打印响应对象
     *
     * @return 响应对象
     * <AUTHOR>
     */
    @Override
    public String toString() {
        return "Result{" +
                "status=" + status +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';

    }
}