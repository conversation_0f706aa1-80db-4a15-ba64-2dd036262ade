package com.jykj.dqm.common;

/**
 * 公共类,定义一些常量
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/7/25 13:26
 */
public class Constant {
    /**
     * 系统名称
     */
    public static final String SYS_NAME = "DQM";

    /**
     * 账号状态 未启用
     */
    public static final int USER_STATUS_DISABLE = 0;

    /**
     * 账号状态 已启用
     */
    public static final int USER_STATUS_ENABLE = 1;

    /**
     * 账号状态 停用
     */
    public static final int USER_STATUS_STOP = 1;

    /**
     * 管理员角色
     */
    public static final String ADMIN_ROLE = "1";

    /**
     * 脱敏密码
     */
    public static final String PASSWORD = "***###";

    /**
     * 默认长度20
     */
    public static final String DEFAULT_LENGTH = "20";

    /**
     * 换行
     */
    public static final String LINE_SEPARATOR = System.getProperty("line.separator");

    /**
     * YES标记
     */
    public static final String STRING_Y = "Y";

    /**
     * NO标记
     */
    public static final String STRING_N = "N";

    /**
     * 集团编码
     */
    public static final String GROUP_CODE = "01";

    /**
     * 组织编码
     */
    public static final String ORGANIZATION_CODE = "001";

    /**
     * token有效期(单位秒)，默认一天
     */
    public static final int TOKEN_VALIDITY_PERIOD = 86400;

    /**
     * 密码过期提醒时间
     */
    public static final int PASSWORD_EXPIRATION_REMINDER_DAY = 5;

    /**
     * 默认密码过期时间
     */
    public static final int DEFAULT_PASSWORD_EXPIRATION_DAY = 90;

    /**
     * 手机正则表达式
     */
    public static final String MOBILE_REG = "^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\\d{8}$";

    /**
     * DQM登录的唯一Key
     */
    public static final String DQM_REDIS_LOGIN_UUID_KEY = "EMRM_REDIS_LOGIN_UUID_KEY";

    /**
     * 主数据自动同步开关
     */
    public static final String MDM_AUTO_SYNC_SWICTH = "mdm.auto.sync.swicth";

    /**
     * 主数据自动同步时间
     */
    public static final String MDM_AUTO_SYNC_TIME = "mdm.auto.sync.time";

    /**
     * 要求项目名称分隔符
     */
    public static final String REQUIRE_PROJECT_NAME_SEPARATOR = "、";

    /**
     * SQL执行界面，默认返回数据条数
     */
    public static final int DEFAULT_NUMBER_OF_RETURNED_DATA = 10;

    /**
     * 文档预览导出缓存key前缀
     */
    public static final String DOCUMENT_EXPORT_RECORD_CACHE_KEY_PREFIX = "DQM:EMRM:DOCUMENT_EXPORT_RECORD:";
}