package com.jykj.dqm.common;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 文件上传限制
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/23 14:42:04
 */
@Component
@Data
@ConfigurationProperties(prefix = "uploadfile.driver")
public class UploadDriverFile extends UploadFile{
    String winUploadPath;
    String linuxUploadPath;
    Integer fileSize;
    List<String> fileTypes;
}
