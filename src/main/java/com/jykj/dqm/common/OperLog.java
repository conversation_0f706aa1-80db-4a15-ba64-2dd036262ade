

package com.jykj.dqm.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * OPER_LOG 操作日志实体类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@ApiModel(value = "操作日志实体类")
@Data
public class OperLog implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private int id;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 功能模块
     */
    @ApiModelProperty(value = "功能模块")
    private String functionModule;

    /**
     * 操作内容
     */
    @ApiModelProperty(value = "操作内容")
    private String operateContent;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    //@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")    //前端-->后端显示
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")    //后端-->前端。
    private Date operateTime;

    private static final long serialVersionUID = 1L;
}