package com.jykj.dqm.notice.sms;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.setting.Setting;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * 阿里云短信
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/10 15:19:00
 */
@Slf4j
@Service
public class MyAliyunSms {
    public static R sendPin(String msg, String phoneNumbers) {
        Setting setting = new Setting("config/sms.setting");
        //这里填自己的accessKeyId
        String accessKeyId = setting.getStr("accessKeyId");
        //这里填自己的accessKey密码
        String secret = setting.getStr("secret");
        //填自己的模板ID
        String templateCode = setting.getStr("templateCode");
        //填自己的签名
        String signName = setting.getStr("signName");
        //因为大部分情况是内网，所以需要用nginx代理出去
        String sendMsgUrl = setting.getStr("sendMsgUrl");
        // 连接阿里云
        DefaultProfile profile = DefaultProfile.getProfile("default", accessKeyId, secret);

        IAcsClient client = new DefaultAcsClient(profile);
        // 构建请求
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setSysMethod(MethodType.POST);
        // 这些内容不要动，阿里的默认配置
        if (StrUtil.isBlank(sendMsgUrl)) {
            sendMsgUrl = "dysmsapi.aliyuncs.com";
        }
        commonRequest.setSysDomain(sendMsgUrl);
        commonRequest.setSysVersion("2017-05-25");

        // 自己的内容,此处 SendSms 为发送验证码
        commonRequest.setSysAction("SendSms");
        commonRequest.putQueryParameter("RegionId", "cn-hangzhou");

        //自定义的参数(手机号，模板ID，签名 )
        commonRequest.putQueryParameter("PhoneNumbers", phoneNumbers);
        commonRequest.putQueryParameter("TemplateCode", templateCode);
        commonRequest.putQueryParameter("SignName", signName);
        //构建一个短信的验证码
        commonRequest.putQueryParameter("TemplateParam", "{\"code\":\"" + msg + "\"}");
        String reason;
        try {
            CommonResponse response = client.getCommonResponse(commonRequest);
            // 在控制台上打印出返回信息
            log.warn("返回信息-----------------" + response.getData());
            JSONObject resultJson = JSONUtil.parseObj(response.getData());
            String resultCode = resultJson.get("Code").toString();
            // 返回请求信息是否成功
            if ("OK".equalsIgnoreCase(resultCode)) {
                return RUtil.success("发送成功！");
            } else {
                return RUtil.error("发送失败！" + resultJson.get("Message"));
            }
        } catch (ServerException e) {
            log.error("发送失败:" + e.getMessage());
            reason = "发送失败:" + e.getMessage();
        } catch (ClientException e) {
            log.error("发送失败" + e.getMessage());
            reason = "发送失败:" + e.getMessage();
        }
        return RUtil.error(reason);
    }

    public static R sendPin8Hospital(String msg, String phoneNumbers) {
        String reason;
        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("CorpID", "LKSDK0003641");
            paramMap.put("Pwd", "jkwqm814");
            paramMap.put("Mobile", phoneNumbers);
            //使用hutool不用特殊处理
            //String encoding = "GB2312";
            //String encodedContent = URLEncoder.encode(msg, encoding);
            //System.out.println(encodedContent);
            paramMap.put("Content", msg);
            String result = HttpUtil.post("https://sdk1.mb345.com:6789/ws/BatchSend2.aspx", paramMap);
            // 在控制台上打印出返回信息
            log.warn("返回信息-----------------" + result);
            // 返回请求信息是否成功
            if (Integer.parseInt(result) > 0) {
                log.info("短信发送成功！" + phoneNumbers);
                return RUtil.success("发送成功！");
            } else {
                return RUtil.error("发送失败！" + result);
            }
        } catch (Exception e) {
            log.error("发送失败:" + e.getMessage());
            reason = "发送失败:" + e.getMessage();
        }
        return RUtil.error(reason);
    }

    public static R sendPinBySystemConfig(String msg, String phoneNumbers) {
        SysConfig sysConfig = RedisUtil.getSysConfigByName("aliyun.accessKeyId");
        //这里填自己的accessKeyId
        String accessKeyId = sysConfig.getConfigValue();
        sysConfig = RedisUtil.getSysConfigByName("aliyun.secret");
        //这里填自己的accessKey密码
        String secret = sysConfig.getConfigValue();
        sysConfig = RedisUtil.getSysConfigByName("aliyun.templateCode");
        //填自己的模板ID
        String templateCode = sysConfig.getConfigValue();
        sysConfig = RedisUtil.getSysConfigByName("aliyun.signName");
        //填自己的签名
        String signName = sysConfig.getConfigValue();
        // 连接阿里云
        //DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, secret);
        DefaultProfile profile = DefaultProfile.getProfile("default", accessKeyId, secret);

        IAcsClient client = new DefaultAcsClient(profile);

        // 构建请求
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setSysMethod(MethodType.POST);
        // 这些内容不要动，阿里的默认配置,因为大部分情况是内网，所以需要用nginx代理出去
        String sendMsgUrl = RedisUtil.getSysConfigByName("aliyun.sendmsg.nginx.url").getConfigValue();
        if (StrUtil.isBlank(sendMsgUrl)) {
            sendMsgUrl = "dysmsapi.aliyuncs.com";
        }
        commonRequest.setSysDomain(sendMsgUrl);
        commonRequest.setSysVersion("2017-05-25");

        // 自己的内容,此处 SendSms 为发送验证码
        commonRequest.setSysAction("SendSms");
        commonRequest.putQueryParameter("RegionId", "cn-hangzhou");

        //自定义的参数(手机号，模板ID，签名 )
        commonRequest.putQueryParameter("PhoneNumbers", phoneNumbers);
        commonRequest.putQueryParameter("TemplateCode", templateCode);
        commonRequest.putQueryParameter("SignName", signName);
        //构建一个短信的验证码
        String code = msg;
        commonRequest.putQueryParameter("TemplateParam", "{\"code\":\"" + code + "\"}");
        String reason;
        try {
            CommonResponse response = client.getCommonResponse(commonRequest);
            // 在控制台上打印出返回信息
            log.warn("返回信息-----------------" + response.getData());
            JSONObject resultJson = JSONUtil.parseObj(response.getData());
            String resultCode = resultJson.get("Code").toString();
            // 返回请求信息是否成功
            if ("OK".equalsIgnoreCase(resultCode)) {
                return RUtil.success("发送成功！");
            } else {
                return RUtil.error("发送失败！" + resultJson.get("Message"));
            }
        } catch (ServerException e) {
            log.error("发送失败:" + e.getMessage());
            reason = "发送失败:" + e.getMessage();
        } catch (ClientException e) {
            log.error("发送失败" + e.getMessage());
            reason = "发送失败:" + e.getMessage();
        }
        return RUtil.error(reason);
    }
}
