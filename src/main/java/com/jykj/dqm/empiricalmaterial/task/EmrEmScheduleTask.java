package com.jykj.dqm.empiricalmaterial.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialExportRecord;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialExportRecordService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialExportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 定时任务
 * 1、兜底，执行未开始的预览文档生成任务
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 24/1/31 17:47:31
 */
@Slf4j
@Configuration
@EnableScheduling
public class EmrEmScheduleTask {
    @Autowired
    private EmpiricalMaterialExportService exportService;

    @Autowired
    private EmpiricalMaterialExportRecordService exportRecordService;

    @Value("${system.enable.emrm}")
    private boolean enableEmr;

    @Scheduled(fixedRate = 10 * 60 * 1000, initialDelay = 20000)
    void doEmExport() {
        if (!enableEmr) {
            return;
        }
        try {
            log.info("开始执行未开始的任务Em" + LocalDateTime.now());
            LambdaQueryWrapper<EmpiricalMaterialExportRecord> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(EmpiricalMaterialExportRecord::getExportStatus, "0");
            queryWrapper.lt(EmpiricalMaterialExportRecord::getTimedExportTime, new Date());
            List<EmpiricalMaterialExportRecord> documentExportRecords = exportRecordService.list(queryWrapper);
            String id;
            for (EmpiricalMaterialExportRecord documentExportRecord : documentExportRecords) {
                id = documentExportRecord.getId();
                exportService.exportAll(id);
                log.info("执行未开始的任务Em:{}", documentExportRecord);
            }
        } catch (Throwable e) {
            //保证定时任务不会因为异常退出
            log.error("检查EMR-Em导出任务状态的任务", e);
        }
    }
}
