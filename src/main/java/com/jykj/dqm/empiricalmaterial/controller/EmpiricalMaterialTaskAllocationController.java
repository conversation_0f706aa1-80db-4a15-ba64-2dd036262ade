package com.jykj.dqm.empiricalmaterial.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialTaskAllocationSaveDTO;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialTaskAllocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

@Api(tags = {"材料任务分配及进度"})
@RestController
@RequestMapping("/emm/empiricalmaterial/taskallocation")
public class EmpiricalMaterialTaskAllocationController {
    @Autowired
    private EmpiricalMaterialTaskAllocationService empiricalMaterialTaskAllocationService;

    @LogRemark(operate = "保存材料任务配置", module = "材料任务分配及进度")
    @ApiOperation(value = "保存材料任务配置(勾选多少传多少，不传父目录，只传最后一级目录)", notes = "材料任务分配及进度")
    @PostMapping("/save")
    public R save(@Validated @RequestBody EmpiricalMaterialTaskAllocationSaveDTO taskAllocationSaveDTO) {
        return empiricalMaterialTaskAllocationService.save(taskAllocationSaveDTO);
    }

    @ApiOperation(value = "根据用户账号查询其负责的材料任务", notes = "材料任务分配及进度")
    @GetMapping("/getTask")
    public R getTask(String userAccount, @NotBlank(message = "等级不能为空！") String levelCode, @NotBlank(message = "项目ID不能为空！") String projectId) {
        EmpiricalMaterialTaskAllocationSaveDTO taskAllocationSaveDTO = empiricalMaterialTaskAllocationService.getTask(userAccount, levelCode, projectId);
        return RUtil.success(taskAllocationSaveDTO);
    }

    @ApiOperation(value = "查询用户及任务情况", notes = "材料任务分配及进度")
    @GetMapping("/getUserListAndTasks")
    public R getUserListAndTasks(@RequestParam(required = false) String levelCode,
                                 @RequestParam(required = false) String userName,
                                 @NotBlank @RequestParam("projectId") String projectId) {
        return empiricalMaterialTaskAllocationService.getUserListAndTasks(levelCode, userName, null, projectId);
    }

    @ApiOperation(value = "查询目录树形", notes = "材料任务分配及进度")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "userAccount", value = "用户账号，没有传则查全部", required = false, dataType = "String"),
            @ApiImplicitParam(name = "levelCode", value = "等级", required = true, dataType = "String"),
            @ApiImplicitParam(name = "evaluationCategory", value = "评价类别", required = false, dataType = "String"),
            @ApiImplicitParam(name = "needNotAllocationTask", value = "是否需要未分配的任务", required = false, dataType = "boolean")
    })
    @GetMapping("/queryDirectoryTree")
    public R queryDirectoryTree(@RequestParam(required = false) String userAccount,
                                @NotBlank @RequestParam("projectId") String projectId,
                                @NotBlank(message = "等级不能为空！") @RequestParam String levelCode,
                                @RequestParam(required = false) String evaluationCategory,
                                @RequestParam(defaultValue = "true") boolean needNotAllocationTask) {
        return empiricalMaterialTaskAllocationService.queryDirectoryTree(userAccount, levelCode, evaluationCategory, needNotAllocationTask, projectId);
    }
}
