package com.jykj.dqm.empiricalmaterial.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialExportRecordQuery;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialExportRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_EMPIRICAL_MATERIAL_EXPORT_RECORD(实证材料导出记录)】的数据库操作Controller
 * @createDate 2024-01-30 17:43:27
 */
@Api(tags = {"实证材料导出记录"})
@RestController
@RequestMapping("/emm/empiricalmaterial/record")
public class EmpiricalMaterialExportRecordController {
    //查询
    //导出文档
    @Autowired
    private EmpiricalMaterialExportRecordService exportRecordService;

    @ApiOperation(value = "查询实证材料导出记录", notes = "实证材料导出记录")
    @PostMapping("/query")
    public R query(@Validated @RequestBody EmpiricalMaterialExportRecordQuery empiricalMaterialExportRecordQuery) {
        return exportRecordService.query(empiricalMaterialExportRecordQuery);
    }

    /**
     * 删除实证材料导出文档记录
     *
     * @param exportRecordId exportRecordId
     * <AUTHOR>
     */
    @ApiOperation(value = "删除实证材料导出文档", notes = "实证材料导出记录")
    @LogRemark(operate = "删除实证材料导出文档", module = "实证材料导出记录")
    @DeleteMapping("/delete/{exportRecordId}")
    public R delete(@PathVariable String exportRecordId) {
        return exportRecordService.delete(exportRecordId);
    }

}
