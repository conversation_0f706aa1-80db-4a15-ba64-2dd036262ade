package com.jykj.dqm.empiricalmaterial.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.config.LogRemark;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPath;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPathDTO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPathQuery;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialFlowPathService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_EMPIRICAL_MATERIAL_FLOW_PATH(实证材料流程和资料)】的数据库操作
 * @createDate 2024-01-23 14:25:03
 */
@Api(tags = {"实证材料证明上传"})
@RestController
@RequestMapping("/emm/empiricalmaterial/flowpath")
public class EmpiricalMaterialFlowPathController {
    @Autowired
    private EmpiricalMaterialFlowPathService empiricalMaterialFlowPathService;

    @LogRemark(operate = "新增实证材料证明", module = "实证材料证明")
    @ApiOperation(value = "新增实证材料证明", notes = "实证材料证明")
    @PostMapping("/add")
    public R add(@Validated EmpiricalMaterialFlowPath materialFlowPath, @RequestPart @RequestParam(value = "file", required = false) List<MultipartFile> file) {
        //前端不好修改参数名，所以适配他使用files->file
        return empiricalMaterialFlowPathService.add(materialFlowPath, file);
    }

    @LogRemark(operate = "更新实证材料证明", module = "实证材料证明")
    @ApiOperation(value = "更新实证材料证明", notes = "实证材料证明")
    @PostMapping("/update")
    public R update(@Validated EmpiricalMaterialFlowPath materialFlowPath, @RequestPart @RequestParam(value = "file", required = false) List<MultipartFile> file) {
        //前端不好修改参数名，所以适配他使用files->file
        return empiricalMaterialFlowPathService.update(materialFlowPath, file);
    }

    @LogRemark(operate = "保存实证材料证明", module = "实证材料证明")
    @ApiOperation(value = "保存实证材料证明", notes = "实证材料证明")
    @PostMapping("/save")
    public R save(@Validated @RequestBody EmpiricalMaterialFlowPathDTO empiricalMaterialFlowPathDTO) {
        return empiricalMaterialFlowPathService.save(empiricalMaterialFlowPathDTO);
    }

    @LogRemark(operate = "删除实证材料证明", module = "实证材料证明")
    @ApiOperation(value = "删除实证材料证明", notes = "实证材料证明")
    @PostMapping("/delete")
    public R delete(@Validated EmpiricalMaterialFlowPath materialFlowPath) {
        return empiricalMaterialFlowPathService.delete(materialFlowPath);
    }

    @LogRemark(operate = "标记完成实证材料证明", module = "实证材料证明")
    @ApiOperation(value = "标记完成实证材料证明", notes = "实证材料证明")
    @PostMapping("/markComplete")
    public R markComplete(@RequestBody EmpiricalMaterialFlowPathQuery materialFlowPathQuery) {
        return empiricalMaterialFlowPathService.markComplete(materialFlowPathQuery.getDirectoryName(), materialFlowPathQuery.getDirectoryCode(), materialFlowPathQuery.getProjectId());
    }

    @ApiOperation(value = "查询实证材料证明", notes = "实证材料证明")
    @PostMapping("/query")
    public R query(@Validated @RequestBody EmpiricalMaterialFlowPathQuery materialFlowPathQuery) {
        return RUtil.success(empiricalMaterialFlowPathService.query(materialFlowPathQuery));
    }

    @ApiOperation(value = "查询实证材料证明图片", notes = "实证材料证明")
    @PostMapping("/queryAll")
    public R queryAll(@Validated @RequestBody EmpiricalMaterialFlowPathQuery materialFlowPathQuery) {
        return empiricalMaterialFlowPathService.queryAll(materialFlowPathQuery);
    }


    @ApiOperation(value = "上传实证材料（支持同时上传多个文件）", notes = "实证材料证明")
    @LogRemark(operate = "上传实证材料", module = "实证材料证明")
    @PostMapping("/uploadMultipleFiles")
    public R uploadMultipleFiles(@RequestParam("files") List<MultipartFile> files,
                                 @NotBlank @RequestParam("materialFlowPathId") String materialFlowPathId,
                                 @NotBlank @RequestParam("projectId") String projectId,
                                 @NotBlank @RequestParam("directoryName") String directoryName,
                                 @NotBlank @RequestParam("directoryCode") String directoryCode,
                                 @NotBlank @RequestParam("evaluationContentId") String evaluationContentId) {
        return RUtil.success(empiricalMaterialFlowPathService.uploadMultipleFiles(files, materialFlowPathId, projectId, directoryName, directoryCode, evaluationContentId));
    }

    @ApiOperation(value = "获取图片", httpMethod = "GET", produces = "application/octet-stream")
    @LogRemark(operate = "获取图片", module = "实证材料证明")
    @GetMapping("/getImages")
    public void getImages(HttpServletResponse response,
                          @NotBlank @RequestParam("materialFlowPathId") String materialFlowPathId,
                          @RequestParam(value = "fileName", required = false) String fileName,
                          @NotBlank @RequestParam("projectId") String projectId,
                          @NotBlank @RequestParam("directoryName") String directoryName,
                          @NotBlank @RequestParam("directoryCode") String directoryCode,
                          @NotBlank @RequestParam("evaluationContentId") String evaluationContentId) {
        empiricalMaterialFlowPathService.getImages(response, fileName, materialFlowPathId, projectId, directoryName, directoryCode, evaluationContentId);
    }

}