package com.jykj.dqm.empiricalmaterial.service;

import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialExportDTO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPathQuery;

public interface EmpiricalMaterialExportService {
    void asyncExportOneDirectory(String directoryName, String directoryCode,String projectId);

    void previewWord(EmpiricalMaterialFlowPathQuery documentPreview);

    void downloadDoc(String exportRecordId);

    void exportAsync(EmpiricalMaterialExportDTO empiricalMaterialExportDTO, String username, String tokenValue);

    void exportAll(String exportRecordId);
}
