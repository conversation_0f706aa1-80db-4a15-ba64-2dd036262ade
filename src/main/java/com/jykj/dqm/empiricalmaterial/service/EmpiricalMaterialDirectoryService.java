package com.jykj.dqm.empiricalmaterial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectory;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectoryQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_EMPIRICAL_MATERIAL_DIRECTORY(实证材料目录)】的数据库操作Service
 * @createDate 2024-01-22 13:40:46
 */
public interface EmpiricalMaterialDirectoryService extends IService<EmpiricalMaterialDirectory> {

    R add(EmpiricalMaterialDirectory empiricalMaterialDirectory);

    R update(EmpiricalMaterialDirectory empiricalMaterialDirectory);

    R delete(List<Long> ids);

    R query(EmpiricalMaterialDirectoryQuery empiricalMaterialDirectory);

    R batchUpdateMy(List<EmpiricalMaterialDirectory> empiricalMaterialDirectories);
}
