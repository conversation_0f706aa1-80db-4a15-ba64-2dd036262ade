package com.jykj.dqm.empiricalmaterial.service;

import com.jykj.dqm.common.R;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialExportRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialExportRecordQuery;

/**
* <AUTHOR>
* @description 针对表【DQM_EMR_EMPIRICAL_MATERIAL_EXPORT_RECORD(实证材料导出记录)】的数据库操作Service
* @createDate 2024-01-30 17:43:27
*/
public interface EmpiricalMaterialExportRecordService extends IService<EmpiricalMaterialExportRecord> {

    R query(EmpiricalMaterialExportRecordQuery empiricalMaterialExportRecordQuery);

    R delete(String exportRecordId);
}
