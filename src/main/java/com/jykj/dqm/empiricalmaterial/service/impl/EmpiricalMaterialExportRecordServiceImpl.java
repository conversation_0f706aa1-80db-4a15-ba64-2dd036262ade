package com.jykj.dqm.empiricalmaterial.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.empiricalmaterial.dao.EmpiricalMaterialExportRecordMapper;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialExportRecord;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialExportRecordQuery;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialExportRecordService;
import com.jykj.dqm.utils.SystemUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_EMPIRICAL_MATERIAL_EXPORT_RECORD(实证材料导出记录)】的数据库操作Service实现
 * @createDate 2024-01-30 17:43:27
 */
@Service
public class EmpiricalMaterialExportRecordServiceImpl extends ServiceImpl<EmpiricalMaterialExportRecordMapper, EmpiricalMaterialExportRecord>
        implements EmpiricalMaterialExportRecordService {
    @Override
    public R query(EmpiricalMaterialExportRecordQuery documentExportRecordQuery) {
        PageHelper.startPage(documentExportRecordQuery.getPageNum(), documentExportRecordQuery.getPageSize());
        LambdaQueryWrapper<EmpiricalMaterialExportRecord> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(StrUtil.isNotBlank(documentExportRecordQuery.getExportStatus()), EmpiricalMaterialExportRecord::getExportStatus, documentExportRecordQuery.getExportStatus());
        lambdaQueryWrapper.eq(EmpiricalMaterialExportRecord::getProjectId, documentExportRecordQuery.getProjectId());
        //单独写if，因为Children lt(boolean condition, R column, Object val);就算condition==false，后面的var表达式也会运算
        if (StrUtil.isNotBlank(documentExportRecordQuery.getStartTime())) {
            lambdaQueryWrapper.ge(EmpiricalMaterialExportRecord::getCreateTime, DateUtil.parse(documentExportRecordQuery.getStartTime()));
        }
        if (StrUtil.isNotBlank(documentExportRecordQuery.getEndTime())) {
            documentExportRecordQuery.setEndTime(documentExportRecordQuery.getEndTime() + " 23:59:59");
            lambdaQueryWrapper.le(EmpiricalMaterialExportRecord::getCreateTime, DateUtil.parse(documentExportRecordQuery.getEndTime()));
        }
        lambdaQueryWrapper.eq(StrUtil.isNotBlank(documentExportRecordQuery.getExportDocumentLevel()), EmpiricalMaterialExportRecord::getExportDocumentLevel, documentExportRecordQuery.getExportDocumentLevel());
        lambdaQueryWrapper.orderByDesc(EmpiricalMaterialExportRecord::getCreateTime);
        List<EmpiricalMaterialExportRecord> list = this.list(lambdaQueryWrapper);
        PageInfo<EmpiricalMaterialExportRecord> pageInfo = new PageInfo<>(list);
        return RUtil.success(pageInfo);
    }

    @Override
    public R delete(String exportRecordId) {
        this.removeById(exportRecordId);
        //删除文件
        String parent = SystemUtils.getFilePath();
        String filePath = parent + "/MaterialFlowPathFiles/" + exportRecordId + "/";
        FileUtil.del(filePath);
        return RUtil.success();
    }
}
