package com.jykj.dqm.empiricalmaterial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialTaskAllocation;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialTaskAllocationSaveDTO;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_EMPIRICAL_MATERIAL_TASK_ALLOCATION(实证材料任务分配)】的数据库操作Service
 * @createDate 2024-01-22 16:13:16
 */
public interface EmpiricalMaterialTaskAllocationService extends IService<EmpiricalMaterialTaskAllocation> {

    R save(EmpiricalMaterialTaskAllocationSaveDTO taskAllocationSaveDTO);

    EmpiricalMaterialTaskAllocationSaveDTO getTask(String userAccount, String levelCode, String projectId);

    R getUserListAndTasks(String levelCode, String userName, String userAccount, String projectId);

    R queryDirectoryTree(String userAccount, String levelCode, String evaluationCategory, boolean needNotAllocationTask, String projectId);
}
