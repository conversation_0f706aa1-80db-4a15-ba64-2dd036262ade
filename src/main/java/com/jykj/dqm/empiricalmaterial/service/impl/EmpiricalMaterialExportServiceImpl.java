package com.jykj.dqm.empiricalmaterial.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepoove.poi.data.Includes;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectory;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialEvaluationContent;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialExportDTO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialExportRecord;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowFirstTreeVO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPath;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPathQuery;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowSecondTree;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialTaskAllocation;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialDirectoryService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialEvaluationContentService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialExportRecordService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialExportService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialFlowPathService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialTaskAllocationService;
import com.jykj.dqm.emr.config.TransmittableThreadLocalManager;
import com.jykj.dqm.emr.entity.LevelCodeEnum;
import com.jykj.dqm.emr.manager.WordUtils;
import com.jykj.dqm.emr.task.DelayQueueManager;
import com.jykj.dqm.emr.task.DelayTask;
import com.jykj.dqm.emr.task.TaskBaseEntity;
import com.jykj.dqm.emr.utils.PathNameUtils;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.SystemUtils;
import com.jykj.dqm.utils.WordUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpiricalMaterialExportServiceImpl implements EmpiricalMaterialExportService {
    @Autowired
    private EmpiricalMaterialDirectoryService directoryService;

    @Autowired
    private EmpiricalMaterialEvaluationContentService evaluationContentService;

    @Autowired
    @Lazy
    private EmpiricalMaterialFlowPathService flowPathService;

    @Autowired
    private EmpiricalMaterialTaskAllocationService materialTaskAllocationService;

    @Autowired
    private EmpiricalMaterialExportRecordService empiricalMaterialExportRecordService;

    @Autowired
    private DelayQueueManager delayQueueManager;

    @Autowired
    private HttpServletResponse response;

    @Resource(name = "threadPoolMonitor")
    ThreadPoolExecutor threadPoolMonitor;

    @Override
    @Async("doExportExecutor")
    public void asyncExportOneDirectory(String directoryName, String directoryCode, String projectId) {
        WordUtils wordUtils = new WordUtils();
        //异步导出每个章节(还要增加一个定时任务来扫)
        //1、表格内容填充
        //2、每个内容对应的流程+图片
        //3、组合成一个完整的章节
        String basePath = "";
        try {
            Map<String, Object> params = new HashMap<>();
            LambdaQueryWrapper<EmpiricalMaterialDirectory> lambdaQueryWrapper = Wrappers.lambdaQuery(EmpiricalMaterialDirectory.class)
                    .eq(EmpiricalMaterialDirectory::getDirectoryCode, directoryCode)
                    .eq(EmpiricalMaterialDirectory::getDirectoryName, directoryName);
            EmpiricalMaterialDirectory directory = directoryService.getOne(lambdaQueryWrapper);
            lambdaQueryWrapper = Wrappers.lambdaQuery(EmpiricalMaterialDirectory.class)
                    .eq(EmpiricalMaterialDirectory::getDirectoryCode, directoryCode.substring(0, 5));
            EmpiricalMaterialDirectory secondDirectory = directoryService.getOne(lambdaQueryWrapper);
            params.put("directoryCode", directory.getDirectoryCode());
            params.put("workRole", secondDirectory.getDirectoryName());
            params.put("project", secondDirectory.getBusinessProject());
            params.put("number", secondDirectory.getProjectNum());
            params.put("evaluationType", "0".equals(directory.getEvaluationCategory()) ? "基本" : "选择");
            params.put("tableDesc", directory.getRemarksDesc());
            List<EmpiricalMaterialEvaluationContent> evaluationContents =
                    evaluationContentService.list(Wrappers.lambdaQuery(EmpiricalMaterialEvaluationContent.class)
                            .eq(EmpiricalMaterialEvaluationContent::getDirectoryCode, directoryCode)
                            .eq(EmpiricalMaterialEvaluationContent::getDirectoryName, directoryName));
            TransmittableThreadLocalManager.set("notImage");
            EmpiricalMaterialFlowPathQuery materialFlowPathQuery;
            EmpiricalMaterialFlowFirstTreeVO flowFirstTreeVO;
            Map<String, Object> flowPictureData;
            List<Map<String, Object>> flowPictureDataList;
            String parent = SystemUtils.getFilePath();
            StringBuilder evaluationContentStr = new StringBuilder();
            Map<String, Object> eachMap;
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> flowPathMap = new HashMap<>();
            int i = 0;
            int size = evaluationContents.size();
            boolean horizontal = false;
            SysConfig sysConfigByName = RedisUtil.getSysConfigByName("empiricalmaterial.word.horizontal");
            if (sysConfigByName != null) {
                if ("Y".equals(sysConfigByName.getConfigValue())) {
                    horizontal = true;
                }
            }
            int j = 0;
            for (EmpiricalMaterialEvaluationContent evaluationContent : evaluationContents) { //评价内容
                eachMap = new LinkedHashMap<>();
                i++;
                if (size > 1) {
                    evaluationContentStr.append("(").append(i).append(")");
                }
                evaluationContentStr.append(evaluationContent.getEvaluationContent() + "                                ");
                materialFlowPathQuery = new EmpiricalMaterialFlowPathQuery()
                        .setProjectId(projectId)
                        .setDirectoryCode(directoryCode)
                        .setDirectoryName(directoryName)
                        .setEvaluationContentId(evaluationContent.getId());
                //去掉Base64图片
                flowFirstTreeVO = flowPathService.query(materialFlowPathQuery);
                if (flowFirstTreeVO.getAllFlowPath() == null) {
                    continue;
                }
                flowPictureDataList = new ArrayList<>();
                basePath = parent + "/MaterialFlowPathFiles/" + directoryName + directoryCode + projectId + "/";
                int maxWidth = 555; // 最大宽度
                int maxHeight = 318; // 最大高度
                if (horizontal) {
                    maxWidth = 980; // 最大宽度
                    maxHeight = 640; // 最大高度
                }
                j = 0;
                for (EmpiricalMaterialFlowSecondTree flowSecondTree : flowFirstTreeVO.getGroupFlowPath()) {//子流程
                    j++;
                    if (CollUtil.isEmpty(flowSecondTree.getChildren())) {
                        flowPictureData = new HashMap<>();
                        flowPictureData.put("childContent", j + ")" + flowSecondTree.getFlowPath()); //+病房医嘱处理
                        flowPictureDataList.add(flowPictureData);
                        continue;
                    }
                    boolean isFirst = true;
                    for (EmpiricalMaterialFlowPath treeChild : flowSecondTree.getChildren()) {//实现方式
                        //图片路径
                        String directoryPath = basePath + evaluationContent.getId();
                        int count = 0;
                        //可能存在多张图片
                        String[] imageNames = Optional.ofNullable(treeChild.getFlowPathData()).orElse("").split(",");
                        String appendCount = " ";
                        for (String imageName : imageNames) {//多张图片
                            String fullPath = directoryPath + "/" + treeChild.getId() + "/" + imageName;
                            String fullPathNum = treeChild.getSerialNum().substring(1) + appendCount + treeChild.getFlowPath();
                            flowPictureData = new HashMap<>();
                            if (isFirst) {
                                flowPictureData.put("childContent", j + ")" + flowSecondTree.getFlowPath()); //+病房医嘱处理
                            }
                            dualPicture(fullPath, maxWidth, maxHeight, flowPictureData, isFirst, fullPathNum.length());
                            count++;
                            if (imageNames.length > 1) {
                                appendCount = "_" + count + " ";
                            }
                            flowPictureData.put("fullPathNum", fullPathNum);
                            flowPictureDataList.add(flowPictureData);
                            isFirst = false;
                        }
                    }
                }
                //生成emEachContent
                wordUtils.generateEmWordForEachEvaluationContent(basePath + evaluationContent.getId() + ".docx",
                        MapUtil.builder(new HashMap<String, Object>())
                                .put("flowPictureData", flowPictureDataList)
                                .build());
                eachMap.put("content", "(" + evaluationContent.getSerialNum() + ")" + evaluationContent.getEvaluationContent());
                eachMap.put("allFlowDesc", flowFirstTreeVO.getAllFlowPath().getFlowPath());
                eachMap.put("eachFlowPath", "{{+w" + evaluationContent.getId() + "}}");
                flowPathMap.put("w" + evaluationContent.getId(), Includes.ofLocal(basePath + evaluationContent.getId() + ".docx").create());
                list.add(eachMap);
            }
            //去掉evaluationContentStr的最后一个 Constant.LINE_SEPARATOR
            //evaluationContentStr = new StringBuilder(evaluationContentStr.substring(0, evaluationContentStr.length() - Constant.LINE_SEPARATOR.length()));
            params.put("evaluationContent", evaluationContentStr.toString());
            params.put("evaluationcontentData", list);
            wordUtils.generateEmWordForEachChapter(basePath + directoryName + directoryCode + "tmp.docx", params);
            //生成完整的章节
            wordUtils.generateEmWordForEachChapterComplete(
                    basePath + directoryName + directoryCode + "tmp.docx",
                    basePath + directoryName + directoryCode + ".docx"
                    , flowPathMap);
        } catch (Exception e) {
            wordUtils.dealEmErrorDoc(e, basePath + directoryName + directoryCode + ".docx");
            e.printStackTrace();
        } finally {
            TransmittableThreadLocalManager.remove();
        }
    }

    private void dualPicture(String fullPath, int maxWidth, int maxHeight, Map<String, Object> flowPictureData, boolean isFirst, int fullPathNumLength) throws IOException {
        //方案一：防止图片变红
        Image image = Toolkit.getDefaultToolkit().getImage(fullPath);
        BufferedImage bufferedImage = toBufferedImage(image);

        //方案二：防止图片变红
        //BufferedImage bufferImg_item = ImageIO.read(Files.newInputStream(Paths.get(fullPath)));
        //BufferedImage bufferedImage= new BufferedImage(bufferImg_item.getWidth(),bufferImg_item.getHeight(),BufferedImage.TYPE_INT_RGB);
        //bufferedImage.getGraphics().drawImage(bufferImg_item, 0, 0, null);
        if (isFirst) {
            maxHeight = maxHeight - 23;
        }

        maxHeight = maxHeight - 23 * ((fullPathNumLength / 61) + 1);

        int height = bufferedImage.getHeight();//原始高度
        int width = bufferedImage.getWidth();//原始宽度
        // 缩放比例
        double scale = Math.min(1.0 * maxWidth / width, 1.0 * maxHeight / height);
        int heightNow = (int) (1.0 * height * scale);
        flowPictureData.put("picture", Pictures.ofBufferedImage(bufferedImage, PictureType.suggestFileType(fullPath)).size((int) (1.0 * width * scale), heightNow).create());
        //if (heightNow < maxHeight) {
        //    StringBuilder stringBuilder = new StringBuilder();
        //    int count = (maxHeight - heightNow) / 23;
        //    for (int i = 0; i < count; i++) {
        //        stringBuilder.append(Constant.LINE_SEPARATOR);
        //    }
        //    flowPictureData.put("fillin", stringBuilder.toString());
        //}
    }

    private BufferedImage toBufferedImage(Image image) {
        if (image instanceof BufferedImage) {
            return (BufferedImage) image;
        }
        // This code ensures that all the pixels in the image are loaded
        image = new ImageIcon(image).getImage();
        BufferedImage bimage = null;
        GraphicsEnvironment ge = GraphicsEnvironment
                .getLocalGraphicsEnvironment();
        try {
            int transparency = Transparency.OPAQUE;
            GraphicsDevice gs = ge.getDefaultScreenDevice();
            GraphicsConfiguration gc = gs.getDefaultConfiguration();
            bimage = gc.createCompatibleImage(image.getWidth(null),
                    image.getHeight(null), transparency);
        } catch (HeadlessException e) {
            // The system does not have a screen
        }
        if (bimage == null) {
            // Create a buffered image using the default color model
            int type = BufferedImage.TYPE_INT_RGB;
            bimage = new BufferedImage(image.getWidth(null),
                    image.getHeight(null), type);
        }
        // Copy image to buffered image
        Graphics g = bimage.createGraphics();
        // Paint the image onto the buffered image
        g.drawImage(image, 0, 0, null);
        g.dispose();
        return bimage;
    }

    @Override
    public void previewWord(EmpiricalMaterialFlowPathQuery documentPreview) {
        String parent = SystemUtils.getFilePath();
        String basePath = parent + "/MaterialFlowPathFiles/" + documentPreview.getDirectoryName() + documentPreview.getDirectoryCode() + documentPreview.getProjectId() + "/";
        //然后目录下文件传输到页面
        String filePath = basePath + documentPreview.getDirectoryName() + documentPreview.getDirectoryCode() + ".docx";
        if (!FileUtil.exist(filePath)) {
            WordUtils wordUtils = new WordUtils();
            RuntimeException runtimeException = new RuntimeException(filePath + "文件不存在");
            wordUtils.dealEmErrorDoc(runtimeException, filePath);
        }
        try {
            InputStream input = new FileInputStream(filePath);
            WordUtil.docxToHtml(input, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void downloadDoc(String exportRecordId) {
        //基本和选择分开导，再压缩成一个zip
        EmpiricalMaterialExportRecord exportRecord = empiricalMaterialExportRecordService.getById(exportRecordId);
        String parent = SystemUtils.getFilePath();
        String filePath = parent + "/MaterialFlowPathFiles/" + exportRecordId + "/";
        SysConfig sysConfigByName = RedisUtil.getSysConfigByName("hospital.name");
        String hospitalName = sysConfigByName == null ? "" : sysConfigByName.getConfigValue();
        FileInputStream fileInputStream = null;
        String fileName = hospitalName + exportRecord.getExportDocumentLevel() + "级实证材料.zip";
        try {
            response.setContentType("application/zip;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLUtil.encode(fileName));
            List<File> files = new ArrayList<>();
            for (File file : FileUtil.loopFiles(filePath)) {
                if (file.getName().contains("实证材料")) {
                    files.add(file);
                }
            }
            File file = ZipUtil.zip(FileUtil.file(filePath + "all.zip"), false, files.toArray(new File[]{}));
            response.setHeader("Content-Transfer-Encoding", "binary");
            fileInputStream = new FileInputStream(file);
            IOUtils.copy(fileInputStream, response.getOutputStream());
            response.flushBuffer();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage(), e);
        } finally {
            IoUtil.close(fileInputStream);
        }
    }

    @Async(value = "doExportExecutor")
    @Override
    public void exportAsync(EmpiricalMaterialExportDTO empiricalMaterialExportDTO, String username, String tokenValue) {
        //基本和选择分开导
        String exportRecordId = empiricalMaterialExportDTO.getUniqueId();

        EmpiricalMaterialExportRecord exportRecord = new EmpiricalMaterialExportRecord()
                .setExportStatus("1")
                .setProjectId(empiricalMaterialExportDTO.getProjectId())
                .setId(exportRecordId)
                .setExportDocumentLevel(empiricalMaterialExportDTO.getLevelCode())
                .setDataStartTime(DateUtil.now());

        exportRecord.setCreateBy(username);
        try {
            long count = materialTaskAllocationService.count(
                    Wrappers.lambdaQuery(EmpiricalMaterialTaskAllocation.class)
                            .eq(EmpiricalMaterialTaskAllocation::getLevelCode, empiricalMaterialExportDTO.getLevelCode())
                            .eq(EmpiricalMaterialTaskAllocation::getTaskStatus, "2")
            );
            exportRecord.setExportDocumentTemplatesNum((int) count);
            if (StrUtil.isNotBlank(empiricalMaterialExportDTO.getTimedExportTime())) {
                long between = DateUtil.between(DateUtil.parse(empiricalMaterialExportDTO.getTimedExportTime()), new Date(), DateUnit.MS, true);
                delayQueueManager.put(new DelayTask(new TaskBaseEntity(exportRecordId, "doEMExport"), between));
                exportRecord.setExportStatus("0");
                empiricalMaterialExportRecordService.save(exportRecord);
                return;
            }
            empiricalMaterialExportRecordService.save(exportRecord);
            TransmittableThreadLocalManager.emprocessMap.put(tokenValue, MapUtil.builder(new HashMap<String, Object>()).put(exportRecordId, 0.05).build());
            LambdaQueryWrapper<EmpiricalMaterialDirectory> lambdaQueryWrapper = Wrappers.lambdaQuery(EmpiricalMaterialDirectory.class)
                    .and(item -> item.isNull(EmpiricalMaterialDirectory::getLevelCode)
                            .or()
                            .le(EmpiricalMaterialDirectory::getLevelCode, empiricalMaterialExportDTO.getLevelCode()))
                    .orderByAsc(EmpiricalMaterialDirectory::getDirectoryCode);
            List<EmpiricalMaterialDirectory> directories = directoryService.list(lambdaQueryWrapper);
            //只要对应级别的目录
            directories = directories.stream().filter(item -> item.getDirectoryCode().length() <= 6 || (StrUtil.isNotBlank(item.getLevelCode()) && item.getLevelCode().equals(empiricalMaterialExportDTO.getLevelCode()))).collect(Collectors.toList());

            List<EmpiricalMaterialDirectory> directoriesBase = Optional.ofNullable(directories).orElse(new ArrayList<>()).stream()
                    .filter(item -> !"1".equals(item.getEvaluationCategory())).collect(Collectors.toList());
            List<EmpiricalMaterialDirectory> directoriesChoose = Optional.ofNullable(directories).orElse(new ArrayList<>()).stream()
                    .filter(item -> !"0".equals(item.getEvaluationCategory())).collect(Collectors.toList());

            //生成两个文档的未填值版：基本和选择
            WordUtils wordUtils = new WordUtils();
            ZipSecureFile.setMinInflateRatio(0);
            String parent = SystemUtils.getFilePath();
            String basePath = parent + "/MaterialFlowPathFiles/" + empiricalMaterialExportDTO.getUniqueId() + "/";
            if (!FileUtil.exist(basePath)) {
                FileUtil.mkdir(basePath);
            }
            //生成模板
            //使用CompletableFuture执行下面的两个任务
            CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
                wordUtils.generateEmCatalogDocumentTemplate(directoriesBase, basePath + "emjbtemplate.docx", null);
            }, threadPoolMonitor);
            CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
                wordUtils.generateEmCatalogDocumentTemplate(directoriesChoose, basePath + "emxztemplate.docx", null);
            }, threadPoolMonitor);
            // 等待两个任务都执行完成
            CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(future1, future2);
            // 阻塞当前线程直到所有任务完成
            combinedFuture.join();
            TransmittableThreadLocalManager.emprocessMap.put(tokenValue, MapUtil.builder(new HashMap<String, Object>()).put(exportRecordId, 0.2).build());
            //填充emjbtemplate和emxztemplate
            Map<String, Object> jbmap = new HashMap<>();
            Map<String, Object> xzmap = new HashMap<>();
            String directoryCode;
            String directoryName;
            String eachDictPath;
            for (EmpiricalMaterialDirectory empiricalMaterialDirectory : directories) {
                directoryCode = empiricalMaterialDirectory.getDirectoryCode();
                directoryName = empiricalMaterialDirectory.getDirectoryName();
                eachDictPath = parent + "/MaterialFlowPathFiles/" + directoryName + directoryCode + empiricalMaterialExportDTO.getProjectId() + "/" + directoryName + directoryCode + ".docx";
                if (directoryCode.length() < 7) {
                    continue;
                }
                if (!FileUtil.exist(eachDictPath)) {
                    log.warn(eachDictPath + "不存在！");
                    continue;
                }
                if ("0".equals(empiricalMaterialDirectory.getEvaluationCategory())) {
                    jbmap.put(directoryCode + directoryName, Includes.ofLocal(eachDictPath).create());
                } else {
                    xzmap.put(directoryCode + directoryName, Includes.ofLocal(eachDictPath).create());
                }
            }
            //使用CompletableFuture执行下面的两个任务
            CompletableFuture<Void> future3 = CompletableFuture.runAsync(() -> {
                if (jbmap.size() == 0) {
                    return;
                }
                wordUtils.generateEmWordForEachChapterComplete(basePath + "emjbtemplate.docx", basePath + "emjb.docx", jbmap); //基本
            }, threadPoolMonitor);
            CompletableFuture<Void> future4 = CompletableFuture.runAsync(() -> {
                if (xzmap.size() == 0) {
                    return;
                }
                wordUtils.generateEmWordForEachChapterComplete(basePath + "emxztemplate.docx", basePath + "emxz.docx", xzmap);//选择
            }, threadPoolMonitor);
            // 等待两个任务都执行完成
            CompletableFuture<Void> combinedFuture2 = CompletableFuture.allOf(future3, future4);
            // 阻塞当前线程直到所有任务完成
            combinedFuture2.join();
            TransmittableThreadLocalManager.emprocessMap.put(tokenValue, MapUtil.builder(new HashMap<String, Object>()).put(exportRecordId, 0.8).build());

            //导出完整文档
            Map<String, Object> jbwzMap = new HashMap<>();
            Map<String, Object> xzwzMap = new HashMap<>();
            Map<String, Object> commonMap = new HashMap<>();

            SysConfig sysConfigByName = RedisUtil.getSysConfigByName("hospital.code");
            String hospitalCode = sysConfigByName == null ? "" : sysConfigByName.getConfigValue();
            sysConfigByName = RedisUtil.getSysConfigByName("hospital.name");
            String hospitalName = sysConfigByName == null ? "" : sysConfigByName.getConfigValue();
            commonMap.put("hospitalCode", hospitalCode);
            commonMap.put("hospitalName", hospitalName);
            commonMap.put("year", DateUtil.date().year() + "");
            commonMap.put("yearAndMouth", DateUtil.date().toString("yyyy年MM月"));
            commonMap.put("levelCode", LevelCodeEnum.fromCode(empiricalMaterialExportDTO.getLevelCode()).getDescription());

            jbwzMap.putAll(commonMap);
            jbwzMap.put("evaluationCategory", "基本");
            jbwzMap.put("eachChapter", Includes.ofLocal(basePath + "emjb.docx").create());

            xzwzMap.putAll(commonMap);
            xzwzMap.put("evaluationCategory", "选择");
            xzwzMap.put("eachChapter", Includes.ofLocal(basePath + "emxz.docx").create());
            String fileName = DateUtil.date().year() + "年" + hospitalName + "电子病历 " + exportRecord.getExportDocumentLevel() + "级实证材料";
            //使用CompletableFuture执行下面的两个任务
            CompletableFuture<Void> future5 = CompletableFuture.runAsync(() -> {
                wordUtils.generateEmWordForEachChapterComplete(PathNameUtils.getEmpiricalmaterialFileName("电子病历实证文档.docx"), basePath + fileName + "(基本项).docx", jbwzMap);
            }, threadPoolMonitor);
            CompletableFuture<Void> future6 = CompletableFuture.runAsync(() -> {
                wordUtils.generateEmWordForEachChapterComplete(PathNameUtils.getEmpiricalmaterialFileName("电子病历实证文档.docx"), basePath + fileName + "(选择项).docx", xzwzMap);
            }, threadPoolMonitor);
            // 等待两个任务都执行完成
            CompletableFuture<Void> combinedFuture3 = CompletableFuture.allOf(future5, future6);
            // 阻塞当前线程直到所有任务完成
            combinedFuture3.join();
            TransmittableThreadLocalManager.emprocessMap.put(tokenValue, MapUtil.builder(new HashMap<String, Object>()).put(exportRecordId, 1.0).build());
            exportRecord.setExportStatus("2");
            exportRecord.setDataEndTime(DateUtil.now());
        } catch (Exception e) {
            TransmittableThreadLocalManager.emprocessMap.put(tokenValue, MapUtil.builder(new HashMap<String, Object>()).put(exportRecordId, -1.0).build());
            log.error(e.getMessage(), e);
            exportRecord.setExportStatus("3");
            exportRecord.setDataEndTime(DateUtil.now());
            exportRecord.setFailReason(e.getMessage());
        } finally {
            empiricalMaterialExportRecordService.updateById(exportRecord);
        }
    }


    @Override
    public void exportAll(String exportRecordId) {
        //基本和选择分开导
        EmpiricalMaterialExportRecord exportRecord = empiricalMaterialExportRecordService.getById(exportRecordId);
        exportRecord.setExportStatus("1");
        empiricalMaterialExportRecordService.updateById(exportRecord);
        try {
            LambdaQueryWrapper<EmpiricalMaterialDirectory> lambdaQueryWrapper = Wrappers.lambdaQuery(EmpiricalMaterialDirectory.class)
                    .and(item -> item.isNull(EmpiricalMaterialDirectory::getLevelCode)
                            .or()
                            .le(EmpiricalMaterialDirectory::getLevelCode, exportRecord.getExportDocumentLevel()))
                    .orderByAsc(EmpiricalMaterialDirectory::getDirectoryCode);
            List<EmpiricalMaterialDirectory> directories = directoryService.list(lambdaQueryWrapper);
            directories = directories.stream().filter(item -> item.getDirectoryCode().length() <= 6 || (StrUtil.isNotBlank(item.getLevelCode()) && item.getLevelCode().equals(exportRecord.getExportDocumentLevel()))).collect(Collectors.toList());

            List<EmpiricalMaterialDirectory> directoriesBase = Optional.ofNullable(directories).orElse(new ArrayList<>()).stream()
                    .filter(item -> !"1".equals(item.getEvaluationCategory())).collect(Collectors.toList());
            List<EmpiricalMaterialDirectory> directoriesChoose = Optional.ofNullable(directories).orElse(new ArrayList<>()).stream()
                    .filter(item -> !"0".equals(item.getEvaluationCategory())).collect(Collectors.toList());

            //生成两个文档的未填值版：基本和选择
            WordUtils wordUtils = new WordUtils();
            String parent = SystemUtils.getFilePath();
            String basePath = parent + "/MaterialFlowPathFiles/" + exportRecord.getId() + "/";
            if (!FileUtil.exist(basePath)) {
                FileUtil.mkdir(basePath);
            }
            //生成模板
            //使用CompletableFuture执行下面的两个任务
            CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
                wordUtils.generateEmCatalogDocumentTemplate(directoriesBase, basePath + "emjbtemplate.docx", null);
            }, threadPoolMonitor);
            CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
                wordUtils.generateEmCatalogDocumentTemplate(directoriesChoose, basePath + "emxztemplate.docx", null);
            }, threadPoolMonitor);
            // 等待两个任务都执行完成
            CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(future1, future2);
            // 阻塞当前线程直到所有任务完成
            combinedFuture.join();
            //填充emjbtemplate和emxztemplate
            Map<String, Object> jbmap = new HashMap<>();
            Map<String, Object> xzmap = new HashMap<>();
            String directoryCode;
            String directoryName;
            String eachDictPath;
            for (EmpiricalMaterialDirectory empiricalMaterialDirectory : directoriesBase) {
                directoryCode = empiricalMaterialDirectory.getDirectoryCode();
                directoryName = empiricalMaterialDirectory.getDirectoryName();
                eachDictPath = parent + "/MaterialFlowPathFiles/" + directoryName + directoryCode + "/" + directoryName + directoryCode + ".docx";
                if (directoryCode.length() < 7) {
                    continue;
                }
                if (!FileUtil.exist(eachDictPath)) {
                    log.warn(eachDictPath + "不存在！");
                    continue;
                }
                if ("0".equals(empiricalMaterialDirectory.getEvaluationCategory())) {
                    jbmap.put(directoryCode + directoryName, Includes.ofLocal(eachDictPath).create());
                } else {
                    xzmap.put(directoryCode + directoryName, Includes.ofLocal(eachDictPath).create());
                }
            }
            //使用CompletableFuture执行下面的两个任务
            CompletableFuture<Void> future3 = CompletableFuture.runAsync(() -> {
                wordUtils.generateEmWordForEachChapterComplete(basePath + "emjbtemplate.docx", basePath + "emjb.docx", jbmap);
            }, threadPoolMonitor);
            CompletableFuture<Void> future4 = CompletableFuture.runAsync(() -> {
                wordUtils.generateEmWordForEachChapterComplete(basePath + "emxztemplate.docx", basePath + "emxz.docx", xzmap);
            }, threadPoolMonitor);
            // 等待两个任务都执行完成
            CompletableFuture<Void> combinedFuture2 = CompletableFuture.allOf(future3, future4);
            // 阻塞当前线程直到所有任务完成
            combinedFuture2.join();
            //导出完整文档
            Map<String, Object> jbwzMap = new HashMap<>();
            Map<String, Object> xzwzMap = new HashMap<>();
            Map<String, Object> commonMap = new HashMap<>();

            SysConfig sysConfigByName = RedisUtil.getSysConfigByName("hospital.code");
            String hospitalCode = sysConfigByName == null ? "" : sysConfigByName.getConfigValue();
            sysConfigByName = RedisUtil.getSysConfigByName("hospital.name");
            String hospitalName = sysConfigByName == null ? "" : sysConfigByName.getConfigValue();
            commonMap.put("hospitalCode", hospitalCode);
            commonMap.put("hospitalName", hospitalName);
            commonMap.put("year", DateUtil.date().year() + "");
            commonMap.put("yearAndMouth", DateUtil.date().toString("yyyy年MM月"));
            commonMap.put("levelCode", LevelCodeEnum.fromCode(exportRecord.getExportDocumentLevel()).getDescription());

            jbwzMap.putAll(commonMap);
            jbwzMap.put("evaluationCategory", "基本");
            jbwzMap.put("eachChapter", Includes.ofLocal(basePath + "emjb.docx").create());

            xzwzMap.putAll(commonMap);
            xzwzMap.put("evaluationCategory", "选择");
            xzwzMap.put("eachChapter", Includes.ofLocal(basePath + "emxz.docx").create());

            String fileName = DateUtil.date().year() + "年" + hospitalName + "电子病历 " + exportRecord.getExportDocumentLevel() + "实证材料";
            //使用CompletableFuture执行下面的两个任务
            CompletableFuture<Void> future5 = CompletableFuture.runAsync(() -> {
                wordUtils.generateEmWordForEachChapterComplete(PathNameUtils.getEmpiricalmaterialFileName("电子病历实证文档.docx"), basePath + fileName + "(基本项).docx", jbwzMap);
            }, threadPoolMonitor);
            CompletableFuture<Void> future6 = CompletableFuture.runAsync(() -> {
                wordUtils.generateEmWordForEachChapterComplete(PathNameUtils.getEmpiricalmaterialFileName("电子病历实证文档.docx"), basePath + fileName + "(选择项).docx", xzwzMap);
            }, threadPoolMonitor);
            // 等待两个任务都执行完成
            CompletableFuture<Void> combinedFuture3 = CompletableFuture.allOf(future5, future6);
            // 阻塞当前线程直到所有任务完成
            combinedFuture3.join();
            exportRecord.setExportStatus("2");
            exportRecord.setDataEndTime(DateUtil.now());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            exportRecord.setExportStatus("3");
            exportRecord.setDataEndTime(DateUtil.now());
            exportRecord.setFailReason(e.getMessage());
        } finally {
            empiricalMaterialExportRecordService.updateById(exportRecord);
        }
    }
}
