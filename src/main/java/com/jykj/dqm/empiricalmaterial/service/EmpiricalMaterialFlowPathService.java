package com.jykj.dqm.empiricalmaterial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowFirstTreeVO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPath;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPathDTO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPathQuery;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowSecondTree;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_EMPIRICAL_MATERIAL_FLOW_PATH(实证材料流程和资料)】的数据库操作Service
 * @createDate 2024-01-23 14:25:03
 */
public interface EmpiricalMaterialFlowPathService extends IService<EmpiricalMaterialFlowPath> {

    EmpiricalMaterialFlowFirstTreeVO query(EmpiricalMaterialFlowPathQuery materialFlowPathQuery);

    R add(EmpiricalMaterialFlowPath materialFlowPath, List<MultipartFile> files);

    List<String> uploadMultipleFiles(List<MultipartFile> files, String materialFlowPathId, String projectId, String directoryName, String directoryCode, String evaluationContentId);

    R update(EmpiricalMaterialFlowPath materialFlowPath, List<MultipartFile> files);

    R delete(EmpiricalMaterialFlowPath materialFlowPath);

    void getImages(HttpServletResponse response, String fileName, String materialFlowPathId, String projectId, String directoryName, String directoryCode, String evaluationContentId);

    R markComplete(String directoryName, String directoryCode, String projectId);

    R queryAll(EmpiricalMaterialFlowPathQuery materialFlowPathQuery);

    R save(EmpiricalMaterialFlowPathDTO empiricalMaterialFlowPathDTO);
}