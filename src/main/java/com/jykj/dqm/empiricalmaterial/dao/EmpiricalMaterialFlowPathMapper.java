package com.jykj.dqm.empiricalmaterial.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPath;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_EMPIRICAL_MATERIAL_FLOW_PATH(实证材料流程和资料)】的数据库操作Mapper
 * @createDate 2024-01-23 14:25:03
 * @Entity com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPath
 */
@Mapper
public interface EmpiricalMaterialFlowPathMapper extends BaseMapper<EmpiricalMaterialFlowPath> {

}