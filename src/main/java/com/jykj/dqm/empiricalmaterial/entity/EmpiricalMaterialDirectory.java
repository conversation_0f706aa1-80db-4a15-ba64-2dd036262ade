package com.jykj.dqm.empiricalmaterial.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.emr.entity.UserAndTimeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 实证材料目录
 *
 * @TableName DQM_EMR_EMPIRICAL_MATERIAL_DIRECTORY
 */
@TableName(value = "DQM_EMR_EMPIRICAL_MATERIAL_DIRECTORY")
@Data
public class EmpiricalMaterialDirectory extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    private String parentId;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serialNum;

    /**
     * 序号
     */
    @ApiModelProperty(value = "项目序号")
    private String projectNum;

    /**
     * 目录名称
     */
    @NotBlank(message = "目录名称不能为空")
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @NotBlank(message = "目录编码不能为空")
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 等级关联
     */
    @ApiModelProperty(value = "等级关联")
    private String levelCode;

    /**
     * 业务项目
     */
    @ApiModelProperty(value = "业务项目")
    private String businessProject;

    /**
     * 评价类别
     */
    @ApiModelProperty(value = "评价类别: 0-基本 1-选择")
    private String evaluationCategory;

    /**
     * 评价内容
     */
    @ApiModelProperty(hidden = true)
    private String evaluationContent;

    /**
     * 备注
     */
    @ApiModelProperty(value = "remarksDesc")
    private String remarksDesc;

    @ApiModelProperty(value = "评价内容")
    @TableField(exist = false)
    private List<EmpiricalMaterialEvaluationContent> evaluationContents;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态:0-未分配，1-已分配，2-已完成")
    @TableField(exist = false)
    private String taskStatus;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(exist = false)
    private String personInCharge;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}