package com.jykj.dqm.empiricalmaterial.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 实证材料任务分配SaveDTO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/7/4 16:20:48
 */
@ApiModel(description = "实证材料任务分配SaveDTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmpiricalMaterialTaskAllocationSaveDTO {
    /**
     * 用户账号
     */
    @ApiModelProperty(value = "用户账号")
    @NotBlank
    private String userAccount;

    /**
     * 等级关联
     */
    @ApiModelProperty(value = "等级关联")
    @NotBlank
    private String levelCode;

    @NotBlank
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 实证材料任务分配List
     */
    @ApiModelProperty(value = "实证材料任务分配List")
    List<EmpiricalMaterialTaskAllocation> taskAllocations;
}