package com.jykj.dqm.empiricalmaterial.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jykj.dqm.emr.entity.UserAndTimeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * 实证材料评价
 *
 * @TableName DQM_EMR_EMPIRICAL_MATERIAL_EVALUATION_CONTENT
 */
@TableName(value = "DQM_EMR_EMPIRICAL_MATERIAL_EVALUATION_CONTENT")
@Data
public class EmpiricalMaterialEvaluationContent extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serialNum;

    /**
     * 目录名称
     */
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 评价类别
     */
    @ApiModelProperty(value = "评价类别")
    private String evaluationCategory;

    /**
     * 评价内容
     */
    @ApiModelProperty(value = "评价内容")
    private String evaluationContent;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarksDesc;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        EmpiricalMaterialEvaluationContent that = (EmpiricalMaterialEvaluationContent) o;
        return Objects.equals(directoryName, that.directoryName) &&
                Objects.equals(directoryCode, that.directoryCode) &&
                Objects.equals(evaluationContent, that.evaluationContent);
    }

    @Override
    public int hashCode() {
        return Objects.hash(directoryName, directoryCode, evaluationContent);
    }
}