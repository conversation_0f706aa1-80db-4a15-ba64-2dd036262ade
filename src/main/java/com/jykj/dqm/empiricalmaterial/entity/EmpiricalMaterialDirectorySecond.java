package com.jykj.dqm.empiricalmaterial.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 文档目录配置一级标题
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 15:48:52
 */
@ApiModel(description = "文档目录配置一级标题")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmpiricalMaterialDirectorySecond {
    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serialNum;

    /**
     * 序号
     */
    @ApiModelProperty(value = "项目序号")
    private String projectNum;

    /**
     * 业务项目
     */
    @ApiModelProperty(value = "业务项目")
    private String businessProject;

    @ApiModelProperty(value = "三级标题list")
    private List<EmpiricalMaterialDirectory> thirdLevels;
}