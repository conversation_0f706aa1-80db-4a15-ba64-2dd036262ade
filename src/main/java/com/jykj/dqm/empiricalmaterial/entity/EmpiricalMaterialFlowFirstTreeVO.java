package com.jykj.dqm.empiricalmaterial.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EmpiricalMaterialFlowFirstTreeVO implements Serializable {
    /**
     * 流程
     */
    @ApiModelProperty(value = "流程")
    private EmpiricalMaterialFlowPath allFlowPath;

    @ApiModelProperty(value = "children")
    private List<EmpiricalMaterialFlowSecondTree> groupFlowPath;
}
