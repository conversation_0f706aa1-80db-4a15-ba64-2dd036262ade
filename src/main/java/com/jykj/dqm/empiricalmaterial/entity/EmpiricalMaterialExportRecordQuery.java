package com.jykj.dqm.empiricalmaterial.entity;

import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 实证材料导出记录
 *
 * @TableName DQM_EMR_EMPIRICAL_MATERIAL_EXPORT_RECORD
 */
@Accessors(chain = true)
@Data
public class EmpiricalMaterialExportRecordQuery extends MyPageInfo implements Serializable {
    /**
     * 导出文档等级
     */
    @ApiModelProperty(value = "导出文档等级")
    private String exportDocumentLevel;

    @NotBlank(message = "项目ID不能为空")
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 导出状态：0、未执行；1、执行中；2、完成;3、异常
     */
    @ApiModelProperty(value = "导出状态：0、未执行；1、执行中；2、成功;3、异常")
    private String exportStatus;

}
