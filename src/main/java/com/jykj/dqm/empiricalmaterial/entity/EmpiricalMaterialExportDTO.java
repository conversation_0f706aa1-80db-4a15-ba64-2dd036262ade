package com.jykj.dqm.empiricalmaterial.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 实证材料导出
 */
@Data
public class EmpiricalMaterialExportDTO implements Serializable {
    /**
     * 唯一标识
     */
    @ApiModelProperty(value = "唯一标识，不传")
    private String uniqueId;

    @NotBlank(message = "项目ID不能为空")
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 等级关联
     */
    @NotBlank
    @ApiModelProperty(value = "等级关联")
    private String levelCode;

    /**
     * 定时导出时间
     */
    @ApiModelProperty(value = "定时导出时间")
    private String timedExportTime;

    @ApiModelProperty(value = "导出记录ID")
    private String id;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}