package com.jykj.dqm.quartz.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 定时任务的信息表
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/24 9:41:32
 */
@Mapper
public interface ScheduleJobTaskDao extends BaseMapper<ScheduleJobInfo> {
    List<ScheduleJobInfo> queryScheduleJobTask(ScheduleJobInfo params);

    List<ScheduleJobInfo> queryScheduleJobTaskByJobGroupTaskType(Map<String, Object> params);

    int addScheduleJobTask(ScheduleJobInfo params);

    int updateScheduleJobTask(ScheduleJobInfo params);

    int addScheduleJobTaskLog(Map<String, Object> params);

    int updateScheduleJobTaskLog(Map<String, Object> params);

    int deleteScheduleJobTaskLog(ScheduleJobInfo params);
}
