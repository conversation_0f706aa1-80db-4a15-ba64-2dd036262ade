package com.jykj.dqm.quartz.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

@Slf4j
@DisallowConcurrentExecution
public class MgDisallowConcurrentJob extends CommonJob {

    /*
       @DisallowConcurrentExecution
       任务有并行和串行之分，并行是指：一个定时任务，当执行时间到了的时候，立刻执行此任务，不管当前这个任务是否在执行中；串行是指：一个定时任务，当执行时间到了的时候，需要等待当前任务执行完毕，再去执行下一个任务。
     */
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.debug("采集任务的串行处理");
        super.execute(context);
    }

}

