package com.jykj.dqm.quartz.scheduler;

import cn.hutool.core.date.DateUtil;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.TriggerKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class CommonJob implements Job {

    private final static Logger logger = LoggerFactory.getLogger(CommonJob.class);
    public static final String TASK_ITEM_DATA_KEY = "TASK_ITEM_DATA_KEY";

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            JobDataMap dataMap = context.getJobDetail().getJobDataMap();
            ScheduleJobInfo jobInfo = (ScheduleJobInfo) dataMap.get(TASK_ITEM_DATA_KEY);
            TaskExeUtils.processTask(jobInfo);
            Date nextTime = context.getNextFireTime();
            Date endTime = DateUtil.parse(jobInfo.getJobEndDate() + " 23:59:59");
            if (nextTime.after(endTime)) {
                logger.info("下次执行时间:" + DateUtil.formatDateTime(nextTime));
                logger.info("任务结束时间:" + DateUtil.formatDateTime(endTime));
                logger.info("任务到期移除:" + jobInfo.getJobId() + " " + jobInfo.getJobGroup() + " " + jobInfo.getJobDesc());
                TriggerKey triggerKey = context.getTrigger().getKey();
                context.getScheduler().pauseTrigger(triggerKey);
                context.getScheduler().unscheduleJob(triggerKey);
                context.getScheduler().deleteJob(JobKey.jobKey(jobInfo.getJobId(), jobInfo.getJobGroup()));
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

}

