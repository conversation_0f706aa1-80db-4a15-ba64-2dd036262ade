package com.jykj.dqm.quartz.scheduler;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.jykj.dqm.quartz.entity.CronExpressionItem;
import com.jykj.dqm.quartz.entity.JobExeRateEnum;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;


/***
 * CronExpression 各段说明
 * 秒 0-59 , - * /
 * 分 0-59 , - * /
 * 小时 0-23 , - * /
 * 日期 1-31 , - * ? / L W C
 * 月份 1-12 或者 JAN-DEC , - * /
 * 星期 1-7 或者 SUN-SAT , - * ? / L C #
 * 年（可选） 留空, 1970-2099 , - * /
 *
 * 特殊字符 意义
 * * 表示所有值；
 * ? 表示未说明的值，即不关心它为何值；
 * - 表示一个指定的范围；
 * , 表示附加一个可能值；
 * / 符号前表示开始时间，符号后表示每次递增的值；
 *
 * L("last") 用在day-of-month字段意思是 "这个月最后一天"；用在 day-of-week字段,它简单意思是 "7" or "SAT"。
 *  如果在day-of-week字段里和数字联合使用，它的意思就是 "这个月的最后一个星期几" – 例如："6L" means "这个月的最后一个星期五".
 *  当我们用“L”时，不指明一个列表值或者范围是很重要的，不然的话，我们会得到一些意想不到的结果。
 *
 * W("weekday") 只能用在day-of-month字段。用来描叙最接近指定天的工作日（周一到周五）。
 *   例如：在day-of-month字段用“15W”指“最接近这个月第15天的工作日”，即如果这个月第15天是周六，
 *   那么触发器将会在这个月第14天即周五触发；如果这个月第15天是周日，那么触发器将会在这个月第
 *   16天即周一触发；如果这个月第15天是周二，那么就在触发器这天触发。注意一点：这个用法只会在当前月计算值，不会越过当前月。
 *   “W”字符仅能在day-of-month指明一天，不能是一个范围或列表。也可以用“LW”来指定这个月的最后一个工作日。
 *
 * # 只能用在day-of-week字段。用来指定这个月的第几个周几。
 *   例：在day-of-week字段用"6#3"指这个月第3个周五（6指周五，3指第3个）。如果指定的日期不存在，触发器就不会触发。
 *
 * C 指和calendar联系后计算过的值。
 *   例：在day-of-month 字段用“5C”指在这个月第5天或之后包括calendar的第一天；在day-of-week字段用“1C”指在这周日或之后包括calendar的第一天。
 *
 *   TEST URL:
 *   https://localhost:9090/scheduler/addJob?cronSecond=1&cronMinute=1&cronHour=1&cronDayOfWeek=1&cronMonth=3/3
 * <AUTHOR>
 *
 */
public class CronExpressionUtil {
    private static Map<String, String> monthOfWeekInfo = new HashMap<>();

    private static Map<String, String> weekInfo = new HashMap<>();

    public static Map<String, CronExpressionItem> cronExpressionMap = Maps.newConcurrentMap();

    public static Map<String, ScheduleJobInfo> scheduleJobInfoMap = Maps.newConcurrentMap();

    /**
     * 根据界面传的ScheduleJobInfo中的对象生成Cron表达式，并填充
     *
     * @param job ScheduleJobInfo
     * @return 包含Cron表达式的ScheduleJobInfo
     */
    public static ScheduleJobInfo pageValue2CronValue(ScheduleJobInfo job) {
        ScheduleJobInfo scheduleJobInfo = scheduleJobInfoMap.get(job.toString());
        if (scheduleJobInfo != null) {
            return scheduleJobInfo;
        }
        String jobExeRate = job.getJobExeRate();
        //时间间隔
        String jobSpan = job.getJobSpan();
        if (StringUtils.isNotEmpty(jobSpan)) {
            jobSpan = "/" + jobSpan;
        } else {
            jobSpan = "";
        }
        if (StrUtil.isNotBlank(job.getJobStartTime())) {
            //起始时间
            String[] hms = job.getJobStartTime().split(":");
            //秒固定
            job.setCronSecond(hms[2]);
            if (JobExeRateEnum.MINUTE.getCode().equals(jobExeRate)) {
                //设置开始点的分钟，间隔
                job.setCronMinute(hms[1] + jobSpan);
            } else if (JobExeRateEnum.HOUR.getCode().equals(jobExeRate)) {
                job.setCronMinute(hms[1]);
                //设置开始点的小时，间隔
                job.setCronHour(hms[0] + jobSpan);
            } else if (JobExeRateEnum.DAY.getCode().equals(jobExeRate)) {
                job.setCronMinute(hms[1]);
                job.setCronHour(hms[0]);
            } else if (JobExeRateEnum.WEEK.getCode().equals(jobExeRate)) {
                job.setCronMinute(hms[1]);
                job.setCronHour(hms[0]);
                //设置开始点的星期，间隔
                job.setCronDayOfWeek(job.getDayOfWeek());
                job.setCronHour(hms[0] + jobSpan);
            } else if (JobExeRateEnum.MONTH.getCode().equals(jobExeRate)
                    || JobExeRateEnum.YEAR.getCode().equals(jobExeRate)
                    || JobExeRateEnum.QUARTER.getCode().equals(jobExeRate)) {
                //月（月、年、季）
                job.setCronMinute(hms[1]);
                job.setCronHour(hms[0]);
                //月的执行方式
                //chkType: 1
                //dayOfMonth: 26
                String chkType = job.getChkType();
                if ("1".equals(chkType)) {
                    //指定日期
                    job.setCronDayOfMonth(job.getDayOfMonth());
                    job.setCronDayOfWeek("?");
                    job.setCronHour(hms[0] + jobSpan);
                } else if ("2".equals(chkType)) {
                    //月末
                    job.setCronDayOfMonth("L");
                    job.setCronDayOfWeek("?");
                } else if ("3".equals(chkType)) {
                    //星期
                    job.setCronDayOfMonth("?");
                    job.setCronDayOfWeek(job.getDayOfWeek2() + "#" + job.getWeekTime());
                }
                // cron表达式月份
                if (StringUtils.isEmpty(job.getChkMonth())) {
                    job.setCronMonth("*");
                } else {
                    job.setCronMonth(job.getChkMonth());
                }
            }
        }
        scheduleJobInfoMap.put(job.toString(), job);
        //生成cron表达式
        CronExpressionItem item = genCronByJobInfo(job);
        job.setCronExpression(item.getCronExpression());
        job.setJobDesc(item.getCronDesc());
        return job;
    }

    public static CronExpressionItem genCronByJobInfo(ScheduleJobInfo job) {
        CronExpressionItem cronExpressionItem = cronExpressionMap.get(job.toString());
        if (cronExpressionItem != null) {
            return cronExpressionItem;
        }
        CronExpressionItem item = new CronExpressionItem();
        int typeCode;
        typeCode = ("*".equals(job.getCronMinute()) || job.getCronMinute() == null) ? 0 : 1;
        typeCode = ("*".equals(job.getCronHour()) || job.getCronHour() == null) ? typeCode : typeCode + 2;
        typeCode = ("*".equals(job.getCronDayOfWeek()) || job.getCronDayOfWeek() == null || "?".equals(job.getCronDayOfWeek())) ? typeCode : typeCode + 4;
        typeCode = ("*".equals(job.getCronDayOfMonth()) || job.getCronDayOfMonth() == null || "?".equals(job.getCronDayOfMonth())) ? typeCode : typeCode + 8;
        typeCode = ("*".equals(job.getCronMonth()) || job.getCronMonth() == null) ? typeCode : typeCode + 16;

        String cronExp = "0 0 0 * * ? *";
        if (typeCode >= 16) {
            if ("?".equals(job.getCronDayOfMonth())) {
                cronExp = genMonthWeekCron(job.getCronSecond(), job.getCronMinute(), job.getCronHour(), job.getCronDayOfWeek(), job.getCronMonth());
            } else {
                cronExp = genMonthDayCron(job.getCronSecond(), job.getCronMinute(), job.getCronHour(), job.getCronDayOfMonth(), job.getCronMonth());
            }
        } else if (typeCode >= 8) {
            cronExp = genDayOfMonthCron(job.getCronSecond(), job.getCronMinute(), job.getCronHour(), job.getCronDayOfMonth());
        } else if (typeCode >= 4) {
            cronExp = genDayOfWeekCron(job.getCronSecond(), job.getCronMinute(), job.getCronHour(), job.getCronDayOfWeek());
        } else if (typeCode >= 2) {
            if ("2".equals(job.getJobExeRate()) && StrUtil.isBlank(job.getJobSpan())) {
                cronExp = genMinuteCron(job.getCronSecond(), job.getCronMinute());
            } else {
                cronExp = genHourCron(job.getCronSecond(), job.getCronMinute(), job.getCronHour());
            }
        } else {
            cronExp = genSecondCron(job.getCronSecond());
        }
        item.setCronExpression(cronExp);
        item.setCronDesc(getDesc(cronExp));
        cronExpressionMap.put(job.toString(), item);
        return item;
    }

    public static String genSecondCron(String second) {
        checkSecondStr(second, false);
        return String.format("%s * * * * ? *", second);
    }

    public static String genMinuteCron(String second, String minute) {
        checkSecondStr(second, true);
        checkMinuteStr(minute, false);
        return String.format("%s %s * * * ? *", second, minute);
    }

    public static String genHourCron(String second, String minute, String hour) {
        checkSecondStr(second, true);
        checkMinuteStr(minute, true);
        checkHourStr(hour, false);
        return String.format("%s %s %s * * ? *", second, minute, hour);
    }

    public static String genDayOfMonthCron(String second, String minute, String hour, String day) {
        checkSecondStr(second, true);
        checkMinuteStr(minute, true);
        checkHourStr(hour, true);
        checkDayOfMonthStr(day, false);
        return String.format("%s %s %s %s * ? *", second, minute, hour, day);
    }

    public static String genMonthDayCron(String second, String minute, String hour, String dayOfMonth, String month) {
        checkSecondStr(second, true);
        checkMinuteStr(minute, true);
        checkHourStr(hour, false);
        checkDayOfMonthStr(dayOfMonth, true);
        checkMonthStr(month, false);
        return String.format("%s %s %s %s %s ? *", second, minute, hour, dayOfMonth, month);
    }

    public static String genDayOfWeekCron(String second, String minute, String hour, String dayOfWeek) {
        checkSecondStr(second, true);
        checkMinuteStr(minute, true);
        checkHourStr(hour, false);
        checkDayOfWeekStr(dayOfWeek, false);
        return String.format("%s %s %s ? * %s *", second, minute, hour, dayOfWeek);
    }

    public static String genMonthWeekCron(String second, String minute, String hour, String dayOfWeek, String month) {
        checkSecondStr(second, true);
        checkMinuteStr(minute, true);
        checkHourStr(hour, true);
        checkDayOfWeekStr(dayOfWeek, true);
        checkMonthStr(month, false);
        return String.format("%s %s %s ? %s %s *", second, minute, hour, month, dayOfWeek);
    }

    public static boolean checkStr(String str, int min, int max, String title, String range) {
        if ("l".equalsIgnoreCase(str)) {
            return true;
        }
        int value = Integer.parseInt(str);
        if (value < min || value > max) {
            throw new RuntimeException(String.format("Cron表达式的%s需要%s之间", title, range));
        }
        return true;
    }

    public static boolean checkStrArr(String str, int min, int max, String title, String range) {
        String[] strArr = new String[1];
        if (str.indexOf("/") > -1) {
            strArr = str.split("/");
        } else if (str.indexOf(",") > 0) {
            strArr = str.split(",");
        } else {
            strArr[0] = str;
        }
        for (String string : strArr) {
            if ("l".equalsIgnoreCase(str)) {
                return true;
            }
            if (!StringUtil.isInteger(string)) {
                throw new RuntimeException(String.format("Cron表达式的%s需要%s之间,[/,]只能二选一", title, range));
            }
            int value = Integer.parseInt(string);
            if (value < 0 || value > 59) {
                throw new RuntimeException(String.format("Cron表达式的%s需要%s之间,[/,]只能二选一", title, range));
            }
        }
        return true;
    }

    public static boolean checkSecondStr(String second, boolean checkFlag) {
        String title = "秒";
        String range = "0-59";
        if (checkFlag) {
            return checkStr(second, 0, 59, title, range);
        } else {
            return checkStrArr(second, 0, 59, title, range);
        }
    }

    public static boolean checkMinuteStr(String second, boolean checkFlag) {
        String title = "分";
        String range = "0-59";
        if (checkFlag) {
            return checkStr(second, 0, 59, title, range);
        } else {
            return checkStrArr(second, 0, 59, title, range);
        }
    }

    public static boolean checkHourStr(String second, boolean checkFlag) {
        String title = "小时";
        String range = "0-23";
        if (checkFlag) {
            return checkStr(second, 0, 23, title, range);
        } else {
            return checkStrArr(second, 0, 23, title, range);
        }
    }

    public static boolean checkDayOfMonthStr(String second, boolean checkFlag) {
        String title = "日期";
        String range = "1-31";
        if (checkFlag) {
            return checkStr(second, 1, 31, title, range);
        } else {
            return checkStrArr(second, 1, 31, title, range);
        }
    }

    public static boolean checkMonthStr(String second, boolean checkFlag) {
        String title = "月份";
        String range = "1-12";
        if (checkFlag) {
            return checkStr(second, 1, 12, title, range);
        } else {
            return checkStrArr(second, 1, 12, title, range);
        }
    }


    public static boolean checkDayOfWeekStr(String second, boolean checkFlag) {
        String title = "周";
        String range = "1-7";
        if (checkFlag) {
            if (second.indexOf("#") > -1) {
                String[] arr = second.split("#");
                return checkStr(arr[0], 1, 7, title, range) && checkStr(arr[1], 1, 4, title, range);
            } else {
                return checkStr(second, 1, 7, title, range);
            }
        } else {
            return checkStrArr(second, 1, 7, title, range);
        }
    }

    private static Map<String, String> monthOfWeekInfo() {
        if (monthOfWeekInfo.size() == 0) {
            monthOfWeekInfo.put("1", "第一周");
            monthOfWeekInfo.put("2", "第二周");
            monthOfWeekInfo.put("3", "第三周");
            monthOfWeekInfo.put("4", "第四周");
        }
        return monthOfWeekInfo;
    }

    private static Map<String, String> getWeekInfo() {
        if (weekInfo.size() == 0) {
            weekInfo.put("1", "星期日");
            weekInfo.put("2", "星期一");
            weekInfo.put("3", "星期二");
            weekInfo.put("4", "星期三");
            weekInfo.put("5", "星期四");
            weekInfo.put("6", "星期五");
            weekInfo.put("7", "星期六");
        }
        return weekInfo;
    }


    public static String getDesc(String cronExpression) {
        String[] items = cronExpression.split(" ");
        StringBuffer sb = new StringBuffer("");
        String spanStr = "";
        String rateDesc = "";
        for (int i = 0; i < items.length; i++) {
            if ("*".equals(items[i]) && rateDesc.length() == 0) {
                rateDesc = descFmt[i];
            }
            if ("*".equals(items[i]) || "?".equals(items[i])) {
                continue;
            }
            String[] fmtValue = items[i].split("/");
            if (fmtValue.length == 2) {
                spanStr = " 间隔" + String.format(descFmt[i], fmtValue[1]);
            }
            if (i == 5) {
                if (items[i].contains("#")) {
                    fmtValue = items[i].split("#");
                    sb.insert(0, getWeekInfo().get(fmtValue[0]));
                    sb.insert(0, monthOfWeekInfo().get(fmtValue[1]));
                } else {
                    for (String v : fmtValue[0].split(",")) {
                        sb.insert(0, getWeekInfo().get(v));
                    }
                }

            } else {
                sb.insert(0, String.format(descFmt[i], fmtValue[0]));
            }
        }
        sb.insert(0, "从");
        sb.append("开始");
        sb.append(spanStr);
        if (items.length > 5 && !items[5].equals("?")) {
            if (!items[5].contains("#")) {
                sb.append("每周");
            }
        } else {
            sb.append(String.format(rateDesc, " 每"));
        }
        sb.append(" 定期执行");
        String temp = sb.toString().replace("L日", "月末");
        return temp;
    }

    private static String[] descFmt = {"%s秒", "%s分", "%s时", "%s日", "%s月", "星期%s ", "%s年"};
}
