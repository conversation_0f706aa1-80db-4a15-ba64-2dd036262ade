package com.jykj.dqm.quartz.entity;

/**
 * 执行频次.分/小时/天/周/月
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/30 13:52:18
 */
public enum JobExeRateEnum {
    MINUTE("1", "分钟"),
    HOUR("2", "小时"),
    DAY("3", "天"),
    WEEK("4", "周"),
    MONTH("5", "月"),
    YEAR("5", "年"),
    QUARTER("5", "季度"),
    ;

    /**
     * 执行频次码
     */
    private String code;
    /**
     * 执行频次内容
     */
    private String name;

    JobExeRateEnum(String code, String value) {
        this.code = code;
        this.name = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
