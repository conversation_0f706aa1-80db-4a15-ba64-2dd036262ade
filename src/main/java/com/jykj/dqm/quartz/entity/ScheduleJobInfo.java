package com.jykj.dqm.quartz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ApiModel(value = "任务配置")
@TableName("DQM_SCHEDULE_JOB_TASK")
public class ScheduleJobInfo implements Serializable {
    private static final long serialVersionUID = 5908631032824094476L;
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    @TableField(value = "JOB_ID")
    private String jobId;
    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    @TableField(value = "JOB_NAME")
    @NotBlank(message = "任务名称不能为空")
    private String jobName;
    /**
     * 任务分组
     */
    @ApiModelProperty(value = "任务分组")
    @TableField(value = "JOB_GROUP")
    private String jobGroup;
    /**
     * 任务状态 0禁用 1启用 2删除
     */
    @ApiModelProperty(value = "任务状态 0禁用 1启用 2删除")
    @TableField(value = "JOB_STATUS")
    private String jobStatus;
    /**
     * 任务运行时间表达式
     */
    @ApiModelProperty(value = "任务运行时间表达式")
    @TableField(value = "CRON_EXPRESSION")
    private String cronExpression;
    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述")
    @TableField(value = "JOB_DESC")
    private String jobDesc;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")   //后端-->前端。
    @TableField(value = "JOB_START_DATE")
    private String jobStartDate;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")   //后端-->前端。
    @TableField(value = "JOB_END_DATE")
    private String jobEndDate;
    /**
     * 业务数据ID
     */
    @ApiModelProperty(value = "业务数据ID")
    @TableField(value = "BIZ_DATA_ID")
    private String bizDataId;
    /**
     * 任务类别
     */
    @ApiModelProperty(value = "任务类别")
    @TableField(value = "TASK_TYPE")
    private String taskType;
    /**
     * cron表达式秒
     */
    @ApiModelProperty(value = "cron表达式秒")
    @TableField(value = "CRON_SECOND")
    private String cronSecond = "0";
    /**
     * cron表达式分钟
     */
    @ApiModelProperty(value = "cron表达式分钟")
    @TableField(value = "CRON_MINUTE")
    private String cronMinute = "*";
    /**
     * cron表达式小时
     */
    @ApiModelProperty(value = "cron表达式小时")
    @TableField(value = "CRON_HOUR")
    private String cronHour = "*";
    /**
     * cron表达式日期
     */
    @ApiModelProperty(value = "cron表达式日期")
    @TableField(value = "CRON_DAY_OF_MONTH")
    private String cronDayOfMonth = "*";
    /**
     * cron表达式星期
     */
    @ApiModelProperty(value = "cron表达式星期")
    @TableField(value = "CRON_DAY_OF_WEEK")
    private String cronDayOfWeek = "?";
    /**
     * cron表达式月份
     */
    @ApiModelProperty(value = "cron表达式月份")
    @TableField(value = "CRON_MONTH")
    private String cronMonth = "*";
    /**
     * 执行类别
     */
    @ApiModelProperty(value = "执行类别：0：手动执行，1：定时调度")
    @TableField(value = "JOB_EXE_TYPE")
    @NotBlank(message = "执行类别不能为空")
    private String jobExeType;
    /**
     * 执行频次 1：每分 2：每小时 3：每天 4：每周 5：每月
     */
    @ApiModelProperty(value = "执行频次 1：每分 2：每小时 3：每天 4：每周 5：每月")
    @TableField(value = "JOB_EXE_RATE")
    private String jobExeRate;
    /**
     * 起始时间
     */
    @ApiModelProperty(value = "起始时间")
    @TableField(value = "JOB_START_TIME")
    private String jobStartTime;
    /**
     * 时间间隔
     */
    @ApiModelProperty(value = "时间间隔")
    @TableField(value = "JOB_SPAN")
    private String jobSpan;
    /**
     * 周执行星期
     */
    @ApiModelProperty(value = "周执行星期")
    @TableField(value = "DAY_OF_WEEK")
    private String dayOfWeek;
    /**
     * 月的执行日期
     */
    @ApiModelProperty(value = "月的执行日期")
    @TableField(value = "DAY_OF_MONTH")
    private String dayOfMonth;
    /**
     * 月的执行周次
     */
    @ApiModelProperty(value = "月的执行周次")
    @TableField(value = "WEEK_TIME")
    private String weekTime;
    /**
     * 月的执行星期
     */
    @ApiModelProperty(value = "月的执行星期")
    @TableField(value = "DAY_OF_WEEK2")
    private String dayOfWeek2;
    /**
     * 月的执行方式
     */
    @ApiModelProperty(value = "月的执行方式")
    @TableField(value = "CHK_TYPE")
    private String chkType;
    /**
     * 执行的月份
     */
    @ApiModelProperty(value = "执行的月份")
    @TableField(value = "CHK_MONTH")
    private String chkMonth;
    /**
     * 参数
     */
    @ApiModelProperty(value = "参数")
    @TableField(value = "JOB_PARAMS")
    private String jobParams;
    /**
     * 手动执行报告日期
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "手动执行报告日期")
    private String reportDate;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_DATE")
    private String createDate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_DATE")
    private String updateDate;

    @TableField(exist = false)
    @ApiModelProperty(value = "pushTime")
    private Date pushTime;


    public String getCronDayOfMonth() {
        return cronDayOfMonth;
    }

    public void setCronDayOfMonth(String cronDayOfMonth) {
        if (!"?".equals(cronDayOfMonth)) {
            this.cronDayOfWeek = "?";
        }
        this.cronDayOfMonth = cronDayOfMonth;
    }

    public String getCronDayOfWeek() {
        return cronDayOfWeek;
    }

    public void setCronDayOfWeek(String cronDayOfWeek) {
        if (!"?".equals(cronDayOfWeek)) {
            this.cronDayOfMonth = "?";
        }
        this.cronDayOfWeek = cronDayOfWeek;
    }
}
