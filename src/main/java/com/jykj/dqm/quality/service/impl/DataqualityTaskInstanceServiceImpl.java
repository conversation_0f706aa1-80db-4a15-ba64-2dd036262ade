package com.jykj.dqm.quality.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.metadata.dao.MetadataDatasourceMapper;
import com.jykj.dqm.quality.dao.DataqualityTaskInstanceMapper;
import com.jykj.dqm.quality.entity.CheckRuleExecutionRecordQueryDTO;
import com.jykj.dqm.quality.entity.DataqualityTaskInstance;
import com.jykj.dqm.quality.entity.DataqualityTaskInstanceVO;
import com.jykj.dqm.quality.service.DataqualityTaskInstanceService;
import com.jykj.dqm.quality.service.DataqulityQstnManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据质量检核任务执行情况
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/27 17:48:23
 */
@Service
public class DataqualityTaskInstanceServiceImpl extends ServiceImpl<DataqualityTaskInstanceMapper, DataqualityTaskInstance> implements DataqualityTaskInstanceService {
    @Autowired
    private DataqualityTaskInstanceMapper dataqualityTaskInstanceMapper;

    @Autowired
    private MetadataDatasourceMapper dqmMetadataDatasourceMapper;

    @Autowired
    private DataqulityQstnManagerService dataqulityQstnManagerService;

    @Override
    public R queryRecord(CheckRuleExecutionRecordQueryDTO checkRuleExecutionRecordQueryDTO) {
        if (checkRuleExecutionRecordQueryDTO.getEndDt() != null) {
            Date endDt = checkRuleExecutionRecordQueryDTO.getEndDt();
            checkRuleExecutionRecordQueryDTO.setEndDt(DateUtil.offsetDay(endDt, 1));
        }
        PageHelper.startPage(checkRuleExecutionRecordQueryDTO.getPageNum(), checkRuleExecutionRecordQueryDTO.getPageSize());
        List<DataqualityTaskInstanceVO> mapList = dataqualityTaskInstanceMapper.queryRecord(checkRuleExecutionRecordQueryDTO);
        PageInfo<DataqualityTaskInstanceVO> pageInfo = new PageInfo(mapList);
        return RUtil.success(pageInfo);
    }

    @Override
    public R querySysAndDb() {
        List<Map<String, Object>> list = dqmMetadataDatasourceMapper.querySysAndDb();
        list.forEach(map -> map.put("sysAndDb", map.get("sysName") + "_" + map.get("dbName")));
        return RUtil.success(list);
    }

    @Override
    public R queryRecordDetail(String dataQltyQstnId) {
        return dataqulityQstnManagerService.queryDetailQstnData(Integer.parseInt(dataQltyQstnId));
    }
}
