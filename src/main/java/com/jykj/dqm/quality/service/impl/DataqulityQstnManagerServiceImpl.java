package com.jykj.dqm.quality.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.auth.dao.SysUserMapper;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.quality.dao.DataqulityQstnDetailMapper;
import com.jykj.dqm.quality.dao.DataqulityQstnManagerMapper;
import com.jykj.dqm.quality.dao.DataqulityQstnOperationRecordMapper;
import com.jykj.dqm.quality.entity.DataqulityQstnDetail;
import com.jykj.dqm.quality.entity.DataqulityQstnManager;
import com.jykj.dqm.quality.entity.DataqulityQstnManagerQueryDTO;
import com.jykj.dqm.quality.entity.DataqulityQstnManagerVO;
import com.jykj.dqm.quality.entity.DataqulityQstnOperationRecord;
import com.jykj.dqm.quality.entity.PushMsgReceiver;
import com.jykj.dqm.quality.entity.QstnRctfctnStusCdEnum;
import com.jykj.dqm.quality.service.DataqulityQstnManagerService;
import com.jykj.dqm.utils.MapperUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 质量问题管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/5 18:32:47
 */
@Slf4j
@Service
public class DataqulityQstnManagerServiceImpl extends ServiceImpl<DataqulityQstnManagerMapper, DataqulityQstnManager> implements DataqulityQstnManagerService {
    @Autowired
    DataqulityQstnManagerMapper dataqulityQstnManagerMapper;

    @Autowired
    DataqulityQstnDetailMapper dataqulityQstnDetailMapper;

    @Autowired
    DataqulityQstnOperationRecordMapper dataqulityQstnOperationRecordMapper;

    @Autowired
    DataqulityQstnManagerServiceImpl dataqulityQstnManagerService;

    @Autowired
    private DataSourceProperties dataSourceProperties;

    @Autowired
    private PushMsgManager pushMsgManager;

    @Autowired
    private SysUserMapper sysUserMapper;


    @Override
    public int insertSelective(DataqulityQstnManager record) {
        return baseMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(DataqulityQstnManager record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public R getAllSysAndDb() {
        List<Map<String, Object>> mapList = dataqulityQstnManagerMapper.getAllSysAndDb();
        return RUtil.success(mapList);
    }

    @Override
    public R editQstnInfo(DataqulityQstnManagerVO dataqulityQstnManager) {
        String operationType = dataqulityQstnManager.getOperationType();
        DataqulityQstnOperationRecord dataqulityQstnOperationRecord = new DataqulityQstnOperationRecord();
        String qstnId = "" + dataqulityQstnManager.getPkId();
        QueryWrapper<DataqulityQstnOperationRecord> queryWrapper;
        String userName = getUserName();
        dataqulityQstnOperationRecord.setOperationPerson(userName);
        //1关闭，2处理，3撤销关闭，4撤销处理
        if ("1".equals(operationType)) {
            //关闭
            dataqulityQstnManager.setQstnRctfctnStusCd(QstnRctfctnStusCdEnum.CLOSE.getCode());

            dataqulityQstnOperationRecord.setOperationTime(new Date());
            dataqulityQstnOperationRecord.setOperationContent("关闭问题");
            dataqulityQstnOperationRecord.setQstnRctfctnStusCd(QstnRctfctnStusCdEnum.CLOSE.getCode());
        } else if ("2".equals(operationType)) {
            //处理
            dataqulityQstnManager.setQstnRctfctnStusCd(QstnRctfctnStusCdEnum.PROCESSING.getCode());

            dataqulityQstnOperationRecord.setOperationTime(new Date());
            dataqulityQstnOperationRecord.setOperationContent("处理问题");
            dataqulityQstnOperationRecord.setQstnRctfctnStusCd(QstnRctfctnStusCdEnum.PROCESSING.getCode());
            //根据配置发送短信或者邮件(异步)
            String msg;
            if ("JSX".equalsIgnoreCase(dataqulityQstnManager.getCheckRuleType())) {
                String template = "{}系统{}数据库，{}检查,发现数据为按时间到达";
                msg = StrUtil.format(template,
                        dataqulityQstnManager.getDbNm(),
                        dataqulityQstnManager.getQstnPrdusSysCd(),
                        DateUtil.today()
                );
            } else {
                String template = "{}系统{}数据库，{}共检查{}条数据，发现{}条问题数据";
                msg = StrUtil.format(template,
                        dataqulityQstnManager.getDbNm(),
                        dataqulityQstnManager.getQstnPrdusSysCd(),
                        DateUtil.today(),
                        dataqulityQstnManager.getDataTotalNum(),
                        dataqulityQstnManager.getDataQstnNum()
                );
            }
            pushMsgManager.pushMsg(dataqulityQstnManager.getPushMessageReceivers(), msg, qstnId);
        } else if ("3".equals(operationType)) {
            //撤销关闭
            dataqulityQstnOperationRecord.setOperationContent("撤销关闭");
            dataqulityQstnOperationRecord.setOperationTime(new Date());
            queryWrapper = new QueryWrapper<DataqulityQstnOperationRecord>()
                    .eq("DATA_QLTY_QSTN_ID", dataqulityQstnManager.getPkId())
                    .orderByDesc("OPERATION_TIME")
                    .ne("QSTN_RCTFCTN_STUS_CD", "1");
            List<DataqulityQstnOperationRecord> dataqulityQstnOperationRecordHistorys = dataqulityQstnOperationRecordMapper.selectList(queryWrapper);
            DataqulityQstnOperationRecord dataqulityQstnOperationRecordHistory = dataqulityQstnOperationRecordHistorys.get(0);

            dataqulityQstnManager.setQstnRctfctnStusCd(dataqulityQstnOperationRecordHistory.getQstnRctfctnStusCd());
            dataqulityQstnOperationRecord.setQstnRctfctnStusCd(dataqulityQstnOperationRecordHistory.getQstnRctfctnStusCd());
        } else if ("4".equals(operationType)) {
            //撤销处理
            queryWrapper = new QueryWrapper<DataqulityQstnOperationRecord>()
                    .eq("DATA_QLTY_QSTN_ID", dataqulityQstnManager.getPkId())
                    .orderByDesc("OPERATION_TIME")
                    .ne("QSTN_RCTFCTN_STUS_CD", "2");
            List<DataqulityQstnOperationRecord> dataqulityQstnOperationRecordHistorys = dataqulityQstnOperationRecordMapper.selectList(queryWrapper);
            DataqulityQstnOperationRecord dataqulityQstnOperationRecordHistory = dataqulityQstnOperationRecordHistorys.get(0);

            dataqulityQstnManager.setQstnRctfctnStusCd(dataqulityQstnOperationRecordHistory.getQstnRctfctnStusCd());
            dataqulityQstnOperationRecord.setOperationTime(new Date());
            dataqulityQstnOperationRecord.setOperationContent("撤销处理");
            dataqulityQstnOperationRecord.setQstnRctfctnStusCd(dataqulityQstnOperationRecordHistory.getQstnRctfctnStusCd());
        } else {
            throw new BusinessException("操作类型不正确：1关闭，2处理，3撤销关闭，4撤销处理");
        }

        dataqulityQstnManagerService.updateAndRecode(dataqulityQstnManager, dataqulityQstnOperationRecord, qstnId);
        return RUtil.success();
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     * <AUTHOR>
     */
    private String getUserName() {
        String loginId = StpUtil.getLoginIdAsString();
        String userName = loginId;
        try {
            Map<String, String> map = sysUserMapper.getUserById(loginId);
            if (StrUtil.isNotBlank(map.get("USER_NAME"))) {
                userName = map.get("USER_NAME");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return userName;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAndRecode(DataqulityQstnManagerVO dataqulityQstnManager, DataqulityQstnOperationRecord dataqulityQstnOperationRecord, String qstnId) {
        DataqulityQstnManager dataqulityQstnManagerDb = MapperUtils.INSTANCE.map(DataqulityQstnManager.class, dataqulityQstnManager);
        dataqulityQstnManagerDb.setPushMessageReceiver(JSONObject.toJSONString(dataqulityQstnManagerDb.getPushMessageReceivers()));
        dataqulityQstnManagerMapper.updateByPrimaryKeySelective(dataqulityQstnManagerDb);
        dataqulityQstnOperationRecord.setDataQltyQstnId(qstnId);
        dataqulityQstnOperationRecordMapper.insert(dataqulityQstnOperationRecord);
    }

    @Override
    public R queryDetailQstnData(Integer dataQltyQstnId) {
        String url = dataSourceProperties.getUrl().toUpperCase();
        String limit;
        if (url.contains("MYSQL")) {
            limit = "limit 10";
        } else {
            limit = "and rownum <=10";
        }
        QueryWrapper<DataqulityQstnDetail> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DataqulityQstnDetail::getDataQltyQstnId, dataQltyQstnId);
        wrapper.last(limit);
        List<DataqulityQstnDetail> dataqulityQstnDetails = dataqulityQstnDetailMapper.selectList(wrapper);
        for (DataqulityQstnDetail dataqulityQstnDetail : dataqulityQstnDetails) {
            if (StrUtil.isNotBlank(dataqulityQstnDetail.getDetailLine())) {
                dataqulityQstnDetail.setDetailLineJson(JSONObject.parseObject(dataqulityQstnDetail.getDetailLine()));
                dataqulityQstnDetail.setDetailLine(null);
            }
        }
        return RUtil.success(dataqulityQstnDetails);
    }

    @Override
    public R queryList(DataqulityQstnManagerQueryDTO dataqulityQstnManagerQueryDTO) {
        if (dataqulityQstnManagerQueryDTO.getCheckDt() != null) {
            dataqulityQstnManagerQueryDTO.setCheckEndDt(DateUtil.offsetDay(dataqulityQstnManagerQueryDTO.getCheckDt(), 1));
        }
        if (dataqulityQstnManagerQueryDTO.getHandleDt() != null) {
            dataqulityQstnManagerQueryDTO.setHandleEndDt(DateUtil.offsetDay(dataqulityQstnManagerQueryDTO.getHandleDt(), 1));
        }
        PageHelper.startPage(dataqulityQstnManagerQueryDTO.getPageNum(), dataqulityQstnManagerQueryDTO.getPageSize());
        List<DataqulityQstnManagerVO> list = dataqulityQstnManagerMapper.queryList(dataqulityQstnManagerQueryDTO);
        PageInfo<DataqulityQstnManagerVO> pageInfo = new PageInfo<>(list);
        List<DataqulityQstnManagerVO> listTemp = pageInfo.getList();
        QueryWrapper<DataqulityQstnOperationRecord> wrapper;
        for (DataqulityQstnManagerVO dataqulityQstnManagerVO : listTemp) {
            wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(DataqulityQstnOperationRecord::getDataQltyQstnId, dataqulityQstnManagerVO.getPkId());
            dataqulityQstnManagerVO.setDataqulityQstnOperationRecords(dataqulityQstnOperationRecordMapper.selectList(wrapper));
            dataqulityQstnManagerVO.setPushMessageReceivers(JSONObject.parseArray(dataqulityQstnManagerVO.getPushMessageReceiver(), PushMsgReceiver.class));
        }
        pageInfo.setList(listTemp);
        return RUtil.success(pageInfo);
    }
}








