package com.jykj.dqm.quality.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.quality.entity.CheckRuleExecutionRecordQueryDTO;
import com.jykj.dqm.quality.entity.DataqualityTaskInstance;

/**
 * 数据质量检核任务执行情况
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/27 17:48:23
 */
public interface DataqualityTaskInstanceService extends IService<DataqualityTaskInstance> {
    R queryRecord(CheckRuleExecutionRecordQueryDTO checkRuleExecutionRecordQueryDTO);

    R querySysAndDb();

    R queryRecordDetail(String dataQltyQstnId);
}
