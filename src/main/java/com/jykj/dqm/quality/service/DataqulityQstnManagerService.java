package com.jykj.dqm.quality.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.quality.entity.DataqulityQstnManager;
import com.jykj.dqm.quality.entity.DataqulityQstnManagerQueryDTO;
import com.jykj.dqm.quality.entity.DataqulityQstnManagerVO;

/**
 * 质量问题管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/5 18:32:47
 */
public interface DataqulityQstnManagerService extends IService<DataqulityQstnManager> {
    int insertSelective(DataqulityQstnManager record);

    int updateByPrimaryKeySelective(DataqulityQstnManager record);

    R getAllSysAndDb();

    R editQstnInfo(DataqulityQstnManagerVO dataqulityQstnManager);

    R queryDetailQstnData(Integer dataQltyQstnId);

    R queryList(DataqulityQstnManagerQueryDTO dataqulityQstnManagerQueryDTO);
}








