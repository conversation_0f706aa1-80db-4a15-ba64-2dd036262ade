package com.jykj.dqm.quality.service.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.quality.dao.CheckRuleExecutionMapper;
import com.jykj.dqm.quality.dao.DataqualityCheckRuleMapper;
import com.jykj.dqm.quality.dao.DataqualityTaskInstanceMapper;
import com.jykj.dqm.quality.entity.CheckRuleExecutionDTO;
import com.jykj.dqm.quality.entity.CheckRuleExecutionQueryDTO;
import com.jykj.dqm.quality.entity.CheckRuleExecutionRecordQueryDTO;
import com.jykj.dqm.quality.entity.CheckRuleRunInfoVO;
import com.jykj.dqm.quality.entity.CheckRuleTableOrFieldQueryDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRule;
import com.jykj.dqm.quality.entity.DataqualityTaskInstance;
import com.jykj.dqm.quality.entity.DataqualityTaskInstanceVO;
import com.jykj.dqm.quality.entity.QCTaskDTO;
import com.jykj.dqm.quality.manager.QualityCheckTaskJobInterfaceImpl;
import com.jykj.dqm.quality.service.CheckRuleExecutionService;
import com.jykj.dqm.quartz.dao.ScheduleJobTaskDao;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.quartz.entity.TaskProcessResult;
import com.jykj.dqm.quartz.scheduler.QuartzManager;
import com.jykj.dqm.utils.IdUtils;
import com.jykj.dqm.utils.MapperUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 规则执行
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/26 15:55:34
 */
@Slf4j
@Service
public class CheckRuleExecutionServiceImpl implements CheckRuleExecutionService {
    @Autowired
    private CheckRuleExecutionMapper checkRuleExecutionMapper;

    @Autowired
    private DataqualityCheckRuleMapper dataqualityCheckRuleMapper;

    @Autowired
    private QuartzManager quartzManager;

    @Autowired
    private ScheduleJobTaskDao scheduleJobTaskDao;

    @Autowired
    private QualityCheckTaskJobInterfaceImpl qualityCheckTaskJobInterface;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private DataqualityTaskInstanceMapper dataqualityTaskInstanceMapper;

    @Override
    public R getAllSystem() {
        List<Map<String, String>> sysList = checkRuleExecutionMapper.getAllSystem();
        return RUtil.success(sysList);
    }

    @Override
    public R configRule(QCTaskDTO qcTaskDTO) {
        // 判断是否整体设置(整体执行标志：0：单独设置 1：整体设置)，如果是整体设置，需要判断是否覆盖，
        if ("1".equals(qcTaskDTO.getOverallSettingFlag())) {
            //根据系统编码，获取所有的规则
            LambdaQueryWrapper<DataqualityCheckRule> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DataqualityCheckRule::getSysCode, qcTaskDTO.getSysCode())
                    .eq(DataqualityCheckRule::getCheckRuleStatus, "0");
            List<DataqualityCheckRule> dataqualityCheckRules = dataqualityCheckRuleMapper.selectList(queryWrapper);
            //是否覆盖：0：不覆盖 1：覆盖
            if ("1".equals(qcTaskDTO.getWhetherCovered())) {
                //1：覆盖：不判定是否有已设置，统一覆盖了
                addRuleRunConfig(qcTaskDTO, dataqualityCheckRules);
            } else {
                //0：不覆盖：未设置的统一设置，已设置的就不管
                //获取已经设置的规则编号，通过BIZ_DATA_ID+TASK_TYPE
                Map<String, Object> build = MapUtil.builder(new HashMap<String, Object>())
                        .put("jobGroup", qcTaskDTO.getSysCode())
                        .put("taskType", "QC").build();
                List<ScheduleJobInfo> dbScheduleJobInfos = scheduleJobTaskDao.queryScheduleJobTaskByJobGroupTaskType(build);
                Set<String> checkRuleIds = dbScheduleJobInfos.stream().map(scheduleJobInfo -> scheduleJobInfo.getBizDataId()).collect(Collectors.toSet());
                List<DataqualityCheckRule> dataqualityCheckRuleNotConfigList = dataqualityCheckRules.stream()
                        .filter(dataqualityCheckRule -> !checkRuleIds.contains(Integer.toString(dataqualityCheckRule.getCheckRuleId())))
                        .collect(Collectors.toList());
                addRuleRunConfig(qcTaskDTO, dataqualityCheckRuleNotConfigList);
            }
        } else {
            Map<String, Object> collect = getBizDataIdAndJobIdHashMap(qcTaskDTO);
            if (!collect.containsKey(String.valueOf(qcTaskDTO.getCheckRuleId()))) {
                String snowflakeId = IdUtils.getID();
                qcTaskDTO.setJobId(snowflakeId);
            } else {
                qcTaskDTO.setJobId((String) collect.get(String.valueOf(qcTaskDTO.getCheckRuleId())));
            }
            ScheduleJobInfo jobInfo = MapperUtils.INSTANCE.map(ScheduleJobInfo.class, qcTaskDTO);
            jobInfo.setBizDataId(qcTaskDTO.getCheckRuleId());
            jobInfo.setTaskType("QC");
            jobInfo.setJobGroup(qcTaskDTO.getSysCode());
            quartzManager.removeJob(jobInfo);
            quartzManager.processScheduleJob(jobInfo);
        }
        return RUtil.success();
    }

    private Map<String, Object> getBizDataIdAndJobIdHashMap(QCTaskDTO qcTaskDTO) {
        //获取当前系统下的规则对应的JobId
        Map<String, Object> params = new HashMap<>();
        params.put("taskType", "QC");
        params.put("jobGroup", qcTaskDTO.getSysCode());
        List<ScheduleJobInfo> scheduleJobInfos = checkRuleExecutionMapper.getScheduleJobTaskByParams(params);
        return scheduleJobInfos.stream().collect(Collectors.toMap(ScheduleJobInfo::getBizDataId, ScheduleJobInfo::getJobId, (n1, n2) -> n2, HashMap<String, Object>::new));
    }

    @Override
    public R ruleRunInfoList(CheckRuleExecutionQueryDTO checkRuleExecutionQueryDTO) {
        PageHelper.startPage(checkRuleExecutionQueryDTO.getPageNum(), checkRuleExecutionQueryDTO.getPageSize());
        List<CheckRuleRunInfoVO> list = checkRuleExecutionMapper.ruleRunInfoList(checkRuleExecutionQueryDTO);
        PageInfo<CheckRuleRunInfoVO> pageInfo = new PageInfo<>(list);
        return RUtil.success(pageInfo);
    }

    @Override
    public R exeAllRuleBySys(CheckRuleExecutionDTO checkRuleExecutionDTO) {
        //添加Redis分布式锁
        boolean result = false;
        RLock lock = null;
        //获取分布式锁
        String errorMsg = "执行失败";
        try {
            lock = redissonClient.getLock("exeAllRuleBySysLock");
            result = lock.tryLock();
            if (result) {
                CheckRuleExecutionQueryDTO checkRuleExecutionQueryDTO =
                        CheckRuleExecutionQueryDTO.builder().sysCode(checkRuleExecutionDTO.getSysCode()).build();
                List<CheckRuleRunInfoVO> list = checkRuleExecutionMapper.ruleRunInfoList(checkRuleExecutionQueryDTO);
                //jobGroup特殊处理，用于之后查询该批任务的所有规则
                String jobGroup = IdUtils.getID();
                for (CheckRuleRunInfoVO checkRuleRunInfoVO : list) {
                    exeRule(jobGroup, Integer.toString(checkRuleRunInfoVO.getCheckRuleId()));
                }
                return RUtil.success(jobGroup);
            } else {
                return RUtil.error("已经有任务在执行，请稍后再试");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            errorMsg = errorMsg + "：" + e.getMessage();
        } finally {
            if (result && lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return RUtil.error(errorMsg);
    }

    private void exeRule(String sysCode, String checkRuleId) {
        ScheduleJobInfo scheduleJobInfo = ScheduleJobInfo.builder()
                .bizDataId(checkRuleId)
                .jobGroup(sysCode)
                .taskType("QC")
                .build();
        TaskProcessResult taskProcessResult = qualityCheckTaskJobInterface.processTask(scheduleJobInfo);
        if (taskProcessResult.getResultCode() != 0) {
            throw new BusinessException(taskProcessResult.getMsg());
        }
    }

    @Override
    public R exeOneRule(CheckRuleExecutionDTO checkRuleExecutionDTO) {
        //jobGroup特殊处理，用于之后查询该批任务的所有规则
        String jobGroup = IdUtils.getID();
        exeRule(jobGroup, Integer.toString(checkRuleExecutionDTO.getCheckRuleId()));
        return RUtil.success(jobGroup);
    }

    @Override
    public R getTable(CheckRuleTableOrFieldQueryDTO checkRuleTableOrFieldQueryDTO) {
        List<String> tables = checkRuleExecutionMapper.getTable(checkRuleTableOrFieldQueryDTO);
        return RUtil.success(tables);
    }

    @Override
    public R getTableField(CheckRuleTableOrFieldQueryDTO checkRuleTableOrFieldQueryDTO) {
        List<String> tableFields = checkRuleExecutionMapper.getTableField(checkRuleTableOrFieldQueryDTO);
        return RUtil.success(tableFields);
    }

    @Override
    public R getProgress(String groupId) {
        List<DataqualityTaskInstance> list = dataqualityTaskInstanceMapper.selectList(new LambdaQueryWrapper<DataqualityTaskInstance>().eq(DataqualityTaskInstance::getTaskGroupId, groupId));
        long completeNum = list.stream().filter(dataqualityTaskInstance -> "11".equals(dataqualityTaskInstance.getTaskState()) || "99".equals(dataqualityTaskInstance.getTaskState())).count();
        Map<String, Object> result = new HashMap<>();
        result.put("total", list.size());
        result.put("completeNum", completeNum);
        //比较完成数量和总数量，如果完成数量等于总数量，则返回list
        if (completeNum == list.size()) {
            //获取list中的taskStartDt（最小的开始时间，date类型）和taskEndDt（最大的结束时间），并计算总耗时
            long taskStartDt = list.stream().mapToLong(item -> item.getTaskStartDt().getTime()).min().orElse(0L);
            long taskEndDt = list.stream().mapToLong(item -> item.getTaskEndDt().getTime()).max().orElse(0L);
            long totalTime = taskEndDt - taskStartDt;
            result.put("totalTime", totalTime / 1000);
            CheckRuleExecutionRecordQueryDTO checkRuleExecutionRecordQueryDTO = new CheckRuleExecutionRecordQueryDTO();
            Set<String> taskInstanceIds = list.stream().map(item -> item.getTaskInstanceId() + "").collect(Collectors.toSet());
            //将集合taskInstanceIds转换为IN条件，格式为(id1,id2,id3)
            String ids = "(" + String.join(",", taskInstanceIds) + ")";
            checkRuleExecutionRecordQueryDTO.setIds(ids);
            List<DataqualityTaskInstanceVO> mapList = dataqualityTaskInstanceMapper.queryRecord(checkRuleExecutionRecordQueryDTO);
            //只返回有问题的规则
            List<DataqualityTaskInstanceVO> hasQstnList = mapList.stream()
                    .filter(item -> (!"JSX".equals(item.getCheckRuleFatherType()) && item.getDataQstnNum() != 0) || ("JSX".equals(item.getCheckRuleFatherType()) && item.getDataQstnNum() == 0))
                    .collect(Collectors.toList());
            result.put("list", hasQstnList);
        }
        return RUtil.success(result);
    }

    private void addRuleRunConfig(QCTaskDTO qcTaskDTO, List<DataqualityCheckRule> dataqualityCheckRuleNotConfigList) {
        //获取当前系统下的规则对应的JobId
        Map<String, Object> collect = getBizDataIdAndJobIdHashMap(qcTaskDTO);
        String key;
        for (DataqualityCheckRule dataqualityCheckRule : dataqualityCheckRuleNotConfigList) {
            key = String.valueOf(dataqualityCheckRule.getCheckRuleId());
            if (!collect.containsKey(key)) {
                String snowflakeId = IdUtils.getID();
                qcTaskDTO.setJobId(snowflakeId);
            } else {
                qcTaskDTO.setJobId((String) collect.get(key));
            }
            ScheduleJobInfo jobInfo = MapperUtils.INSTANCE.map(ScheduleJobInfo.class, qcTaskDTO);
            jobInfo.setBizDataId(key);
            jobInfo.setTaskType("QC");
            jobInfo.setJobGroup(dataqualityCheckRule.getSysCode());
            quartzManager.removeJob(jobInfo);
            quartzManager.processScheduleJob(jobInfo);
        }
    }
}
