package com.jykj.dqm.quality.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jykj.dqm.common.R;
import com.jykj.dqm.quality.entity.DataqualityCheckRule;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleQueryDTO;
import com.jykj.dqm.quality.entity.RuleFirstPageQueryDTO;

import java.util.Map;

/**
 * 检核规则
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/20 14:26:46
 */
public interface DataqualityCheckRuleService extends IService<DataqualityCheckRule> {
    R firstPageList(RuleFirstPageQueryDTO ruleFirstPageQueryDTO);

    R ruleList(DataqualityCheckRuleQueryDTO dataqualityCheckRuleQueryDTO);

    R addRule(DataqualityCheckRule dataqualityCheckRule);

    R updateRule(DataqualityCheckRule dataqualityCheckRule);

    R getTable(String dataSourceId, String tableName);

    R getTableField(String dataSourceId, String tableName, String fieldName, Integer pageSize, Integer pageNum);

    R getDictTable(String tableName, Integer pageSize, Integer pageNum);

    R getDictTableFiled(String fieldName, Integer pageSize, Integer pageNum);

    /**
     * 根据目前的配置获取检核规则SQL
     *
     * @param dataqualityCheckRule DataqualityCheckRule
     * @return 检核规则SQL
     * <AUTHOR>
     */
    R getCheckSql(DataqualityCheckRule dataqualityCheckRule);

    R delRule(String checkRuleId);

    R updateRuleStatus(Map<String, Object> dataqualityCheckRule);

    R getCopiedSerialNumber(String checkRuleName);
}
