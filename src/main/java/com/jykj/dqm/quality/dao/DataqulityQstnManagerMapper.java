package com.jykj.dqm.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.quality.entity.DataqulityQstnManager;
import com.jykj.dqm.quality.entity.DataqulityQstnManagerQueryDTO;
import com.jykj.dqm.quality.entity.DataqulityQstnManagerVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 数据质量问题管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/7 10:33:34
 */
@Mapper
public interface DataqulityQstnManagerMapper extends BaseMapper<DataqulityQstnManager> {
    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(DataqulityQstnManager record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DataqulityQstnManager record);

    List<Map<String, Object>> getAllSysAndDb();

    List<DataqulityQstnManagerVO> queryList(DataqulityQstnManagerQueryDTO dataqulityQstnManagerQueryDTO);

    List<Map<String, Object>> getProblemDataTrends(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 问题占比情况
     *
     * @return sum(DATA_QSTN_NUM) TOTAL,DATA_QLTY_QSTN_NM QSTN_NM
     * <AUTHOR>
     */
    List<Map<String, Object>> getTheQstnProportion();
}