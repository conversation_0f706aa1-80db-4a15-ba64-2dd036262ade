package com.jykj.dqm.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.quality.entity.DataqualityCheckRule;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleQueryDTO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 检核规则
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/20 14:26:46
 */
@Mapper
public interface DataqualityCheckRuleMapper extends BaseMapper<DataqualityCheckRule> {
    List<DataqualityCheckRule> ruleList(DataqualityCheckRuleQueryDTO dataqualityCheckRuleDTO);

    /**
     * 排除DQM_METADATA_STRUCTURE_CHOOSE_RESULT中不包含的表，剩下的全部表
     *
     * @param dataSourceId 数据源ID
     * @param tableName    表名
     * @return 排除不包含的全部表
     * <AUTHOR>
     */
    List<String> getTableFromAllExcludeNotContain(@Param("dataSourceId") String dataSourceId, @Param("tableName") String tableName);

    /**
     * 包含的表，从DQM_METADATA_STRUCTURE_CHOOSE_RESULT 中获取
     *
     * @param dataSourceId 数据源ID
     * @param tableName    表名
     * @return 包含的表
     * <AUTHOR>
     */
    List<String> getTableFromChooseResult(@Param("dataSourceId") String dataSourceId, @Param("tableName") String tableName);

    /**
     * 获取字段
     *
     * @param dataSourceId 数据源ID
     * @param tableName    表名
     * @param fieldName    字段名（模糊匹配）
     * @return 字段Map
     * <AUTHOR>
     */
    List<Map<String, Object>> getTableField(@Param("dataSourceId") String dataSourceId,
                                            @Param("tableName") String tableName,
                                            @Param("fieldName") String fieldName);
    @MapKey("type")
    Map<String, Map<String,Integer>> getAllTypeTableNum(String dataSourceId);

    int getContainTableNum(String dataSourceId);

    List<Map<String, Object>> getDictTable(@Param("tableName") String tableName);

    List<Map<String, Object>> getDictTableFiled(@Param("fieldName") String fieldName);

    List<Map<String, Object>> getSysCheckRule();
}