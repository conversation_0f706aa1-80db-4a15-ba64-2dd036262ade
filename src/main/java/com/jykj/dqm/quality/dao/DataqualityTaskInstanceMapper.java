package com.jykj.dqm.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.quality.entity.CheckRuleExecutionRecordQueryDTO;
import com.jykj.dqm.quality.entity.DataqualityTaskInstance;
import com.jykj.dqm.quality.entity.DataqualityTaskInstanceVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 数据质量检核任务执行情况
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/27 17:48:23
 */
@Mapper
public interface DataqualityTaskInstanceMapper extends BaseMapper<DataqualityTaskInstance> {
    List<DataqualityTaskInstanceVO> queryRecord(CheckRuleExecutionRecordQueryDTO checkRuleExecutionRecordQueryDTO);

    int updateTaskState();
}