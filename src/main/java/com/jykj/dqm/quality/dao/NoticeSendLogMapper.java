package com.jykj.dqm.quality.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jykj.dqm.quality.entity.NoticeSendLog;
import org.apache.ibatis.annotations.Mapper;

/**
 * 通知发送消息日志
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/11 15:47:54
 */
@Mapper
public interface NoticeSendLogMapper extends BaseMapper<NoticeSendLog> {
    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(NoticeSendLog record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(NoticeSendLog record);
}