package com.jykj.dqm.quality.dao;

import com.jykj.dqm.quality.entity.CheckRuleExecutionQueryDTO;
import com.jykj.dqm.quality.entity.CheckRuleRunInfoVO;
import com.jykj.dqm.quality.entity.CheckRuleTableOrFieldQueryDTO;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 规则执行
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/26 15:56:31
 */
@Mapper
public interface CheckRuleExecutionMapper {
    /**
     * 获取所有规则的系统
     *
     * @return List<Map < String, String>>  sysCode, sysName
     * <AUTHOR>
     */
    List<Map<String, String>> getAllSystem();

    /**
     * 根据系统，以及其他条件查询对应的规则(启用状态的)
     *
     * @param checkRuleExecutionQueryDTO CheckRuleExecutionQueryDTO
     * @return List<CheckRuleRunInfoVO>
     * <AUTHOR>
     */
    List<CheckRuleRunInfoVO> ruleRunInfoList(CheckRuleExecutionQueryDTO checkRuleExecutionQueryDTO);

    List<String> getTable(CheckRuleTableOrFieldQueryDTO checkRuleTableOrFieldQueryDTO);

    List<String> getTableField(CheckRuleTableOrFieldQueryDTO checkRuleTableOrFieldQueryDTO);

    List<ScheduleJobInfo> getScheduleJobTaskByParams(Map<String, Object> params);
}
