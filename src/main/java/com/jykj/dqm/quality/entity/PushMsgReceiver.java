package com.jykj.dqm.quality.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 推送消息接收方
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/11 14:03:21
 */
@ApiModel(value = "推送消息接收方")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PushMsgReceiver implements Serializable {
    @ApiModelProperty(value = "账号类型 0:邮箱 1：短信")
    String accountType;
    @ApiModelProperty(value = "账号")
    String account;
    private static final long serialVersionUID = 1L;
}
