package com.jykj.dqm.quality.entity;

import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 检核规则
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/20 14:26:46
 */
@ApiModel(value = "DataqualityCheckRuleQueryDTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataqualityCheckRuleQueryDTO extends MyPageInfo {
    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
    private Integer checkRuleId;

    /**
     * 检核规则名称
     */
    @ApiModelProperty(value = "检核规则名称")
    private String checkRuleName;

    /**
     * 问题分类编码：提示，预警，严重
     */
    @ApiModelProperty(value = "问题分类编码：提示，预警，严重")
    private String qstType;

    /**
     * 检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性
     */
    @ApiModelProperty(value = "检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性")
    private String checkRuleFatherType;

    /**
     * 检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个）
     */
    @ApiModelProperty(value = "检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个）")
    private String checkRuleType;

    /**
     * 检核系统代码
     */
    @ApiModelProperty(value = "检核系统代码")
    @NotBlank
    private String sysCode;

    /**
     * 检核系统名称
     */
    @ApiModelProperty(value = "检核系统名称")
    private String sysName;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    @NotBlank
    private String dbNm;

    /**
     * 检查表或视图
     */
    @ApiModelProperty(value = "检查表或视图")
    private String checkRuleTableOrView;

    /**
     * 检核字段
     */
    @ApiModelProperty(value = "检核字段")
    private String checkRuleColumn;

    private static final long serialVersionUID = 1L;
}
