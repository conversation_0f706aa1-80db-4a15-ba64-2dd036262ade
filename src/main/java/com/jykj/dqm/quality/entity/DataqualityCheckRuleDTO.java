package com.jykj.dqm.quality.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 检核规则
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/20 14:26:46
 */
@ApiModel(value = "DataqualityCheckRuleDTO")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class DataqualityCheckRuleDTO extends DataqualityCheckRule {
    /**
     * 数据库类型
     */
    @ApiModelProperty(value = "数据库类型")
    private String dbType;

    private static final long serialVersionUID = 1L;
}
