package com.jykj.dqm.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 通知发送消息日志
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/11 15:47:54
 */
@ApiModel(value = "通知发送消息日志")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_NOTICE_SEND_LOG")
public class NoticeSendLog implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Integer id;

    /**
     * 问题ID
     */
    @TableField(value = "DATA_QLTY_QSTN_ID")
    @ApiModelProperty(value = "问题ID")
    private String dataQltyQstnId;

    /**
     * 账号
     */
    @TableField(value = "ACCOUNT")
    @ApiModelProperty(value = "账号")
    private String account;

    /**
     * 账号类型
     */
    @TableField(value = "ACCOUNT_TYPE")
    @ApiModelProperty(value = "账号类型")
    private String accountType;

    /**
     * 通知内容
     */
    @TableField(value = "NOTICE_MSG")
    @ApiModelProperty(value = "通知内容")
    private String noticeMsg;

    /**
     * 通知时间
     */
    @TableField(value = "NOTICE_TIME")
    @ApiModelProperty(value = "通知时间")
    private Date noticeTime;

    /**
     * 通知结果
     */
    @TableField(value = "NOTICE_RESULT")
    @ApiModelProperty(value = "通知结果")
    private String noticeResult;

    private static final long serialVersionUID = 1L;
}