package com.jykj.dqm.quality.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 执行记录VO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/31 16:29:30
 */
@NoArgsConstructor
@Data
public class DataqualityTaskInstanceVO {
    /**
     * 检核规则编号
     */
    @ApiModelProperty(value = "检核规则编号")
    private Integer checkRuleId;

    /**
     * 任务组编号
     */
    @ApiModelProperty(value = "任务组编号")
    private String taskGroupId;
    @ApiModelProperty(value = "系统编码")
    private String sysCode;
    @ApiModelProperty(value = "系统名称")
    private String sysName;
    @ApiModelProperty(value = "执行数据库")
    private String dbNm;
    @ApiModelProperty(value = "执行规行名称")
    private String checkRuleName;
    @ApiModelProperty(value = "执行规行描述")
    private String CheckRuleDesc;
    @ApiModelProperty(value = "检测表名")
    private String checkRuleTableOrView;
    @ApiModelProperty(value = "检测字段名")
    private String checkRuleColumn;
    @ApiModelProperty(value = "执行状态")
    private String taskState;
    @ApiModelProperty(value = "任务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startDt;
    @ApiModelProperty(value = "任务结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endDt;
    @ApiModelProperty(value = "执行结果")
    private String taskResult;
    /**
     * 数据总记录数量
     */
    @ApiModelProperty(value = "数据总记录数量")
    private Integer dataTotalNum;

    /**
     * 数据质量问题数量
     */
    @ApiModelProperty(value = "数据质量问题数量")
    private Integer dataQstnNum;

    @ApiModelProperty(value = "问题分类编码：提示，预警，严重")
    private String qstType;

    @ApiModelProperty(value = "问题明细SQL")
    private String pbSubsidiarySql;

    /**
     * 问题ID
     */
    @ApiModelProperty(value = "问题ID")
    private String dataQltyQstnId;

    /**
     * 检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性
     */
    @ApiModelProperty(value = "检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性")
    private String checkRuleFatherType;
}
