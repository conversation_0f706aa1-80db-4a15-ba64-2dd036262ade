package com.jykj.dqm.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据质量检核任务执行情况
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/27 17:48:23
 */
@ApiModel(value = "数据质量检核任务执行情况")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_DATAQUALITY_TASK_INSTANCE")
public class DataqualityTaskInstance implements Serializable {
    /**
     * ID
     */
    @TableId(value = "TASK_INSTANCE_ID", type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Integer taskInstanceId;

    /**
     * 检核规则编号
     */
    @TableField(value = "CHECK_RULE_ID")
    @ApiModelProperty(value = "检核规则编号")
    private Integer checkRuleId;

    /**
     * 任务组编号
     */
    @TableField(value = "TASK_GROUP_ID")
    @ApiModelProperty(value = "任务组编号")
    private String taskGroupId;

    /**
     * 00:执行中,11:成功,99:失败
     */
    @TableField(value = "TASK_STATE")
    @ApiModelProperty(value = "00:执行中,11:成功,99:失败")
    private String taskState;

    /**
     * 开始时间
     */
    @TableField(value = "TASK_START_DT")
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskStartDt;

    /**
     * 结束时间
     */
    @TableField(value = "TASK_END_DT")
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskEndDt;

    /**
     * 执行结果
     */
    @TableField(value = "TASK_RESULT")
    @ApiModelProperty(value = "执行结果")
    private String taskResult;

    /**
     * 数据总记录数量
     */
    @TableField(value = "DATA_TOTAL_NUM")
    @ApiModelProperty(value = "数据总记录数量")
    private Integer dataTotalNum;

    /**
     * 数据质量问题数量
     */
    @TableField(value = "DATA_QSTN_NUM")
    @ApiModelProperty(value = "数据质量问题数量")
    private Integer dataQstnNum;

    /**
     * 问题ID
     */
    @TableField(value = "DATA_QLTY_QSTN_ID")
    @ApiModelProperty(value = "问题ID")
    private String dataQltyQstnId;

    private static final long serialVersionUID = 1L;
}