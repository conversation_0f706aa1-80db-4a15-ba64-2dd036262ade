package com.jykj.dqm.quality.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 规则执行展示
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/20 14:26:46
 */
@ApiModel(value = "CheckRuleRunInfoVO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckRuleRunInfoVO {
    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
    private Integer checkRuleId;

    /**
     * 检核规则名称
     */
    @ApiModelProperty(value = "检核规则名称")
    private String checkRuleName;

    /**
     * 检核规则说明
     */
    @ApiModelProperty(value = "检核规则说明")
    private String checkRuleDesc;

    /**
     * 检核规则状态 0：启用 1：未启用
     */
    @ApiModelProperty(value = "检核规则状态 0：启用 1：未启用")
    private String checkRuleStatus;

    /**
     * 问题分类编码：提示，预警，严重
     */
    @ApiModelProperty(value = "问题分类编码：提示，预警，严重")
    private String qstType;

    /**
     * 检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性
     */
    @ApiModelProperty(value = "检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性")
    private String checkRuleFatherType;

    /**
     * 检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个）
     */
    @ApiModelProperty(value = "检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个）")
    private String checkRuleType;

    /**
     * 检核系统代码
     */
    @ApiModelProperty(value = "检核系统代码")
    private String sysCode;

    /**
     * 检核系统名称
     */
    @ApiModelProperty(value = "检核系统名称")
    private String sysName;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    private String dbNm;

    /**
     * 检核SCHEMA代码
     */
    @ApiModelProperty(value = "检核SCHEMA代码")
    private String checkSchema;

    /**
     * 检查表或视图
     */
    @ApiModelProperty(value = "检查表或视图")
    private String checkRuleTableOrView;

    /**
     * 检查表或视图中文名
     */
    @ApiModelProperty(value = "检查表或视图中文名")
    private String checkRuleTableOrViewCn;

    /**
     * 检核字段
     */
    @ApiModelProperty(value = "检核字段")
    private String checkRuleColumn;

    /**
     * 检测字段中文
     */
    @ApiModelProperty(value = "检测字段中文")
    private String checkRuleColumnCn;

    /**
     * 执行类别
     */
    @ApiModelProperty(value = "执行类别：0：手动执行，1：定时调度")
    private String jobExeType;

    /**
     * 执行频率
     */
    @ApiModelProperty(value = "执行频率")
    private String jobDesc;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String jobStartDate;

    /**
     * 起始时间
     */
    @ApiModelProperty(value = "起始时间")
    private String jobStartTime;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束日期")
    private String jobEndDate;

    /**
     * 间隔时间
     */
    @ApiModelProperty(value = "间隔时间")
    private String jobSpan;

    /**
     * 执行频率
     */
    @ApiModelProperty(value = "执行频率")
    private String jobExeRate;

    /**
     * 周执行星期
     */
    @ApiModelProperty(value = "周执行星期")
    private String dayOfWeek;
    /**
     * 月的执行日期
     */
    @ApiModelProperty(value = "月的执行日期")
    private String dayOfMonth;
    /**
     * 月的执行周次
     */
    @ApiModelProperty(value = "月的执行周次")
    private String weekTime;
    /**
     * 月的执行星期
     */
    @ApiModelProperty(value = "月的执行星期")
    private String dayOfWeek2;
    /**
     * 月的执行方式
     */
    @ApiModelProperty(value = "月的执行方式")
    private String chkType;
    /**
     * 执行的月份
     */
    @ApiModelProperty(value = "执行的月份")
    private String chkMonth;

    private static final long serialVersionUID = 1L;
}
