package com.jykj.dqm.quality.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jykj.dqm.config.group.CustomValidateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 检核规则
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/20 14:26:46
 */
@ApiModel(value = "DQM_DATAQUALITY_CHECK_RULE")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_DATAQUALITY_CHECK_RULE")
public class DataqualityCheckRule implements Serializable {
    /**
     * 规则ID
     */
    @TableId(value = "CHECK_RULE_ID", type = IdType.AUTO)
    @ApiModelProperty(value = "规则ID")
    @NotNull(groups = CustomValidateGroup.Crud.Update.class, message = "更新的时候规则ID不能为空")
    private Integer checkRuleId;

    /**
     * 检核规则名称
     */
    @NotBlank(message = "检核规则名称不能为空")
    @TableField(value = "CHECK_RULE_NAME")
    @ApiModelProperty(value = "检核规则名称")
    private String checkRuleName;

    /**
     * 检核规则说明
     */
    @TableField(value = "CHECK_RULE_DESC")
    @ApiModelProperty(value = "检核规则说明")
    private String checkRuleDesc;

    /**
     * 标准主题
     */
    @TableField(value = "STAND_TITLE")
    @ApiModelProperty(value = "标准主题", hidden = true)
    private String standTitle;

    /**
     * 是否需要发送邮件Y/N
     */
    @TableField(value = "SEND_MAIL")
    @ApiModelProperty(value = "是否需要发送邮件Y/N", hidden = true)
    private String sendMail;

    /**
     * 问题分类编码：提示，预警，严重
     */
    @NotBlank(message = "问题分类不能为空")
    @TableField(value = "QST_TYPE")
    @ApiModelProperty(value = "问题分类编码：提示，预警，严重")
    private String qstType;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")   //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")    //前端-->后端显示
    private Date createDate;

    /**
     * 修改时间
     */
    @TableField(value = "UPDATE_DATE")
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")   //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")    //前端-->后端显示
    private Date updateDate;

    /**
     * 检核规则状态 0：启用 1：未启用
     */
    @TableField(value = "CHECK_RULE_STATUS")
    @ApiModelProperty(value = "检核规则状态 0：启用 1：未启用")
    private String checkRuleStatus;

    /**
     * 启用日期
     */
    @TableField(value = "COMM_DATE")
    @ApiModelProperty(value = "启用日期", hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")   //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private String commDate;

    /**
     * 停用日期
     */
    @TableField(value = "STOP_DATE")
    @ApiModelProperty(value = "停用日期", hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")   //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private String stopDate;

    /**
     * 规则来源
     */
    @TableField(value = "CHECK_RULE_SOURCE")
    @ApiModelProperty(value = "规则来源", hidden = true)
    private String checkRuleSource;

    /**
     * 规则编号，有规律的
     */
    @TableField(value = "RULE_NO")
    @ApiModelProperty(value = "规则编号，有规律的", hidden = true)
    private String ruleNo;

    /**
     * 检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性
     */
    @NotBlank(message = "规则大类不能为空")
    @TableField(value = "CHECK_RULE_FATHER_TYPE")
    @ApiModelProperty(value = "检核规则父类型，完整性，一致性，准确性，有效性，规范性，及时性")
    private String checkRuleFatherType;

    /**
     * 检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个）
     */
    @NotBlank(message = "规则小类不能为空")
    @TableField(value = "CHECK_RULE_TYPE")
    @ApiModelProperty(value = "检核规则类型，整体完整性，条件完整性，一致性，唯一性，计算正确性，代码有效性，范围有效性，长度规范性，及时性，自定义（每个大类一个）")
    private String checkRuleType;

    /**
     * 规则来源系统
     */
    @TableField(value = "RULE_SORC_SYS_NM")
    @ApiModelProperty(value = "规则来源系统", hidden = true)
    private String ruleSorcSysNm;

    /**
     * 检核数据源，为空或0使用检核系统的数据源
     */
    @TableField(value = "CHECK_DATASOURCE")
    @ApiModelProperty(value = "检核数据源，为空或0使用检核系统的数据源", hidden = true)
    private String checkDatasource;

    /**
     * 检核系统代码
     */
    @TableField(value = "SYS_CODE")
    @ApiModelProperty(value = "检核系统代码")
    @NotBlank(message = "检核系统代码不能为空")
    private String sysCode;

    /**
     * 检核系统名称
     */
    @TableField(value = "SYS_NAME")
    @ApiModelProperty(value = "检核系统名称")
    private String sysName;

    /**
     * 数据库名称
     */
    @TableField(value = "DB_NM")
    @ApiModelProperty(value = "数据库名称")
    @NotBlank(message = "数据库名称不能为空")
    private String dbNm;

    /**
     * 检核SCHEMA
     */
    @TableField(value = "CHECK_SCHEMA")
    @ApiModelProperty(value = "检核SCHEMA")
    @NotBlank(message = "检核SCHEMA不能为空")
    private String checkSchema;

    /**
     * 副表检核SCHEMA代码
     */
    @TableField(value = "ST_CHECK_SCHEMA")
    @ApiModelProperty(value = "副表检核SCHEMA代码")
    private String stCheckSchema;

    /**
     * 检查表或视图
     */
    @TableField(value = "CHECK_RULE_TABLE_OR_VIEW")
    @ApiModelProperty(value = "检查表或视图")
    private String checkRuleTableOrView;

    /**
     * 检查表或视图中文名
     */
    @TableField(value = "CHECK_RULE_TABLE_OR_VIEW_CN")
    @ApiModelProperty(value = "检查表或视图中文名")
    private String checkRuleTableOrViewCn;

    /**
     * 副表表名或视图名/字典表或视图
     */
    @TableField(value = "ST_CHECK_RULE_TABLE_OR_VIEW")
    @ApiModelProperty(value = "副表表名或视图名/字典表或视图")
    private String stCheckRuleTableOrView;

    /**
     * 副表表中文名或视图中文名
     */
    @TableField(value = "ST_CHECK_RULE_TABLE_OR_VIEW_CN")
    @ApiModelProperty(value = "副表表中文名或视图中文名")
    private String stCheckRuleTableOrViewCn;

    /**
     * 规则是表还是视图0是表1是视图
     */
    @TableField(value = "IS_TABLE_OR_VIEW")
    @ApiModelProperty(value = "规则是表还是视图0是表1是视图")
    private String isTableOrView;

    /**
     * 是外键的表还是视图0是表1是视图
     */
    @TableField(value = "ST_IS_TABLE_OR_VIEW")
    @ApiModelProperty(value = "是外键的表还是视图0是表1是视图")
    private String stIsTableOrView;

    /**
     * 检核字段
     */
    @TableField(value = "CHECK_RULE_COLUMN")
    @ApiModelProperty(value = "检核字段")
    private String checkRuleColumn;

    /**
     * 检测字段中文
     */
    @TableField(value = "CHECK_RULE_COLUMN_CN")
    @ApiModelProperty(value = "检测字段中文")
    private String checkRuleColumnCn;

    /**
     * 副表字段名/明细表字段1/字典字段
     */
    @TableField(value = "ST_CHECK_RULE_COLUMN")
    @ApiModelProperty(value = "副表字段名/明细表字段1/字典字段")
    private String stCheckRuleColumn;

    /**
     * 副表字段中文名
     */
    @TableField(value = "ST_CHECK_RULE_COLUMN_CN")
    @ApiModelProperty(value = "副表字段中文名")
    private String stCheckRuleColumnCn;

    /**
     * where条件
     */
    @TableField(value = "CHECK_RULE_WHERE")
    @ApiModelProperty(value = "where条件")
    @JsonFormat()
    private String checkRuleWhere;

    /**
     * 问题明细列
     */
    @TableField(value = "PB_SUBSIDIARY_COLUMNS")
    @ApiModelProperty(value = "问题明细列")
    private String pbSubsidiaryColumns;

    /**
     * 值域：0数值、1日期 ,2字符串；长度：0等于、1不等于、2大于、3小于、4.大于等于、5小于等于 ； 一致性：count、sum
     */
    @TableField(value = "CHECK_OBJECT_TYPE")
    @ApiModelProperty(value = "范围有效性：0数值、1日期 ,2字符串；长度规范性/长度执行条件：0等于、1不等于、2大于、3小于、4.大于等于、5小于等于 ； 一致性：count、sum")
    private String checkObjectType;

    /**
     * 最大值
     */
    @TableField(value = "MAX_VALUE")
    @ApiModelProperty(value = "上限值/下限值")
    private String maxValue;

    /**
     * 最小值
     */
    @TableField(value = "MIN_VALUE")
    @ApiModelProperty(value = "最小值/下限值")
    private String minValue;

    @TableField(value = "COLUMNS_LENGTH")
    @ApiModelProperty(value = "字段长度")
    private String columnsLength;

    /**
     * 最大值是否包含0：包含 1不包含
     */
    @TableField(value = "MAX_VALUE_CONTAIN")
    @ApiModelProperty(value = "最大值是否包含0：包含 1不包含")
    private String maxValueContain;

    /**
     * 最小值是否包含0：包含 1不包含
     */
    @TableField(value = "MIN_VALUE_CONTAIN")
    @ApiModelProperty(value = "最小值是否包含0：包含 1不包含")
    private String minValueContain;

    /**
     * 检核方式0:null，1：空字符串
     */
    @TableField(value = "ISNULLTAG")
    @ApiModelProperty(value = "检核方式0:null，1：空字符串")
    private String isnulltag;

    /**
     * 检核标准来源代码，1数据源/2对标码值管理
     */
    @TableField(value = "RVW_STDD_SORC_CD")
    @ApiModelProperty(value = "检核标准来源代码，1数据源/2对标码值管理", hidden = true)
    private String rvwStddSorcCd;

    /**
     * 日期执行条件描述(0：T 1：T-1 2：T-2；3:T-3)
     */
    @TableField(value = "DT_EXEC_COND_DESCR")
    @ApiModelProperty(value = "日期执行条件描述(0：T 1：T-1 2：T-2；3:T-3)")
    private String dtExecCondDescr;

    /**
     * 精度执行条件代码
     */
    @TableField(value = "PRCSN_EXEC_COND_CD")
    @ApiModelProperty(value = "精度执行条件代码")
    private String prcsnExecCondCd;

    /**
     * 结果小数位数
     */
    @TableField(value = "RESULT_SML_NMRL_DGIT")
    @ApiModelProperty(value = "结果小数位数/字段精度")
    private String resultSmlNmrlDgit;

    /**
     * 字段数据类型
     */
    @TableField(value = "FIELD_DATA_TYP")
    @ApiModelProperty(value = "字段数据类型")
    private String fieldDataTyp;

    /**
     * 码值字段名称/明细表字段2
     */
    @TableField(value = "CD_VAL_FIELD_NM")
    @ApiModelProperty(value = "码值字段名称/明细表字段2")
    private String cdValFieldNm;

    /**
     * 计算规则代码
     */
    @TableField(value = "CALC_RULE_CD")
    @ApiModelProperty(value = "计算规则代码")
    private String calcRuleCd;

    /**
     * 代码值/类型
     */
    @TableField(value = "CD_VAL")
    @ApiModelProperty(value = "代码值/类型")
    private String cdVal;

    /**
     * 问题数SQL
     */
    @TableField(value = "QUESTION_NM_SQL")
    @ApiModelProperty(value = "问题数SQL")
    private String questionNmSql;

    /**
     * 总数据记录数SQL
     */
    @TableField(value = "TOTAL_NM_SQL")
    @ApiModelProperty(value = "总数据记录数SQL")
    private String totalNmSql;

    /**
     * 问题明细SQL
     */
    @TableField(value = "PB_SUBSIDIARY_SQL")
    @ApiModelProperty(value = "问题明细SQL")
    private String pbSubsidiarySql;

    @TableField(exist = false)
    @ApiModelProperty(value = "检核SQL", hidden = true)
    CheckSqlDTO checkSqlDTO;

    @TableField(exist = false)
    @ApiModelProperty(value = "主外键对应关系")
    List<PrimaryForeignFiled> primaryForeignFileds;

    @TableField(exist = false)
    @ApiModelProperty(value = "记录数", hidden = true)
    Integer recodeCount;

    private static final long serialVersionUID = 1L;
}