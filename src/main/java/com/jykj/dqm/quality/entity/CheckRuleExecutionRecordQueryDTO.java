package com.jykj.dqm.quality.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 规则执行记录QueryDTO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/20 14:26:46
 */
@ApiModel(value = "CheckRuleExecutionRecordQueryDTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckRuleExecutionRecordQueryDTO extends MyPageInfo {
    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
    private Integer checkRuleId;

    /**
     * 检核规则名称
     */
    @ApiModelProperty(value = "检核规则名称")
    private String checkRuleName;

    /**
     * 检核系统代码
     */
    @NotBlank
    @ApiModelProperty(value = "检核系统代码")
    private String sysCode;

    /**
     * 检核系统名称
     */
    @ApiModelProperty(value = "检核系统名称")
    private String sysName;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    private String dbNm;

    /**
     * 检查表或视图
     */
    @ApiModelProperty(value = "检查表或视图")
    private String checkRuleTableOrView;

    /**
     * 检核字段
     */
    @ApiModelProperty(value = "检核字段")
    private String checkRuleColumn;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")   //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private Date startDt;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")   //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private Date endDt;

    private String ids;

    private static final long serialVersionUID = 1L;
}
