package com.jykj.dqm.quality.entity;

import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 规则执行DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/20 14:26:46
 */
@ApiModel(value = "规则执行DTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckRuleExecutionDTO{
    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
    private Integer checkRuleId;

    /**
     * 检核系统代码
     */
    @NotBlank
    @ApiModelProperty(value = "检核系统代码")
    private String sysCode;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    private String dbNm;

    private static final long serialVersionUID = 1L;
}
