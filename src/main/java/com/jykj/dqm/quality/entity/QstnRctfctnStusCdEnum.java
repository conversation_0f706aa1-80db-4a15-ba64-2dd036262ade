package com.jykj.dqm.quality.entity;

/**
 * 问题整改状态代码（1、关闭、2、处理中、3、待处理）
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/7 13:53:53
 */
public enum QstnRctfctnStusCdEnum {
    CLOSE("1", "关闭"),
    PROCESSING("2", "处理中"),
    PENDING("3", "待处理");

    String code;
    String value;

    QstnRctfctnStusCdEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
