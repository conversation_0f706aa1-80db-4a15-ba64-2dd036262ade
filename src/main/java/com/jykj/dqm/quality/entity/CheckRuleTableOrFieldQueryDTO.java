package com.jykj.dqm.quality.entity;

import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 检核规则查询表和系统
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/4 17:36:31
 */
@Data
public class CheckRuleTableOrFieldQueryDTO extends MyPageInfo {
    @NotNull(message = "系统编码不能为空")
    @ApiModelProperty(value = "系统编码")
    String sysCode;

    @NotNull(message = "数据库名称不能为空")
    @ApiModelProperty(value = "数据库名称")
    String dbNm;

    @ApiModelProperty(value = "表名(查字段时不能为空)")
    String tableName;

    @ApiModelProperty(value = "字段名")
    String fieldName;
}
