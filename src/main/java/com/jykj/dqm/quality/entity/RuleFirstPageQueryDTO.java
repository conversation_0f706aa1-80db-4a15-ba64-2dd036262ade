package com.jykj.dqm.quality.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jykj.dqm.common.MyPageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 规则配置第一页列表查询条件
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/25 9:53:48
 */
@ApiModel(value = "规则配置第一页列表查询条件")
@Data
public class RuleFirstPageQueryDTO extends MyPageInfo {
    /**
     * 系统或数据库名称
     */
    @ApiModelProperty(value = "系统或数据库名称")
    private String sysOrDbname;

    /**
     * 执行方式
     */
    @ApiModelProperty(value = "执行方式：0：手动执行，1：定时调度")
    private String jobExeType;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")   //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private Date startDt;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")   //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd")    //前端-->后端显示
    private Date endDt;
}
