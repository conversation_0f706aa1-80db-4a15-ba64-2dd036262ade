package com.jykj.dqm.quality.manager;

import com.jykj.dqm.common.R;
import com.jykj.dqm.quality.dao.DataqualityTaskInstanceMapper;
import com.jykj.dqm.quality.entity.DataqualityTaskInstance;
import com.jykj.dqm.quartz.entity.ScheduleJobInfo;
import com.jykj.dqm.quartz.entity.TaskProcessResult;
import com.jykj.dqm.quartz.service.TaskJobInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class QualityCheckTaskJobInterfaceImpl implements TaskJobInterface {
    @Autowired
    private QualityCheckService qualityCheckService;

    @Autowired
    private DataqualityTaskInstanceMapper dataqualityTaskInstanceMapper;

    @Override
    public String getTaskType() {
        return "QC";
    }

    @Override
    public String getTaskDesc() {
        return "数据质量检查";
    }

    /**
     * 执行任务
     *
     * @param jobData 任务执行参数  checkRuleId,sysCode
     * @return TaskProcessResult 0-》OK   1-》数据质量检查任务正在执行中……请稍后    2-》数据质量检查任务执行失败
     * <AUTHOR>
     */
    @Override
    public TaskProcessResult processTask(ScheduleJobInfo jobData) {
        Date startDate = new Date();
        log.debug(getTaskDesc() + "启动");
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("checkRuleId", jobData.getBizDataId());
        params.put("sysCode", jobData.getJobGroup());
        R result = qualityCheckService.exeRuleCheckTask(params);
        TaskProcessResult taskResult = new TaskProcessResult();
        if (result.getStatus() == 0) {
            taskResult.setResultCode(0);
            taskResult.setMsg("OK");
        } else if (result.getStatus() == 1) {
            taskResult.setResultCode(1);
            taskResult.setMsg("相同数据质量检查任务正在执行中……请稍后");
        } else {
            taskResult.setResultCode(2);
            taskResult.setMsg("数据质量检查任务执行失败");
        }

        //处理异常情况的执行记录
        if (taskResult.getResultCode() != 0) {
            DataqualityTaskInstance dataqualityTaskInstance =
                    DataqualityTaskInstance.builder()
                            .taskGroupId(jobData.getJobGroup())
                            .taskStartDt(startDate)
                            .taskEndDt(new Date())
                            .taskState("99")
                            .checkRuleId(Integer.parseInt(jobData.getBizDataId()))
                            .taskResult(taskResult.getMsg())
                            .build();
            dataqualityTaskInstanceMapper.insert(dataqualityTaskInstance);
        }
        return taskResult;
    }
}
