package com.jykj.dqm.quality.manager.rulesql;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 检核SQL工厂(+策略模式)
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/22 17:23:30
 */
//@Component
//public class CheckSqlFactory implements ApplicationContextAware {
//    private static Map<String, CheckSqlGenarate> checkSqlGenarateMap = new ConcurrentHashMap<>();
//
//    @Override
//    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
//        Map<String, CheckSqlGenarate> beanMap = applicationContext.getBeansOfType(CheckSqlGenarate.class);
//        beanMap.values().forEach(a -> checkSqlGenarateMap.put(a.gainCheckRuleType().getType(), a));
//    }
//
//    public static CheckSqlDTO getCheckSql(DataqualityCheckRuleDTO dataqualityCheckRule) {
//        String checkRuleType = dataqualityCheckRule.getCheckRuleType();
//        if (checkRuleType.endsWith("ZDY")) {
//            checkRuleType = CheckRuleTypeEnum.ZDY.getType();
//        }
//        if (checkRuleType.endsWith("WZX")) {
//            checkRuleType = CheckRuleTypeEnum.WZX.getType();
//        }
//        CheckSqlGenarate checkSqlGenarate = checkSqlGenarateMap.get(checkRuleType);
//        if (checkSqlGenarate == null) {
//            throw new BusinessException("规则小类参数异常，没有对应解析类");
//        }
//        return checkSqlGenarate.generateSql(dataqualityCheckRule);
//    }
//}

@Component
public class CheckSqlFactory {
    private static Map<String, CheckSqlGenarate> checkSqlGenarateMap = new ConcurrentHashMap<>();

    @Autowired
    public void setTasksList(CheckSqlGenarate[] checkSqlGenarates) {
        for (CheckSqlGenarate checkSqlGenarate : checkSqlGenarates) {
            checkSqlGenarateMap.put(checkSqlGenarate.gainCheckRuleType().getType(), checkSqlGenarate);
        }
    }

    public static CheckSqlDTO getCheckSql(DataqualityCheckRuleDTO dataqualityCheckRule) {
        String checkRuleType = dataqualityCheckRule.getCheckRuleType();
        if (checkRuleType.startsWith("ZDY")) {
            checkRuleType = CheckRuleTypeEnum.ZDY.getType();
        }
        if (checkRuleType.endsWith("WZX")) {
            checkRuleType = CheckRuleTypeEnum.WZX.getType();
        }
        CheckSqlGenarate checkSqlGenarate = checkSqlGenarateMap.get(checkRuleType);
        if (checkSqlGenarate == null) {
            throw new BusinessException("规则小类参数异常，没有对应解析类");
        }
        if (StrUtil.isNotBlank(dataqualityCheckRule.getCheckRuleWhere())) {
            SqlUtils.checkSql(dataqualityCheckRule.getCheckRuleWhere());
        }
        return checkSqlGenarate.generateSql(dataqualityCheckRule);
    }
}
