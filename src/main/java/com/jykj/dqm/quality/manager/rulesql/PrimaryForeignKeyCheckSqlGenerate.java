package com.jykj.dqm.quality.manager.rulesql;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import org.springframework.stereotype.Component;

/**
 * 一致性（主外键）检核SQL生成
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/22 14:20:45
 */
@Component
public class PrimaryForeignKeyCheckSqlGenerate extends BaseCheckSqlGenerator implements CheckSqlGenarate {
    /*
           SELECT XXX FROM stTable t2
           WHERE NOT EXISTS( SELECT 1 FROM TABLE t1 WHERE t1.XXX= t2.XXX) AND checkRuleWhere
     */
    @Override
    public CheckSqlDTO generateSql(DataqualityCheckRuleDTO dataqualityCheckRule) {
        String table = dataqualityCheckRule.getCheckSchema() + "." + dataqualityCheckRule.getCheckRuleTableOrView();
        String stTable = dataqualityCheckRule.getCheckSchema() + "." + dataqualityCheckRule.getStCheckRuleTableOrView();
        String checkRuleWhere = getWhereStr(dataqualityCheckRule);
        String detailFiled = dataqualityCheckRule.getPbSubsidiaryColumns();
        String checkRuleColumn = dataqualityCheckRule.getCheckRuleColumn();
        String stCheckRuleColumn = dataqualityCheckRule.getStCheckRuleColumn();
        String joinCondition = "";
        if (checkRuleColumn.indexOf(",") != -1) {
            String[] arrayC = checkRuleColumn.split(","); //t1 主
            String[] arraySTC = stCheckRuleColumn.split(","); //t2 副
            if (arrayC.length != arraySTC.length) {
                throw new BusinessException("一致性主外键字段数量不相等，请检查！");
            }
            for (int i = 0; i < arrayC.length; i++) {
                joinCondition += "t1." + arrayC[i] + "= t2." + arraySTC[i] + " AND ";
            }
            joinCondition = joinCondition.substring(0, joinCondition.length() - 4);
        } else {
            joinCondition = "t1." + checkRuleColumn + "= t2." + stCheckRuleColumn;
        }
        //问题明细SQL
        StringBuilder qstDetailFieldSql = new StringBuilder();
        qstDetailFieldSql.append("SELECT " + detailFiled + " FROM " + stTable + " t2");
        qstDetailFieldSql.append(" WHERE NOT EXISTS(");
        qstDetailFieldSql.append(" SELECT 1 FROM " + table + " t1");
        qstDetailFieldSql.append(" WHERE " + joinCondition + ")");
        //添加Where的条件
        if (StrUtil.isNotBlank(checkRuleWhere)) {
            qstDetailFieldSql.append(" AND " + checkRuleWhere);
        }
        CheckSqlDTO checkSqlDTO = addTotalAndQstCheckSql(table, checkRuleWhere, qstDetailFieldSql);
        return checkSqlDTO;
    }

    @Override
    public CheckRuleTypeEnum gainCheckRuleType() {
        return CheckRuleTypeEnum.YZX;
    }
}
