package com.jykj.dqm.quality.manager.rulesql;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.quality.entity.CheckSqlDTO;
import com.jykj.dqm.quality.entity.DataqualityCheckRuleDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 长度规范性检核SQL生成
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/9/22 14:20:45
 */
@Component
public class LengthNormCheckSqlGenerate extends BaseCheckSqlGenerator implements CheckSqlGenarate {
    /*
         select uat_cd,sum from datacontrol.qrtz_locks_uat_p
         where (length(uat_cd) <>3 AND LENGTH(SUBSTRING_INDEX(uat_cd,'.',-1))<>0)
     */
    @Override
    public CheckSqlDTO generateSql(DataqualityCheckRuleDTO dataqualityCheckRule) {
        String table = dataqualityCheckRule.getCheckSchema() + "." + dataqualityCheckRule.getCheckRuleTableOrView();
        String checkRuleWhere = getWhereStr(dataqualityCheckRule);
        String detailFiled = dataqualityCheckRule.getPbSubsidiaryColumns();
        String checkRuleColumn = dataqualityCheckRule.getCheckRuleColumn();
        String dbType = dataqualityCheckRule.getDbType();
        //长度：0等于、1不等于、2大于、3小于、4.大于等于、5小于等于
        String checkObjectType = dataqualityCheckRule.getCheckObjectType();
        //问题明细字段
        StringBuilder qstDetailFieldSql = new StringBuilder();
        qstDetailFieldSql.append("SELECT " + detailFiled + " FROM " + table + " WHERE (" + getLengthSql(checkRuleColumn, dbType) + getTypeByCheckObjectType(checkObjectType) + dataqualityCheckRule.getColumnsLength());
        //小数位数
        String digitalAccuracy = dataqualityCheckRule.getResultSmlNmrlDgit();
        if (!StringUtils.isBlank(digitalAccuracy)) {
            //sql获取小数点后面的字符串长度
            String str = getStrAfterDecimalPointLength(checkRuleColumn, dataqualityCheckRule.getDbType());
            qstDetailFieldSql.append(" AND " + str + getTypeByCheckObjectType(dataqualityCheckRule.getPrcsnExecCondCd()) + digitalAccuracy + ")");
        } else {
            qstDetailFieldSql.append(")");
        }
        if (StrUtil.isNotBlank(checkRuleWhere)) {
            qstDetailFieldSql.append(" AND " + checkRuleWhere);
        }
        CheckSqlDTO checkSqlDTO = addTotalAndQstCheckSql(table, checkRuleWhere, qstDetailFieldSql);
        return checkSqlDTO;
    }

    private String getTypeByCheckObjectType(String checkObjectType) {
        String CHECK_OBJECT_TYPE = "";
        if ("0".equals(checkObjectType)) {
            CHECK_OBJECT_TYPE = " = ";
        } else if ("1".equals(checkObjectType)) {
            CHECK_OBJECT_TYPE = " != ";
        } else if ("2".equals(checkObjectType)) {
            CHECK_OBJECT_TYPE = ">";
        } else if ("3".equals(checkObjectType)) {
            CHECK_OBJECT_TYPE = " < ";
        } else if ("4".equals(checkObjectType)) {
            CHECK_OBJECT_TYPE = " >= ";
        } else if ("5".equals(checkObjectType)) {
            CHECK_OBJECT_TYPE = " <= ";
        }else{
            throw new RuntimeException("判断长度大小参数错误，不是0，1，2，3，4，5请修改");
        }
        return CHECK_OBJECT_TYPE;
    }

    @Override
    public CheckRuleTypeEnum gainCheckRuleType() {
        return CheckRuleTypeEnum.CDGFX;
    }
}
