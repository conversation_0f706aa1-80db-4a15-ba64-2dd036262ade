package com.jykj.dqm.quality.manager.rulesql;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.exception.BusinessException;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statements;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.commons.lang3.StringUtils;

/**
 * Sql工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/9 14:50:55
 */
public class SqlUtils {
    /**
     * 检查SQL是否合法，不合法抛出BusinessException
     *
     * @param sql sql语句
     * <AUTHOR>
     */
    public static void checkSql(String sql) {
        if (StrUtil.isBlank(sql)) {
            throw new BusinessException("不能为空！");
        }
        //sql = sql.replaceAll("\r|\n", " ");
        //sql = sql.replaceAll("(?i)ROW.*?(?=;)", " ");
        try {
            Statements statements = CCJSqlParserUtil.parseStatements(sql);
            statements.getStatements().forEach(statement -> {
                if (!(statement instanceof Select)) {
                    throw new BusinessException("只支持查询语句！");
                }
            });
        } catch (JSQLParserException e) {
            throw new RuntimeException("非法SQL！" + sql + ",", e);
        }
        //sql = sql.toUpperCase(Locale.ENGLISH);
        //if (sql.contains("DELETE ")
        //        || sql.contains("TRUNCATE ")
        //        || sql.contains("UPDATE ")
        //        || sql.contains("INSERT ")
        //        || sql.contains("MODIFY ")
        //        || sql.contains("ALTER ")
        //        || sql.contains("CREATE ")
        //        || sql.contains("DROP ")) {
        //    throw new BusinessException("非法SQL！！！");
        //}
    }

    /**
     * 去掉其中的换行符
     *
     * @param sql sql语句
     * @return normalSql
     * <AUTHOR>
     */
    public static String getSqlNormal(String sql) {
        if (StringUtils.isEmpty(sql)) {
            return sql;
        }
        String sqlTemp = sql.replaceAll("\\n", System.lineSeparator()).replaceAll("\\r\\n", System.lineSeparator());
        return sqlTemp;
    }

    /**
     * 去掉注释内容
     * 单行注释：以两个短横线（--或者 #）开头，直到行尾结束。
     * 多行注释：以 /* 开头，以 x/ 结尾
     *
     * @param sql sql语句
     * @return normalSql
     * <AUTHOR>
     */
    public static String removeComments(String sql) {
        if (StringUtils.isEmpty(sql)) {
            return sql;
        }
        //sql = getSqlNormal(sql);
        String regex = "(?ms)(--.*?$|#.*?$|(?<!:)/\\*.*?\\*/)";
        return sql.replaceAll(regex, "");
    }


    public static void main(String[] args) {
        //System.out.println(removeComments("SELECT t1.DIRECTORY_CODE, t1.DIRECTORY_NAME, t1.EMR_RULE_TYPE, t2.ID, t2.REQUIRE_PROJECT_NAME, t2.UPDATE_TIME, t2.CREATE_TIME, t2.CREATE_BY, t2.UPDATE_BY from dqm_emr_document_directory_configuration t1 left join dqm_emr_require_project_dictionary t2 on t1.DIRECTORY_CODE = t2.DIRECTORY_CODE and t1.DIRECTORY_NAME = t2.DIRECTORY_NAME and t1.EMR_RULE_TYPE = t2.EMR_RULE_TYPE WHERE t1.EMR_RULE_TYPE is not null and length(t1.DIRECTORY_CODE) > 5  -- 嗯嗯嗯\n" +
        //        "ORDER BY t1.DIRECTORY_CODE LIMIT 10 #嗯嗯嗯\n" +
        //        ";"));

        checkSql("select count(Interface_EmrLISResult.名称)\n" +
                "from dbo.Interface_EmrLISResult\n" +
                "left join Cod_DepItem on Interface_EmrLISResult.机器号 = Cod_DepItem.CheckDepId \n" +
                "and Interface_EmrLISResult.项目号 = Cod_DepItem.CheckItemNo\n" +
                "left join Pat_patient on Pat_patient.PatietntId = Interface_EmrLISResult.标本号\n" +
                "where pat_patient.inputdate BETWEEN #startDate AND #endDate\n" +
                "and Pat_patient.PayType = '6'\n" +
                "and Interface_EmrLISResult.名称 is not null");

        checkSql("CREATE TABLE A(B int)");
    }
}
