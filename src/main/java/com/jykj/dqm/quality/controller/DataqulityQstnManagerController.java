package com.jykj.dqm.quality.controller;

import com.jykj.dqm.common.R;
import com.jykj.dqm.quality.entity.DataqulityQstnManagerQueryDTO;
import com.jykj.dqm.quality.entity.DataqulityQstnManagerVO;
import com.jykj.dqm.quality.service.DataqulityQstnManagerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 质量问题管理
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/11/7 11:08:30
 */
@Api(tags = {"质量问题管理"})
@RestController
@RequestMapping("/dataqulityQstnManager")
public class DataqulityQstnManagerController {
    @Autowired
    private DataqulityQstnManagerService dataqulityQstnManagerService;

    /*
       1 关闭接口/处理接口/撤销关闭接口/撤销处理接口 完成
       2 查询列表接口（其中的获取系统+数据库接口重写，表和字段复用之前规则执行界面的） 完成
       3 查看接口（操作记录）完成
       4 查询数据详情（检核的详细数据（Limit 10）） 完成
       5 发送邮箱和短信接口（是处理类型的时候发）
     */
    @ApiOperation(value = "获取所有规则的系统和数据库", notes = "质量问题管理")
    @GetMapping(value = "/getAllSysAndDb")
    public R getAllSysAndDb() {
        return dataqulityQstnManagerService.getAllSysAndDb();
    }

    @ApiOperation(value = "编辑质量问题信息（1关闭，2处理，3撤销关闭，4撤销处理）", notes = "质量问题管理")
    @PostMapping(value = "/editQstnInfo")
    public R editQstnInfo(@RequestBody DataqulityQstnManagerVO dataqulityQstnManager) {
        return dataqulityQstnManagerService.editQstnInfo(dataqulityQstnManager);
    }

    @ApiOperation(value = "查询质量问题数据详情", notes = "质量问题管理")
    @GetMapping(value = "/queryDetailQstnData")
    public R queryDetailQstnData(@RequestParam("dataQltyQstnId") Integer dataQltyQstnId) {
        return dataqulityQstnManagerService.queryDetailQstnData(dataQltyQstnId);
    }

    @ApiOperation(value = "查询质量问题管理列表(包含查看界面的所有参数，其中检测样例数据调这个即接口queryDetailQstnData)", notes = "质量问题管理")
    @PostMapping(value = "/queryList")
    public R queryList(@RequestBody DataqulityQstnManagerQueryDTO dataqulityQstnManagerQueryDTO) {
        return dataqulityQstnManagerService.queryList(dataqulityQstnManagerQueryDTO);
    }
}
