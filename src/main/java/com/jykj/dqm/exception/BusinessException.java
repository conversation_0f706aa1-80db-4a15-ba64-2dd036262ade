/**
 * @Title : BusinessException
 * @Package : com.scjy.mdm.exception
 * @Description :业务异常类
 * <AUTHOR> 黄杰
 * @DateTime : 2021/8/18 19:03
 */
package com.jykj.dqm.exception;


import com.jykj.dqm.common.ExceptionEnum;

/**
 * 业务异常类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
public class BusinessException extends RuntimeException {
    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 异常信息
     */
    private String msg;

    /**
     * 异常码
     */
    private int code = 500;

    /**
     * 异常类有参构造
     *
     * @param msg 异常信息
     */
    public BusinessException(String msg) {
        super(msg);
        this.msg = msg;
    }

    /**
     * 异常类有参构造
     *
     * @param msg 异常信息
     * @param e   Throwable异常
     */
    public BusinessException(String msg, Throwable e) {
        super(msg, e);
        this.msg = msg;
    }

    /**
     * 异常类有参构造
     *
     * @param code 异常编码
     * @param msg  异常信息
     */
    public BusinessException(int code, String msg) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }

    /**
     * 异常类有参构造
     *
     * @param code 异常编码
     * @param msg  异常信息
     * @param e    Throwable 异常
     */
    public BusinessException(int code, String msg, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;
    }

    /**
     * 异常类有参构造
     *
     * @param resultEnum 异常枚举
     */
    public BusinessException(ExceptionEnum resultEnum) {
        super(resultEnum.getMsg());
        this.msg = resultEnum.getMsg();
        this.code = Integer.parseInt(resultEnum.getCode());
    }

    /**
     * 获取异常信息
     *
     * @return 异常信息
     */
    public String getMsg() {
        return msg;
    }

    /**
     * 设置异常信息
     *
     * @param msg 异常信息
     */
    public void setMsg(String msg) {
        this.msg = msg;
    }

    /**
     * 获取异常编码
     *
     * @return 常编码
     */
    public int getCode() {
        return code;
    }

    /**
     * 设置异常编码
     *
     * @param code 常编码
     */
    public void setCode(int code) {
        this.code = code;
    }
}