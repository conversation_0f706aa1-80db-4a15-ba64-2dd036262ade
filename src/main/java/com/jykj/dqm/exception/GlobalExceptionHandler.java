/**
 * @Title : GlobalExceptionHandler
 * @Package : com.scjy.mdm.exception
 * @Description :全局异常
 * <AUTHOR> 黄杰
 * @DateTime : 2021/8/18 19:03
 */
package com.jykj.dqm.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.hutool.core.collection.CollUtil;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.sql.SQLException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2021/8/18 19:03
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    /**
     * 处理业务异常
     *
     * @param e BusinessException异常
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(BusinessException.class)
    public R error(BusinessException e) {
        log.error(e.getMsg(), e);
        return RUtil.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理NoHandlerFoundException异常
     *
     * @param e NoHandlerFoundException
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public R error(NoHandlerFoundException e) {
        log.error(e.getMessage(), e);
        return RUtil.error(404, "路径不存在，请检查路径是否正确");
    }

    /**
     * 处理参数合法性校验异常
     *
     * @param e MethodArgumentNotValidException
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R error(MethodArgumentNotValidException e) {
        List<FieldError> list = e.getBindingResult().getFieldErrors();
        if(CollUtil.isEmpty(list)){
            return RUtil.error(e.getMessage());
        }
        StringBuilder sb = new StringBuilder("参数错误：[");
        for (FieldError item : list) {
            sb.append(item.getField()).append(item.getDefaultMessage()).append(',');
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(']');
        String msg = sb.toString();
        log.error(msg, e);
        return RUtil.error(msg);
    }

    /**
     * 参数绑定异常-类型不匹配
     *
     * @param e BindException
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(BindException.class)
    public R error(BindException e) {
        List<FieldError> list = e.getBindingResult().getFieldErrors();
        if(CollUtil.isEmpty(list)){
            return RUtil.error(e.getMessage());
        }
        StringBuilder sb = new StringBuilder("参数错误：[");
        for (FieldError item : list) {
            sb.append(item.getField()).append(item.getDefaultMessage()).append(',');
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(']');
        String msg = sb.toString();
        log.error(msg, e);
        return RUtil.error(msg);
    }


    /**
     * 处理单个参数校验失败抛出的异常
     *
     * @param e ConstraintViolationException
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public R constraintViolationExceptionHandler(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
        List<String> collect = constraintViolations.stream()
                .map(o -> o.getMessage())
                .collect(Collectors.toList());
        return RUtil.error(collect.toString());
    }

    /**
     * 参数合法性校验异常-类型不匹配
     *
     * @param e MethodArgumentTypeMismatchException
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public R error(MethodArgumentTypeMismatchException e) {
        log.error("参数合法性校验异常-类型不匹配---{}", e.getMessage());
        return RUtil.error(e.getMessage());
    }

    /**
     * Sql语法错误
     *
     * @param e BadSqlGrammarException
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(BadSqlGrammarException.class)
    public R error(BadSqlGrammarException e) {
        log.error(e.getMessage());
        return RUtil.error("sql语法错误:" + StringUtil.getErrorMsg(e));
    }

    /**
     * Sql语法错误
     *
     * @param e SQLException
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(SQLException.class)
    public R error(SQLException e) {
        log.error(e.getMessage(), e);
        return RUtil.error("sql语法错误:" + StringUtil.getErrorMsg(e));
    }

    /**
     * Sql语法错误
     *
     * @param e DataIntegrityViolationException
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public R error(DataIntegrityViolationException e) {
        log.error(e.getMessage(), e);
        return RUtil.error("sql语法错误:" + StringUtil.getErrorMsg(e));
    }

    /**
     * 数据库中已存在该记录错误
     *
     * @param e DuplicateKeyException
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public R error(DuplicateKeyException e) {
        log.error(e.getMessage(), e);
        return RUtil.error("数据库中已存在该记录");
    }

    /**
     * 400 请求参数封装到bean时 类型转换错误
     * 400 (Bad Request)
     *
     * @param ex Exception-》 HttpMessageNotReadableException，MissingServletRequestParameterException
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler({HttpMessageNotReadableException.class, MissingServletRequestParameterException.class})
    @ResponseBody
    public R handleRequestParamFormatError(Exception ex) {
        log.error("handleRequestParamFormatError() bad request ex:{};detail:{}", ex.getLocalizedMessage(), ex);
        return RUtil.error(400, ex.getMessage());
    }

    /**
     * 405 (Method Not Allowed)
     *
     * @param ex HttpRequestMethodNotSupportedException
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseBody
    public R handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        log.error("handleHttpRequestMethodNotSupportedException() request Method Not Allowed(405) exception message:{},detail：{}", ex.getLocalizedMessage(), ex);
        return RUtil.error(405, ex.getLocalizedMessage());
    }

    /**
     * 未登录异常
     *
     * @param e NotLoginException
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(NotLoginException.class)
    public R handleNotLoginException(NotLoginException e) {
        log.error("未登录", e);
        return RUtil.error(e.getLocalizedMessage());
    }

    /**
     * 系统异常
     *
     * @param e Exception
     * @return Result对象
     * <AUTHOR>
     */
    @ExceptionHandler(Exception.class)
    public R error(Exception e) {
        log.error(e.getMessage(), e);
        return RUtil.error(500, "系统异常:" + StringUtil.getErrorMsg(e));
    }
}