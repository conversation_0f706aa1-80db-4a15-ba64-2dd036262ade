package com.deepoove.poi.render;

import cn.hutool.core.map.MapUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.exception.RenderException;
import com.deepoove.poi.policy.DocxRenderPolicy;
import com.deepoove.poi.policy.RenderPolicy;
import com.deepoove.poi.render.compute.RenderDataCompute;
import com.deepoove.poi.render.processor.DelegatePolicy;
import com.deepoove.poi.render.processor.DocumentProcessor;
import com.deepoove.poi.render.processor.LogProcessor;
import com.deepoove.poi.template.MetaTemplate;
import com.deepoove.poi.template.run.RunTemplate;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.jykj.dqm.emr.config.TransmittableThreadLocalManager;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * default render
 *
 * <AUTHOR>
 * @since 1.7.0
 */
public class DefaultRender implements Render {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultRender.class);

    private String exportRecordId;

    private String token;

    public DefaultRender() {
    }

    @Override
    public void render(XWPFTemplate template, Object root) {
        Objects.requireNonNull(template, "Template must not be null.");
        Objects.requireNonNull(root, "Data root must not be null");

        LOGGER.info("Render template start...");

        Map<String, Object> map = (Map<String, Object>) root;
        exportRecordId = map.get("exportRecordId") == null ? null : map.get("exportRecordId").toString();
        token = map.get("token") == null ? null : map.get("token").toString();
        RenderDataCompute renderDataCompute = template.getConfig().getRenderDataComputeFactory().newCompute(root);
        StopWatch watch = new StopWatch();
        try {

            watch.start();
            renderTemplate(template, renderDataCompute);
            renderInclude(template, renderDataCompute);

        } catch (Exception e) {
            if (e instanceof RenderException) throw (RenderException) e;
            throw new RenderException("Cannot render docx template", e);
        } finally {
            watch.stop();
        }
        LOGGER.info("Successfully Render template in {} millis", TimeUnit.NANOSECONDS.toMillis(watch.getNanoTime()));
    }

    private void renderTemplate(XWPFTemplate template, RenderDataCompute renderDataCompute) {
        // log
        new LogProcessor().process(template.getElementTemplates());

        // render
        DocumentProcessor documentRender = new DocumentProcessor(template, template.getResolver(), renderDataCompute);
        documentRender.process(template.getElementTemplates());
    }

    private void renderInclude(XWPFTemplate template, RenderDataCompute renderDataCompute) throws IOException {
        List<MetaTemplate> elementTemplates = template.getElementTemplates();
        long docxCount = elementTemplates.stream()
                .filter(meta -> (meta instanceof RunTemplate
                        && ((RunTemplate) meta).findPolicy(template.getConfig()) instanceof DocxRenderPolicy))
                .count();
        if (docxCount >= 1) {
            template.reload(template.getXWPFDocument().generate());
            applyDocxPolicy(template, renderDataCompute, docxCount);
        }
    }

    //修改当前方法，实现可以看到嵌套的进度
    private void applyDocxPolicy(XWPFTemplate template, RenderDataCompute renderDataCompute, long docxItems) {
        RenderPolicy policy = null;
        NiceXWPFDocument current = template.getXWPFDocument();
        List<MetaTemplate> elementTemplates = template.getElementTemplates();
        int k = 0;
        while (k < elementTemplates.size()) {
            for (int j = 0; j < elementTemplates.size(); k = ++j) {
                MetaTemplate metaTemplate = elementTemplates.get(j);
                if (!(metaTemplate instanceof RunTemplate)) continue;
                RunTemplate runTemplate = (RunTemplate) metaTemplate;
                policy = runTemplate.findPolicy(template.getConfig());
                if (!(policy instanceof DocxRenderPolicy)) {
                    continue;
                }
                DelegatePolicy.invoke(policy, runTemplate, renderDataCompute.compute(runTemplate.getTagName()), template);
                //System.out.println("-------------------------------------------"+TransmittableThreadLocalManager.get());
                if (exportRecordId != null && token != null) {
                    //记录进度
                    TransmittableThreadLocalManager.processMap.put(token, MapUtil.builder(new HashMap<String, Object>()).put(exportRecordId, j * 1.0 / (elementTemplates.size() + 1)).build());
                }
                if (current != template.getXWPFDocument()) {
                    current = template.getXWPFDocument();
                    elementTemplates = template.getElementTemplates();
                    k = 0;
                    break;
                }
            }
        }
    }

}