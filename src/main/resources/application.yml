spring:
  jackson:
    date-format: "yyyy-MM-dd HH:mm:ss"
    time-zone: GMT+8
  profiles:
    #公司测试环境
    #active: testmysql124
    #公司本地环境
    #active: testmysqllocal
    #公司135环境
    active: testmysql135
    #公司本地环境
    #active: gymysql
    #公司测试环境
    #active: testoracle
    #8院正式环境
    #active: 8oracle
    #广元中医院
    #active: gyzyymysql
    #active:
      #- @profiles.active@

# 开启Gzip压缩，默认只压缩超过2048字节的数据
server:
  compression:
    enabled: true
    mime-types: application/json,application/javascript,image/png,text/css,text/html,application/octet-stream

system:
  enable:
    emrm: true

# 加密算法配置
crypto:
  hash-algorithm: SM3  # 可选值: SM3, MD5 - 用于哈希加密（如密码哈希）
  symmetric-algorithm: SM4  # 可选值: SM4, AES - 用于对称加密（如数据加密）

# 跨库查询配置
cross-query:
  auto-catalog-config: false  # 是否开启自动配置跨库catalog，默认关闭
  presto-docker-path: /home/<USER>
  presto-env-password: Jykj1994@  # Presto环境密码，用于加密env.encrypted文件