<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.system.dao.OperLogMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.common.OperLog">
        <result column="ID" jdbcType="INTEGER" property="id"/>
        <result column="USER_ID" jdbcType="VARCHAR" property="userId"/>
        <result column="USER_NAME" jdbcType="VARCHAR" property="userName"/>
        <result column="FUNCTION_MODULE" jdbcType="VARCHAR" property="functionModule"/>
        <result column="OPERATE_CONTENT" jdbcType="VARCHAR" property="operateContent"/>
        <result column="OPERATE_TIME" jdbcType="TIMESTAMP" property="operateTime"/>
    </resultMap>
    <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.jykj.dqm.common.OperLog"
            useGeneratedKeys="true">
        insert into DQM_OPER_LOG (USER_ID, USER_NAME, FUNCTION_MODULE,
                                  OPERATE_CONTENT, OPERATE_TIME)
        values (#{userId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{functionModule,jdbcType=VARCHAR},
                #{operateContent,jdbcType=VARCHAR}, #{operateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.jykj.dqm.common.OperLog"
            useGeneratedKeys="true">
        insert into DQM_OPER_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="userName != null">
                USER_NAME,
            </if>
            <if test="functionModule != null">
                FUNCTION_MODULE,
            </if>
            <if test="operateContent != null">
                OPERATE_CONTENT,
            </if>
            <if test="operateTime != null">
                OPERATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="functionModule != null">
                #{functionModule,jdbcType=VARCHAR},
            </if>
            <if test="operateContent != null">
                #{operateContent,jdbcType=VARCHAR},
            </if>
            <if test="operateTime != null">
                #{operateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <delete id="clearLog">
        DELETE
        FROM DQM_OPER_LOG
        WHERE OPERATE_TIME <![CDATA[ <= ]]> #{date}
    </delete>
    <select id="queryLog" resultType="com.jykj.dqm.common.OperLog"  databaseId="mysql">
        SELECT *
        from DQM_OPER_LOG WHERE 1 = 1
        <if test="startTime != null and startTime != ''">
            AND OPERATE_TIME <![CDATA[ >= ]]> str_to_date(#{startTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="endTime != null and endTime != ''">
            AND OPERATE_TIME <![CDATA[ <= ]]> str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="userId != null and userId != ''">
            AND USER_ID LIKE CONCAT('%', #{userId}, '%')
        </if>
        <if test="username != null and username != ''">
            AND USER_NAME LIKE CONCAT('%', #{username}, '%')
        </if>
        ORDER BY OPERATE_TIME DESC
    </select>

    <select id="queryLog" resultType="com.jykj.dqm.common.OperLog" databaseId="oracle">
        SELECT *
        from DQM_OPER_LOG WHERE 1 = 1
        <if test="startTime != null and startTime != ''">
            AND OPERATE_TIME <![CDATA[ >= ]]> str_to_date(#{startTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="endTime != null and endTime != ''">
            AND OPERATE_TIME <![CDATA[ <= ]]> str_to_date(#{endTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="userId != null and userId != ''">
            AND USER_ID like '%'|| #{userId} ||'%'
        </if>
        <if test="username != null and username != ''">
            AND USER_NAME like '%'|| #{username} ||'%'
        </if>
        ORDER BY OPERATE_TIME DESC
    </select>
</mapper>