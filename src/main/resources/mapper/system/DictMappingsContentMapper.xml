<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.system.dao.DictMappingsContentMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.system.entity.DictMappingsContent">
        <!--@mbg.generated-->
        <!--@Table DQM_DICT_MAPPINGS_CONTENT-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="TERM_ID" jdbcType="VARCHAR" property="termId"/>
        <result column="MAPPINGS_TYPE" jdbcType="VARCHAR" property="mappingsType"/>
        <result column="SOURCE_FIELD" jdbcType="VARCHAR" property="sourceField"/>
        <result column="SOURCE_FIELD_CODE" jdbcType="VARCHAR" property="sourceFieldCode"/>
        <result column="TARGET_FIELD" jdbcType="VARCHAR" property="targetField"/>
        <result column="TARGET_FIELD_CODE" jdbcType="VARCHAR" property="targetFieldCode"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="DELETED_FLAG" jdbcType="VARCHAR" property="deletedFlag"/>
        <result column="ISMATCH" jdbcType="VARCHAR" property="ismatch"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        TERM_ID,
        MAPPINGS_TYPE,
        SOURCE_FIELD,
        SOURCE_FIELD_CODE,
        TARGET_FIELD,
        TARGET_FIELD_CODE,
        CREATED_BY,
        CREATED_TIME,
        UPDATED_BY,
        UPDATED_TIME,
        DELETED_FLAG,
        ISMATCH
    </sql>

    <select id="getTermDictData" resultType="java.lang.String">
        SELECT DQM_DICT_MAPPINGS_CONTENT.${stCheckRuleColumn} FROM DQM_DICT_MAPPINGS
        LEFT JOIN DQM_DICT_MAPPINGS_CONTENT ON DQM_DICT_MAPPINGS.TERM_ID=DQM_DICT_MAPPINGS_CONTENT.TERM_ID AND DQM_DICT_MAPPINGS.MAPPINGS_TYPE=DQM_DICT_MAPPINGS_CONTENT.MAPPINGS_TYPE
        WHERE DQM_DICT_MAPPINGS.MAPPINGS_TABLE=#{stTable} AND DQM_DICT_MAPPINGS.MAPPINGS_TYPE=#{cdVal} AND DQM_DICT_MAPPINGS_CONTENT.DELETED_FLAG='0'
    </select>
</mapper>