<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jykj.dqm.auth.dao.SysRoleMapper">

    <resultMap id="BaseResultMap" type="com.jykj.dqm.auth.entity.SysRole">
        <result column="GROUP_CODE" property="groupCode" jdbcType="VARCHAR"/>
        <result column="ROLE_ID" property="roleId" jdbcType="VARCHAR"/>
        <result column="ROLE_NAME" property="roleName" jdbcType="VARCHAR"/>
        <result column="SYS_ID" property="sysId" jdbcType="VARCHAR"/>
        <result column="DESCRIPTION" property="description" jdbcType="VARCHAR"/>
        <result column="ORGANIZATION_CODE" property="organizationCode" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="addSysRole" parameterType="com.jykj.dqm.auth.entity.SysRole" useGeneratedKeys="true" keyProperty="roleId" databaseId="mysql">
        insert  into SYS_ROLE
        (GROUP_CODE,ROLE_NAME,SYS_ID,DESCRIPTION,ORGANIZATION_CODE)
        VALUES (
        #{sysRole.groupCode,jdbcType=VARCHAR},
        #{sysRole.roleName,jdbcType=VARCHAR},
        #{sysRole.sysId,jdbcType=VARCHAR},
        #{sysRole.description,jdbcType=VARCHAR},
        #{sysRole.organizationCode,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="addSysRole" parameterType="com.jykj.dqm.auth.entity.SysRole" databaseId="oracle">
        <selectKey resultType="java.lang.String" order="BEFORE" keyProperty="roleId">
            SELECT  SEQ_SYS_ROLE.nextval as roleId from dual
        </selectKey>
        insert  into SYS_ROLE
        (GROUP_CODE,ROLE_ID,ROLE_NAME,SYS_ID,DESCRIPTION,ORGANIZATION_CODE)
        VALUES (
        #{sysRole.groupCode,jdbcType=VARCHAR},
        #{roleId},
        #{sysRole.roleName,jdbcType=VARCHAR},
        #{sysRole.sysId,jdbcType=VARCHAR},
        #{sysRole.description,jdbcType=VARCHAR},
        #{sysRole.organizationCode,jdbcType=VARCHAR}
        )
    </insert>

    <select id="getRoleId" resultType="java.lang.String">
        SELECT SEQ_SYS_ROLE.nextval as roleId from dual
    </select>

   <update id="updateByRoleId" parameterType="com.jykj.dqm.auth.entity.SysRole">
       update SYS_ROLE
       set
       GROUP_CODE = #{sysRole.groupCode},
       ORGANIZATION_CODE = #{sysRole.organizationCode},
       ROLE_NAME = #{sysRole.roleName},
       SYS_ID = #{sysRole.sysId},
       DESCRIPTION = #{sysRole.description}
       where
       ROLE_ID = #{sysRole.roleId} AND SYS_ID = 'DQM'
   </update>

    <select id="querylist" resultMap="BaseResultMap">
        select  * from SYS_ROLE where SYS_ID = 'DQM'
    </select>

    <delete id="deleteByRoleId" parameterType="java.lang.Integer" >
        delete from SYS_ROLE WHERE ROLE_ID = #{roleId,jdbcType=NUMERIC} AND SYS_ID = 'DQM'
    </delete>
</mapper>