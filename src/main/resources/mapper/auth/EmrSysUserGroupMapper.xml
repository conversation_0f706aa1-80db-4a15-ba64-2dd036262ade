<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jykj.dqm.auth.dao.EmrSysUserGroupMapper">
    <select id="queryByGroupName" resultType="com.jykj.dqm.auth.entity.EmrSysUserGroup">
        SELECT SG.*,SU.user_name,SU.gender FROM DQM_EMR_SYS_USER_GROUP SG
        LEFT JOIN SYS_USER SU ON SG.LOGIN_ID =SU.LOGIN_ID
        WHERE SU.SYS_ID ='DQM'
        <if test="groupName != null and groupName != ''">
            AND SG.GROUP_NAME = #{groupName}
        </if>
        <if test="userAccount != null and userAccount != ''">
            AND SG.LOGIN_ID = #{userAccount}
        </if>
        <if test="projectId != null and projectId != ''">
            AND SG.PROJECT_ID = #{projectId}
        </if>
    </select>
</mapper>
