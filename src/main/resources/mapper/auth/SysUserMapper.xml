<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jykj.dqm.auth.dao.SysUserMapper">

    <resultMap id="BaseResultMap" type="com.jykj.dqm.auth.entity.SysUser">
        <result column="GROUP_CODE" property="groupCode" jdbcType="VARCHAR"/>
        <result column="USER_ID" property="userId" jdbcType="VARCHAR"/>
        <result column="LOGIN_ID" property="loginId" jdbcType="VARCHAR"/>
        <result column="PASSWORD" property="password" jdbcType="VARCHAR"/>
        <result column="PASSWORD_EXPIRATION_DATE" property="passwordExpirationDate" jdbcType="VARCHAR"/>
        <result column="PASSWORD_RESET" property="passwordReset" jdbcType="VARCHAR"/>
        <result column="LOGIN_IP" property="loginIp" jdbcType="VARCHAR"/>
        <result column="LAST_LOGIN_IP" property="lastLoginIp" jdbcType="VARCHAR"/>
        <result column="LAST_LOGIN_DATE" property="lastLoginDate" jdbcType="VARCHAR"/>
        <result column="FAILED_LOGIN_ATTEMPTS" property="failedLoginAttempts" jdbcType="VARCHAR"/>
        <result column="USER_STATUS" property="userStatus" jdbcType="VARCHAR"/>
        <result column="MODIFIED_DATE" property="modifiedDate" jdbcType="VARCHAR"/>
        <result column="EMPLOYMENT_ID" property="employmentId" jdbcType="VARCHAR"/>
        <result column="ORGANIZATION_CODE" property="organizationCode" jdbcType="VARCHAR"/>
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="SYS_ID" property="sysId" jdbcType="VARCHAR"/>
        <result column="GENDER" property="gender" jdbcType="VARCHAR"/>
        <result column="ID_CARD" property="idCard" jdbcType="VARCHAR"/>
        <result column="IDENTIFY_TYPE" property="identifyType" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_ACTIVATION_DATE" property="accountActivationDate" jdbcType="TIMESTAMP"/>
        <result column="ACCOUNT_EXPIRATION_DATE" property="accountExpirationDate" jdbcType="TIMESTAMP"/>
        <result column="TOKEN_EXPIRATION_TIME" property="tokenExpirationTime" jdbcType="INTEGER"/>
    </resultMap>

    <insert id="addSysUser" parameterType="com.jykj.dqm.auth.entity.SysUser" useGeneratedKeys="true" keyProperty="userId" databaseId="mysql">
        insert into SYS_USER
        (GROUP_CODE,LOGIN_ID,PASSWORD,PASSWORD_EXPIRATION_DATE,PASSWORD_RESET,LOGIN_IP
        ,LAST_LOGIN_IP,FAILED_LOGIN_ATTEMPTS,USER_STATUS,
        MODIFIED_DATE,EMPLOYMENT_ID,ORGANIZATION_CODE,USER_NAME,MOBILE,CREATE_TIME,EMAIL,SYS_ID
        <if test="sysUser.gender!=null">
            ,GENDER
        </if>
        <if test="sysUser.idCard!=null">
            ,ID_CARD
        </if>
        <if test="sysUser.identifyType!=null">
            ,IDENTIFY_TYPE
        </if>
        <if test="sysUser.accountActivationDate!=null">
            ,ACCOUNT_ACTIVATION_DATE
        </if>
        <if test="sysUser.accountExpirationDate!=null">
            ,ACCOUNT_EXPIRATION_DATE
        </if>
        <if test="sysUser.tokenExpirationTime!=null">
            ,TOKEN_EXPIRATION_TIME
        </if>
        )
        VALUES (
        #{sysUser.groupCode,jdbcType=NUMERIC},
        #{sysUser.loginId,jdbcType=VARCHAR},
        #{sysUser.password,jdbcType=VARCHAR},
        str_to_date(#{sysUser.passwordExpirationDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'),
        #{sysUser.passwordReset,jdbcType=VARCHAR},
        #{sysUser.loginIp,jdbcType=NUMERIC},
        #{sysUser.lastLoginIp,jdbcType=VARCHAR},
        #{sysUser.failedLoginAttempts,jdbcType=VARCHAR},
        #{sysUser.userStatus,jdbcType=VARCHAR},
        str_to_date( #{sysUser.modifiedDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'),
        #{sysUser.employmentId,jdbcType=VARCHAR} ,
        #{sysUser.organizationCode,jdbcType=NUMERIC},
        #{sysUser.userName,jdbcType=VARCHAR},
        #{sysUser.mobile,jdbcType=VARCHAR},
        str_to_date( #{sysUser.createTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'),
        #{sysUser.email,jdbcType=VARCHAR},
        #{sysUser.sysId,jdbcType=VARCHAR}
        <if test="sysUser.gender!=null">
            ,#{sysUser.gender,jdbcType=VARCHAR}
        </if>
        <if test="sysUser.idCard!=null">
            ,#{sysUser.idCard,jdbcType=VARCHAR}
        </if>
        <if test="sysUser.identifyType!=null">
            ,#{sysUser.identifyType,jdbcType=VARCHAR}
        </if>
        <if test="sysUser.accountActivationDate!=null">
            ,#{sysUser.accountActivationDate,jdbcType=TIMESTAMP}
        </if>
        <if test="sysUser.accountExpirationDate!=null">
            ,#{sysUser.accountExpirationDate,jdbcType=TIMESTAMP}
        </if>
        <if test="sysUser.tokenExpirationTime!=null">
            ,#{sysUser.tokenExpirationTime,jdbcType=INTEGER}
        </if>
        )
    </insert>

    <insert id="addSysUser" parameterType="com.jykj.dqm.auth.entity.SysUser" databaseId="oracle">
        <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="userId">
             SELECT SEQ_SYS_USER.nextval as userId from dual
         </selectKey>
        insert into SYS_USER
        (GROUP_CODE,USER_ID,LOGIN_ID,PASSWORD,PASSWORD_EXPIRATION_DATE,PASSWORD_RESET,LOGIN_IP
        ,LAST_LOGIN_IP,FAILED_LOGIN_ATTEMPTS,USER_STATUS,
        MODIFIED_DATE,EMPLOYMENT_ID,ORGANIZATION_CODE,USER_NAME,MOBILE,CREATE_TIME,EMAIL,SYS_ID
        <if test="sysUser.gender!=null">
            ,GENDER
        </if>
        <if test="sysUser.idCard!=null">
            ,ID_CARD
        </if>
        <if test="sysUser.identifyType!=null">
            ,IDENTIFY_TYPE
        </if>
        <if test="sysUser.accountActivationDate!=null">
            ,ACCOUNT_ACTIVATION_DATE
        </if>
        <if test="sysUser.accountExpirationDate!=null">
            ,ACCOUNT_EXPIRATION_DATE
        </if>
        <if test="sysUser.tokenExpirationTime!=null">
            ,TOKEN_EXPIRATION_TIME
        </if>
        )
        VALUES (
        #{sysUser.groupCode,jdbcType=NUMERIC},
        #{userId,jdbcType=VARCHAR},
        #{sysUser.loginId,jdbcType=VARCHAR},
        #{sysUser.password,jdbcType=VARCHAR},
        to_date(#{sysUser.passwordExpirationDate,jdbcType=VARCHAR},'yyyy/mm/dd hh24:mi:ss'),
        #{sysUser.passwordReset,jdbcType=VARCHAR},
        #{sysUser.loginIp,jdbcType=NUMERIC},
        #{sysUser.lastLoginIp,jdbcType=VARCHAR},
        #{sysUser.failedLoginAttempts,jdbcType=VARCHAR},
        #{sysUser.userStatus,jdbcType=VARCHAR},
        to_date( #{sysUser.modifiedDate,jdbcType=VARCHAR},'yyyy/mm/dd hh24:mi:ss'),
        #{sysUser.employmentId,jdbcType=VARCHAR} ,
        #{sysUser.organizationCode,jdbcType=NUMERIC},
        #{sysUser.userName,jdbcType=VARCHAR},
        #{sysUser.mobile,jdbcType=VARCHAR},
        to_date( #{sysUser.createTime,jdbcType=VARCHAR},'yyyy/mm/dd hh24:mi:ss'),
        #{sysUser.email,jdbcType=VARCHAR},
        #{sysUser.sysId,jdbcType=VARCHAR}
        <if test="sysUser.gender!=null">
            ,#{sysUser.gender,jdbcType=VARCHAR}
        </if>
        <if test="sysUser.idCard!=null">
            ,#{sysUser.idCard,jdbcType=VARCHAR}
        </if>
        <if test="sysUser.identifyType!=null">
            ,#{sysUser.identifyType,jdbcType=VARCHAR}
        </if>
        <if test="sysUser.accountActivationDate!=null">
            ,#{sysUser.accountActivationDate,jdbcType=TIMESTAMP}
        </if>
        <if test="sysUser.accountExpirationDate!=null">
            ,#{sysUser.accountExpirationDate,jdbcType=TIMESTAMP}
        </if>
        <if test="sysUser.tokenExpirationTime!=null">
            ,#{sysUser.tokenExpirationTime,jdbcType=INTEGER}
        </if>
        )
    </insert>

    <insert id="addSysUserWithoutPasswordExpirationDate" parameterType="com.jykj.dqm.auth.entity.SysUser"
            useGeneratedKeys="true" keyProperty="userId" databaseId="mysql">
        insert into SYS_USER
        (GROUP_CODE,LOGIN_ID,PASSWORD,PASSWORD_RESET,LOGIN_IP
        ,LAST_LOGIN_IP,FAILED_LOGIN_ATTEMPTS,USER_STATUS,
        MODIFIED_DATE,EMPLOYMENT_ID,ORGANIZATION_CODE,USER_NAME,MOBILE,CREATE_TIME,EMAIL,SYS_ID
        <if test="sysUser.gender!=null">
            ,GENDER
        </if>
        <if test="sysUser.idCard!=null">
            ,ID_CARD
        </if>
        <if test="sysUser.identifyType!=null">
            ,IDENTIFY_TYPE
        </if>
        <if test="sysUser.accountActivationDate!=null">
            ,ACCOUNT_ACTIVATION_DATE
        </if>
        <if test="sysUser.accountExpirationDate!=null">
            ,ACCOUNT_EXPIRATION_DATE
        </if>
        <if test="sysUser.tokenExpirationTime!=null">
            ,TOKEN_EXPIRATION_TIME
        </if>
        )
        VALUES (
        #{sysUser.groupCode,jdbcType=VARCHAR},
        #{sysUser.loginId,jdbcType=VARCHAR},
        #{sysUser.password,jdbcType=VARCHAR},
        #{sysUser.passwordReset,jdbcType=VARCHAR},
        #{sysUser.loginIp,jdbcType=NUMERIC},
        #{sysUser.lastLoginIp,jdbcType=VARCHAR},
        #{sysUser.failedLoginAttempts,jdbcType=VARCHAR},
        #{sysUser.userStatus,jdbcType=VARCHAR},
        str_to_date( #{sysUser.modifiedDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'),
        #{sysUser.employmentId,jdbcType=VARCHAR} ,
        #{sysUser.organizationCode,jdbcType=VARCHAR},
        #{sysUser.userName,jdbcType=VARCHAR},
        #{sysUser.mobile,jdbcType=VARCHAR},
        str_to_date( #{sysUser.createTime,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'),
        #{sysUser.email,jdbcType=VARCHAR},
        #{sysUser.sysId,jdbcType=VARCHAR}
        <if test="sysUser.gender!=null">
            ,#{sysUser.gender,jdbcType=VARCHAR}
        </if>
        <if test="sysUser.idCard!=null">
            ,#{sysUser.idCard,jdbcType=VARCHAR}
        </if>
        <if test="sysUser.identifyType!=null">
            ,#{sysUser.identifyType,jdbcType=VARCHAR}
        </if>
        <if test="sysUser.accountActivationDate!=null">
            ,#{sysUser.accountActivationDate,jdbcType=TIMESTAMP}
        </if>
        <if test="sysUser.accountExpirationDate!=null">
            ,#{sysUser.accountExpirationDate,jdbcType=TIMESTAMP}
        </if>
        <if test="sysUser.tokenExpirationTime!=null">
            ,#{sysUser.tokenExpirationTime,jdbcType=INTEGER}
        </if>
        )
    </insert>

    <insert id="addSysUserWithoutPasswordExpirationDate" parameterType="com.jykj.dqm.auth.entity.SysUser" databaseId="oracle">
         <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="userId">
              SELECT SEQ_SYS_USER.nextval as userId from dual
          </selectKey>
        insert into SYS_USER
        (GROUP_CODE,USER_ID,LOGIN_ID,PASSWORD,PASSWORD_RESET,LOGIN_IP
        ,LAST_LOGIN_IP,FAILED_LOGIN_ATTEMPTS,USER_STATUS,
        MODIFIED_DATE,EMPLOYMENT_ID,ORGANIZATION_CODE,USER_NAME,MOBILE,CREATE_TIME,EMAIL,SYS_ID
        <if test="sysUser.gender!=null">
            ,GENDER
        </if>
        <if test="sysUser.idCard!=null">
            ,ID_CARD
        </if>
        <if test="sysUser.identifyType!=null">
            ,IDENTIFY_TYPE
        </if>
        <if test="sysUser.accountActivationDate!=null">
            ,ACCOUNT_ACTIVATION_DATE
        </if>
        <if test="sysUser.accountExpirationDate!=null">
            ,ACCOUNT_EXPIRATION_DATE
        </if>
        <if test="sysUser.tokenExpirationTime!=null">
            ,TOKEN_EXPIRATION_TIME
        </if>
        )
        VALUES (
        #{sysUser.groupCode,jdbcType=VARCHAR},
        #{userId},
        #{sysUser.loginId,jdbcType=VARCHAR},
        #{sysUser.password,jdbcType=VARCHAR},
        #{sysUser.passwordReset,jdbcType=VARCHAR},
        #{sysUser.loginIp,jdbcType=NUMERIC},
        #{sysUser.lastLoginIp,jdbcType=VARCHAR},
        #{sysUser.failedLoginAttempts,jdbcType=VARCHAR},
        #{sysUser.userStatus,jdbcType=VARCHAR},
        to_date( #{sysUser.modifiedDate,jdbcType=VARCHAR},'yyyy/mm/dd hh24:mi:ss'),
        #{sysUser.employmentId,jdbcType=VARCHAR} ,
        #{sysUser.organizationCode,jdbcType=VARCHAR},
        #{sysUser.userName,jdbcType=VARCHAR},
        #{sysUser.mobile,jdbcType=VARCHAR},
        to_date( #{sysUser.createTime,jdbcType=VARCHAR},'yyyy/mm/dd hh24:mi:ss'),
        #{sysUser.email,jdbcType=VARCHAR},
        #{sysUser.sysId,jdbcType=VARCHAR}
        <if test="sysUser.gender!=null">
            ,#{sysUser.gender,jdbcType=VARCHAR}
        </if>
        <if test="sysUser.idCard!=null">
            ,#{sysUser.idCard,jdbcType=VARCHAR}
        </if>
        <if test="sysUser.identifyType!=null">
            ,#{sysUser.identifyType,jdbcType=VARCHAR}
        </if>
        <if test="sysUser.accountActivationDate!=null">
            ,#{sysUser.accountActivationDate,jdbcType=TIMESTAMP}
        </if>
        <if test="sysUser.accountExpirationDate!=null">
            ,#{sysUser.accountExpirationDate,jdbcType=TIMESTAMP}
        </if>
        <if test="sysUser.tokenExpirationTime!=null">
            ,#{sysUser.tokenExpirationTime,jdbcType=INTEGER}
        </if>
        )
    </insert>

    <update id="updateUserStatus" parameterType="com.jykj.dqm.auth.entity.SysUser" databaseId="mysql">
        update SYS_USER
        set USER_STATUS= #{sysUser.userStatus},
        MODIFIED_DATE = str_to_date(#{sysUser.modifiedDate,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%s')
        where USER_ID = #{sysUser.userId}
        AND SYS_ID = 'DQM'
    </update>

    <update id="updateUserStatus" parameterType="com.jykj.dqm.auth.entity.SysUser" databaseId="oracle">
        update SYS_USER
        set USER_STATUS= #{sysUser.userStatus},
        MODIFIED_DATE = to_date(#{sysUser.modifiedDate,jdbcType=VARCHAR}, 'yyyy/mm/dd hh24:mi:ss')
        where USER_ID = #{sysUser.userId}
        AND SYS_ID = 'DQM'
    </update>

    <update id="updateByUserId" parameterType="com.jykj.dqm.auth.entity.SysUser" databaseId="mysql">
        update SYS_USER
        set
        GROUP_CODE = #{sysUser.groupCode},LOGIN_ID = #{sysUser.loginId},
        <if test="sysUser.password != null and sysUser.password !=''">
            PASSWORD = #{sysUser.password},
        </if>
        <if test="sysUser.passwordReset != null and sysUser.passwordReset !=''">
            PASSWORD_RESET = #{sysUser.passwordReset},
        </if>
        LOGIN_IP = #{sysUser.loginIp},
        LAST_LOGIN_IP = #{sysUser.lastLoginIp},
        FAILED_LOGIN_ATTEMPTS = #{sysUser.failedLoginAttempts},
        <if test="sysUser.userName != null and sysUser.userName !=''">
            USER_NAME = #{sysUser.userName},
        </if>
        <if test="sysUser.mobile != null and sysUser.mobile !=''">
            MOBILE =#{sysUser.mobile},
        </if>
        <if test="sysUser.email != null and sysUser.email !=''">
            EMAIL = #{sysUser.email},
        </if>
        <if test="sysUser.modifiedDate != null and sysUser.modifiedDate !=''">
            MODIFIED_DATE = str_to_date(#{sysUser.modifiedDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'),
        </if>
        <if test="sysUser.employmentId != null and sysUser.employmentId !=''">
            EMPLOYMENT_ID = #{sysUser.employmentId},
        </if>
        <if test="sysUser.organizationCode != null and sysUser.organizationCode !=''">
            ORGANIZATION_CODE = #{sysUser.organizationCode},
        </if>
        <if test="sysUser.passwordExpirationDate != null and sysUser.passwordExpirationDate !=''">
            PASSWORD_EXPIRATION_DATE = str_to_date(#{sysUser.passwordExpirationDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'),
        </if>
        <if test="sysUser.lastLoginDate != null and sysUser.lastLoginDate !=''">
            LAST_LOGIN_DATE = str_to_date(#{sysUser.lastLoginDate,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%s'),
        </if>
        <if test="sysUser.gender != null">
            GENDER=#{sysUser.gender,jdbcType=VARCHAR},
        </if>
        <if test="sysUser.idCard != null">
            ID_CARD=#{sysUser.idCard,jdbcType=VARCHAR},
        </if>
        <if test="sysUser.identifyType!=null">
            IDENTIFY_TYPE=#{sysUser.identifyType,jdbcType=VARCHAR},
        </if>
        <if test="sysUser.accountActivationDate!=null">
            ACCOUNT_ACTIVATION_DATE=#{sysUser.accountActivationDate,jdbcType=TIMESTAMP},
        </if>
        <if test="sysUser.accountExpirationDate!=null">
            ACCOUNT_EXPIRATION_DATE=#{sysUser.accountExpirationDate,jdbcType=TIMESTAMP},
        </if>
        <if test="sysUser.tokenExpirationTime!=null">
            TOKEN_EXPIRATION_TIME=#{sysUser.tokenExpirationTime,jdbcType=INTEGER},
        </if>
        USER_STATUS = #{sysUser.userStatus}
        where
        USER_ID = #{sysUser.userId} AND SYS_ID = 'DQM'
    </update>

    <update id="updateByUserId" parameterType="com.jykj.dqm.auth.entity.SysUser" databaseId="oracle">
        update SYS_USER
        set
        GROUP_CODE = #{sysUser.groupCode},LOGIN_ID = #{sysUser.loginId},
        <if test="sysUser.password != null and sysUser.password !=''">
            PASSWORD = #{sysUser.password},
        </if>
        <if test="sysUser.passwordReset != null and sysUser.passwordReset !=''">
            PASSWORD_RESET = #{sysUser.passwordReset},
        </if>
        LOGIN_IP = #{sysUser.loginIp},
        LAST_LOGIN_IP = #{sysUser.lastLoginIp},
        FAILED_LOGIN_ATTEMPTS = #{sysUser.failedLoginAttempts},
        <if test="sysUser.userName != null and sysUser.userName !=''">
            USER_NAME = #{sysUser.userName},
        </if>
        <if test="sysUser.mobile != null and sysUser.mobile !=''">
            MOBILE =#{sysUser.mobile},
        </if>
        <if test="sysUser.email != null and sysUser.email !=''">
            EMAIL = #{sysUser.email},
        </if>
        <if test="sysUser.modifiedDate != null and sysUser.modifiedDate !=''">
            MODIFIED_DATE = to_date(#{sysUser.modifiedDate,jdbcType=VARCHAR},'yyyy/mm/dd hh24:mi:ss'),
        </if>
        <if test="sysUser.employmentId != null and sysUser.employmentId !=''">
            EMPLOYMENT_ID = #{sysUser.employmentId},
        </if>
        <if test="sysUser.organizationCode != null and sysUser.organizationCode !=''">
            ORGANIZATION_CODE = #{sysUser.organizationCode},
        </if>
        <if test="sysUser.passwordExpirationDate != null and sysUser.passwordExpirationDate !=''">
            PASSWORD_EXPIRATION_DATE = to_date(#{sysUser.passwordExpirationDate,jdbcType=VARCHAR},'yyyy/mm/dd
            hh24:mi:ss'),
        </if>
        <if test="sysUser.lastLoginDate != null and sysUser.lastLoginDate !=''">
            LAST_LOGIN_DATE = to_date(#{sysUser.lastLoginDate,jdbcType=VARCHAR},'yyyy/mm/dd hh24:mi:ss'),
        </if>
        <if test="sysUser.gender != null">
            GENDER=#{sysUser.gender,jdbcType=VARCHAR},
        </if>
        <if test="sysUser.idCard != null">
            ID_CARD=#{sysUser.idCard,jdbcType=VARCHAR},
        </if>
        <if test="sysUser.identifyType!=null">
            IDENTIFY_TYPE=#{sysUser.identifyType,jdbcType=VARCHAR},
        </if>
        <if test="sysUser.accountActivationDate!=null">
            ACCOUNT_ACTIVATION_DATE=#{sysUser.accountActivationDate,jdbcType=TIMESTAMP},
        </if>
        <if test="sysUser.accountExpirationDate!=null">
            ACCOUNT_EXPIRATION_DATE=#{sysUser.accountExpirationDate,jdbcType=TIMESTAMP},
        </if>
        <if test="sysUser.tokenExpirationTime!=null">
            TOKEN_EXPIRATION_TIME=#{sysUser.tokenExpirationTime,jdbcType=INTEGER},
        </if>
        USER_STATUS = #{sysUser.userStatus}
        where
        USER_ID = #{sysUser.userId} AND SYS_ID = 'DQM'
    </update>

    <update id="updatePasswordByUserId" parameterType="com.jykj.dqm.auth.entity.SysUser">
        update SYS_USER
        set password = #{sysUser.password}
        <if test="_databaseId == 'mysql' and sysUser.passwordExpirationDate != null">
            , PASSWORD_EXPIRATION_DATE=#{sysUser.passwordExpirationDate}
        </if>
        <if test="_databaseId == 'oracle' and sysUser.passwordExpirationDate != null">
            , PASSWORD_EXPIRATION_DATE=to_date(#{sysUser.passwordExpirationDate},'yyyy/mm/dd')
        </if>
        where user_id = #{sysUser.userId}
          AND SYS_ID = 'DQM'
    </update>
    <select id="queryList" resultMap="BaseResultMap" parameterType="java.lang.String"  databaseId="mysql">
        select * from SYS_USER
        where 1 = 1
        <if test="loginId != null and loginId!=''">
            AND login_id = #{loginId}
        </if>
        <if test="userName != null and userName!=''">
            AND user_name like CONCAT('%',#{userName},'%')
        </if>
        <if test="mobile != null and mobile!=''">
            AND mobile = #{mobile}
        </if>
        <if test="startTime != null and startTime!=''">
            AND create_time &gt;= str_to_date(#{startTime},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="endTime != null and endTime!=''">
            AND create_time &lt;= str_to_date(#{endTime},'%Y-%m-%d %H:%i:%s')
        </if>
        AND SYS_ID = 'DQM'
    </select>

    <select id="queryList" resultMap="BaseResultMap" parameterType="java.lang.String" databaseId="oracle">
        select * from SYS_USER
        where 1 = 1
        <if test="loginId != null and loginId!=''">
            AND login_id = #{loginId}
        </if>
        <if test="userName != null and userName!=''">
            AND user_name like '%'|| #{userName} ||'%'
        </if>
        <if test="mobile != null and mobile!=''">
            AND mobile = #{mobile}
        </if>
        <if test="startTime != null and startTime!=''">
            AND create_time &gt;= to_date(#{startTime},'yyyy/mm/dd hh24:mi:ss')
        </if>
        <if test="endTime != null and endTime!=''">
            AND create_time &lt;= to_date(#{endTime},'yyyy/mm/dd hh24:mi:ss')
        </if>
        AND SYS_ID = 'DQM'
    </select>

    <select id="getUserName" resultType="java.lang.String">
        SELECT DISTINCT USER_NAME
        FROM SYS_USER
        WHERE SYS_ID = 'DQM'
    </select>
    <select id="getUserById" resultType="java.util.Map">
        SELECT DISTINCT USER_NAME, LOGIN_ID
        FROM SYS_USER
        WHERE SYS_ID = 'DQM'
          AND USER_ID = #{id}
    </select>
    <select id="getUser" resultType="com.jykj.dqm.auth.entity.SysUser">
        SELECT *
        from SYS_USER
        WHERE SYS_ID = 'DQM'
          AND USER_ID = #{userId}
    </select>
    <select id="getUserNameById" resultType="java.lang.String">
        SELECT DISTINCT USER_NAME
        FROM SYS_USER
        WHERE SYS_ID = 'DQM'
          AND USER_ID = #{id}
    </select>

    <select id="queryUserInfo" resultType="com.jykj.dqm.auth.entity.SysUser">
        SELECT *
        FROM SYS_USER
        WHERE SYS_ID = 'DQM'
          and LOGIN_ID = #{loginId}
          and PASSWORD = #{password}
    </select>

    <select id="selectByUserAccount" resultMap="BaseResultMap">
        select *
        from SYS_USER
        WHERE login_id = #{UserAccount}
        and SYS_ID = 'DQM'
    </select>

    <select id="getUserInfoByLoginId" resultMap="BaseResultMap">
        SELECT *
        FROM SYS_USER
        WHERE LOGIN_ID = #{loginId}
        and SYS_ID = 'DQM'
    </select>

    <select id="selectByUserMobile" resultType="com.jykj.dqm.auth.entity.SysUser">
        select *
        from SYS_USER
        WHERE MOBILE = #{mobile}
          and SYS_ID = 'DQM'
        limit 1
    </select>
    <select id="selectByUserWxAccount" resultType="com.jykj.dqm.auth.entity.SysUser">
        SELECT *
        from SYS_USER s
                     LEFT JOIN USER_WX_ACCOUNT o on s.sys_ID = o.sys_id and s.login_id = o.user_account
        where s.sys_id = 'DQM'
          and o.sys_id = 'DQM'
          and o.wx_account = #{wxAccount}
        limit 1
    </select>
</mapper>
