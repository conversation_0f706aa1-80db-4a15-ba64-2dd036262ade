<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.auth.dao.UserWxAccountMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.auth.entity.UserWxAccount">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="USER_ACCOUNT" jdbcType="VARCHAR" property="userAccount"/>
        <result column="WX_ACCOUNT" jdbcType="VARCHAR" property="wxAccount"/>
        <result column="USER_NAME" jdbcType="VARCHAR" property="userName"/>
        <result column="SYS_ID" jdbcType="VARCHAR" property="sysId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, USER_ACCOUNT, WX_ACCOUNT, USER_NAME,SYS_ID
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from USER_WX_ACCOUNT
        where ID = #{id,jdbcType=INTEGER}
          and SYS_ID = 'DQM'
    </select>
    <select id="selectByWxAccount" resultType="com.jykj.dqm.auth.entity.UserWxAccount">
        select
        <include refid="Base_Column_List"/>
        from USER_WX_ACCOUNT
        where WX_ACCOUNT = #{wxAccount}
          and SYS_ID = 'DQM'
    </select>
    <select id="selectByUserAccount" resultType="com.jykj.dqm.auth.entity.UserWxAccount">
        select
        <include refid="Base_Column_List"/>
        from USER_WX_ACCOUNT
        where USER_ACCOUNT = #{userAccount}
          and SYS_ID = 'DQM'
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from USER_WX_ACCOUNT
        where ID = #{id,jdbcType=INTEGER}
          and SYS_ID = 'DQM'
    </delete>
    <delete id="deleteByUserAccount">
        delete
        from USER_WX_ACCOUNT
        where USER_ACCOUNT = #{loginId}
          and SYS_ID = 'DQM'
    </delete>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.jykj.dqm.auth.entity.UserWxAccount"
            useGeneratedKeys="true">
        insert into USER_WX_ACCOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userAccount != null">
                USER_ACCOUNT,
            </if>
            <if test="userName != null">
                USER_NAME,
            </if>
            <if test="wxAccount != null">
                WX_ACCOUNT,
            </if>
            <if test="sysId != null">
                SYS_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userAccount != null">
                #{userAccount,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="wxAccount != null">
                #{wxAccount,jdbcType=VARCHAR},
            </if>
            <if test="sysId != null">
                #{sysId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jykj.dqm.auth.entity.UserWxAccount">
        <!--@mbg.generated-->
        update USER_WX_ACCOUNT
        <set>
            <if test="userAccount != null">
                USER_ACCOUNT = #{userAccount,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                USER_NAME=#{userName,jdbcType=VARCHAR},
            </if>
            <if test="wxAccount != null">
                WX_ACCOUNT = #{wxAccount,jdbcType=VARCHAR},
            </if>
            <if test="sysId != null">
                SYS_ID = #{sysId,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into USER_WX_ACCOUNT
                (USER_ACCOUNT, USER_NAME, WX_ACCOUNT, SYS_ID)
                values
        <foreach collection="list" item="item" separator=",">
            (#{item.userAccount,jdbcType=VARCHAR}, #{item.userName,jdbcType=VARCHAR},
             #{item.wxAccount,jdbcType=VARCHAR}, #{item.sysId,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>