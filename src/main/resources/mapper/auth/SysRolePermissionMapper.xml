<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jykj.dqm.auth.dao.SysRolePermissionMapper">

    <resultMap id="BaseResultMap" type="com.jykj.dqm.auth.entity.SysRolePermission">
        <result column="GROUP_CODE" property="groupCode" jdbcType="VARCHAR"/>
        <result column="ROLE_ID" property="roleId" jdbcType="VARCHAR"/>
        <result column="PERMISSION_ID" property="permissionId" jdbcType="VARCHAR"/>
        <result column="ROLE_PERMISSION_ID" property="rolePermissionId" jdbcType="VARCHAR"/>
        <result column="ORGANIZATION_CODE" property="organizationCode" jdbcType="VARCHAR"/>
        <result column="SYS_ID" property="sysId" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="add" parameterType="com.jykj.dqm.auth.entity.SysRolePermission"  databaseId="mysql">
        insert  into SYS_ROLE_PERMISSION
        (GROUP_CODE,ROLE_ID,PERMISSION_ID,ORGANIZATION_CODE,SYS_ID)
        VALUES (
        #{sysRolePermission.groupCode,jdbcType=VARCHAR},
        #{sysRolePermission.roleId,jdbcType=VARCHAR},
        #{sysRolePermission.permissionId,jdbcType=VARCHAR},
        #{sysRolePermission.organizationCode,jdbcType=VARCHAR},
        #{sysRolePermission.sysId,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="add" parameterType="com.jykj.dqm.auth.entity.SysRolePermission">
        <selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="rolePermissionId" databaseId="oracle">
            SELECT SEQ_SYS_ROLE_PERMISSION.nextval as rolePermissionId from dual
        </selectKey>
        insert  into SYS_ROLE_PERMISSION
        (GROUP_CODE,ROLE_ID,PERMISSION_ID,ROLE_PERMISSION_ID,ORGANIZATION_CODE,SYS_ID)
        VALUES (
        #{sysRolePermission.groupCode,jdbcType=VARCHAR},
        #{sysRolePermission.roleId,jdbcType=VARCHAR},
        #{sysRolePermission.permissionId,jdbcType=VARCHAR},
        #{rolePermissionId},
        #{sysRolePermission.organizationCode,jdbcType=VARCHAR},
        #{sysRolePermission.sysId,jdbcType=VARCHAR}
        )
    </insert>
    
    <select id="queryList" resultMap="BaseResultMap">
      select  * from SYS_ROLE_PERMISSION WHERE SYS_ID = 'DQM'
    </select>

    <delete id="deleteByRoleId">
        delete  from SYS_ROLE_PERMISSION where role_id = #{roleId} AND  SYS_ID = 'DQM'
    </delete>
</mapper>