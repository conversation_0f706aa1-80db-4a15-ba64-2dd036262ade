<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.metadata.dao.MetadataDatasourceMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.metadata.entity.MetadataDatasource">
        <!--@mbg.generated-->
        <!--@Table DQM_METADATA_DATASOURCE-->
        <id column="DATA_SOURCE_ID" jdbcType="INTEGER" property="dataSourceId"/>
        <result column="DATA_SOURCE_NAME" jdbcType="VARCHAR" property="dataSourceName"/>
        <result column="DATA_SOURCE_DESCRIBE" jdbcType="VARCHAR" property="dataSourceDescribe"/>
        <result column="DATABASE_TYPE" jdbcType="VARCHAR" property="databaseType"/>
        <result column="DATABASE_DRIVER" jdbcType="VARCHAR" property="databaseDriver"/>
        <result column="DATABASE_URL" jdbcType="VARCHAR" property="databaseUrl"/>
        <result column="DATABASE_USER" jdbcType="VARCHAR" property="databaseUser"/>
        <result column="DATABASE_PWD" jdbcType="VARCHAR" property="databasePwd"/>
        <result column="DATABASE_NAME" jdbcType="VARCHAR" property="databaseName"/>
        <result column="DATABASE_SCHEMA" jdbcType="VARCHAR" property="databaseSchema"/>
        <result column="TESTSQL" jdbcType="VARCHAR" property="testsql"/>
        <result column="DRIVER_FILES" jdbcType="VARCHAR" property="driverFiles"/>
        <result column="TASK_GROUP_ID" jdbcType="VARCHAR" property="taskGroupId"/>
        <result column="SYS_CODE" jdbcType="VARCHAR" property="sysCode"/>
        <result column="SYS_NAME" jdbcType="VARCHAR" property="sysName"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        DATA_SOURCE_ID,
        DATA_SOURCE_NAME,
        DATA_SOURCE_DESCRIBE,
        DATABASE_TYPE,
        DATABASE_DRIVER,
        DATABASE_URL,
        DATABASE_USER,
        DATABASE_PWD,
        DATABASE_NAME,
        DATABASE_SCHEMA,
        TESTSQL,
        DRIVER_FILES,
        TASK_GROUP_ID,
        SYS_CODE,
        SYS_NAME
    </sql>

    <select id="getDatasourceByParams" resultMap="BaseResultMap">
        SELECT *
        FROM DQM_METADATA_DATASOURCE
        <where>
            <if test="_databaseId == 'mysql' and dataSourceName != null and dataSourceName != ''">
                and (DATA_SOURCE_NAME like CONCAT('%', #{dataSourceName}, '%') OR DATA_SOURCE_DESCRIBE like CONCAT('%', #{dataSourceName}, '%'))
            </if>
            <if test="_databaseId == 'oracle' and dataSourceName != null and dataSourceName != ''">
                and (DATA_SOURCE_NAME like '%'|| #{dataSourceName} ||'%'  OR DATA_SOURCE_DESCRIBE like '%'|| #{dataSourceName} ||'%')
            </if>
            <if test="databaseType != null and databaseType != ''">
                and DATABASE_TYPE = #{databaseType}
            </if>
            <if test="associatedTaskStatus != null and associatedTaskStatus == 1">
                and DATA_SOURCE_ID in (SELECT DISTINCT SUB_TASK_ID FROM DQM_TASK_GROUP_SUBTASKS)
            </if>
            <if test="associatedTaskStatus != null and associatedTaskStatus == 2">
                and DATA_SOURCE_ID not in (SELECT DISTINCT SUB_TASK_ID FROM DQM_TASK_GROUP_SUBTASKS)
            </if>
            <if test="whetherFilterCrossDb != null and whetherFilterCrossDb == 1">
                and upper(DATABASE_TYPE) != 'PRESTO'
            </if>
            <if test="whetherFilterCrossDb != null and whetherFilterCrossDb == 2">
                and upper(DATABASE_TYPE) = 'PRESTO'
            </if>
        </where>
    </select>

    <select id="firstPageList" resultType="java.util.Map" databaseId="mysql">
        SELECT t.DATA_SOURCE_ID                                dataSourceId,
               t.DATA_SOURCE_NAME                              dataSourceName,
               t.DATABASE_NAME                                 databaseName,
               t.DATABASE_SCHEMA                               databaseSchema,
               t.SYS_CODE                                      sysCode,
               t.SYS_NAME                                      sysName,
               DATE_FORMAT(t.UPDATE_TIME, '%Y-%m-%d %H:%i:%s') updateTime,
               (SELECT COUNT(*)
                FROM DQM_DATAQUALITY_CHECK_RULE
                WHERE SYS_CODE = t.SYS_CODE
                  AND DB_NM = t.DATABASE_NAME)                 ruleNum,
               t.DATABASE_TYPE                                 databaseType
        FROM DQM_METADATA_DATASOURCE t
        <where>
            <if test="sysOrDbname != null and sysOrDbname != ''">
                t.DATA_SOURCE_NAME like CONCAT('%', #{sysOrDbname}, '%')
                   OR t.SYS_NAME like CONCAT('%', #{sysOrDbname}, '%')
            </if>
            <if test="startDt != null">
                AND t.UPDATE_TIME <![CDATA[ >= ]]> #{startDt}
            </if>
            <if test="endDt != null">
                AND t.UPDATE_TIME <![CDATA[ <= ]]> #{endDt}
            </if>
        </where>
    </select>

    <select id="firstPageList" resultType="java.util.Map" databaseId="oracle">
        SELECT t.DATA_SOURCE_ID                                "dataSourceId",
               t.DATA_SOURCE_NAME                              "dataSourceName",
               t.DATABASE_NAME                                 "databaseName",
               t.DATABASE_SCHEMA                               "databaseSchema",
               t.SYS_CODE                                      "sysCode",
               t.SYS_NAME                                      "sysName",
               to_char(t.UPDATE_TIME, 'yyyy/mm/dd hh24:mi:ss') "updateTime",
               (SELECT COUNT(*)
                FROM DQM_DATAQUALITY_CHECK_RULE
                WHERE SYS_CODE = t.SYS_CODE
                  AND DB_NM = t.DATABASE_NAME)                 "ruleNum",
               t.DATABASE_TYPE                                 "databaseType"
        FROM DQM_METADATA_DATASOURCE t
        <where>
            <if test="sysOrDbname != null and sysOrDbname != ''">
                    t.DATA_SOURCE_NAME LIKE '%' || #{sysOrDbname} || '%'
                    OR t.SYS_NAME LIKE '%' || #{sysOrDbname} ||'%'
            </if>
            <if test="startDt != null">
                AND t.UPDATE_TIME <![CDATA[ >= ]]> #{startDt}
            </if>
            <if test="endDt != null">
                AND t.UPDATE_TIME <![CDATA[ <= ]]> #{endDt}
            </if>
            <if test="jobExeType != null and jobExeType != ''">
            </if>
        </where>
    </select>

    <select id="querySysAndDb" resultType="java.util.Map">
        SELECT t.SYS_NAME "sysName", t.SYS_CODE "sysCode", t.DATA_SOURCE_ID "dataSourceId", t.DATABASE_NAME "dbName"
        FROM DQM_METADATA_DATASOURCE t
    </select>
</mapper>