<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.metadata.dao.TaskGroupSubtasksMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.metadata.entity.TaskGroupSubtasks">
        <!--@mbg.generated-->
        <!--@Table DQM_TASK_GROUP_SUBTASKS-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="TASK_GROUP_ID" jdbcType="VARCHAR" property="taskGroupId"/>
        <result column="SUB_TASK_ID" jdbcType="VARCHAR" property="subTaskId"/>
        <result column="TASK_TYPE" jdbcType="VARCHAR" property="taskType"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        TASK_GROUP_ID,
        SUB_TASK_ID,
        TASK_TYPE
    </sql>

    <select id="getAllTaskGroupDataSourceId" resultType="java.lang.String">
        SELECT DISTINCT SUB_TASK_ID
        FROM DQM_TASK_GROUP_SUBTASKS
    </select>
</mapper>