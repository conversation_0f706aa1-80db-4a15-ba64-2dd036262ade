<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.quality.dao.CheckRuleExecutionMapper">
    <select id="getAllSystem" resultType="java.util.Map">
        SELECT DISTINCT SYS_CODE "sysCode", SYS_NAME "sysName", DB_NM "dbNm"
        FROM DQM_DATAQUALITY_CHECK_RULE
    </select>
    <select id="ruleRunInfoList" resultType="com.jykj.dqm.quality.entity.CheckRuleRunInfoVO">
        SELECT t1.CHECK_RULE_ID,
        t1.CHECK_RULE_NAME,
        t1.CHECK_RULE_DESC,
        t1.CHECK_RULE_FATHER_TYPE,
        t1.CHECK_RULE_TYPE,
        t1.CHECK_RULE_STATUS,
        t1.QST_TYPE,
        t1.CHECK_RULE_TABLE_OR_VIEW,
        t1.CHECK_RULE_COLUMN,
        <if test="_databaseId == 'mysql'">
            IFNULL( t2.JOB_EXE_TYPE,0) JOB_EXE_TYPE,
        </if>
        <if test="_databaseId == 'oracle'">
            nvl( t2.JOB_EXE_TYPE,0) JOB_EXE_TYPE,
        </if>
        t2.JOB_DESC,
        t2.JOB_START_DATE,
        t2.JOB_START_TIME,
        t2.JOB_END_DATE,
        t2.JOB_SPAN,
        t2.JOB_EXE_RATE,
        t2.DAY_OF_WEEK,
        t2.DAY_OF_MONTH,
        t2.WEEK_TIME,
        t2.DAY_OF_WEEK2,
        t2.CHK_TYPE,
        t2.CHK_MONTH
        FROM DQM_DATAQUALITY_CHECK_RULE t1
        LEFT JOIN DQM_SCHEDULE_JOB_TASK t2 ON t1.CHECK_RULE_ID = t2.BIZ_DATA_ID and t2.TASK_TYPE = 'QC'
        <where>
            t1.SYS_CODE = #{sysCode}
            and t1.CHECK_RULE_STATUS = '0'
            <if test="dbNm != null and dbNm != ''">
                and t1.DB_NM = #{dbNm}
            </if>
            <if test="qstType != null and qstType != ''">
                AND QST_TYPE = #{qstType}
            </if>
            <if test="checkRuleTableOrView != null and checkRuleTableOrView != ''">
                AND ST_CHECK_RULE_TABLE_OR_VIEW = #{checkRuleTableOrView}
            </if>
            <if test="checkRuleColumn != null and checkRuleColumn != ''">
                AND CHECK_RULE_COLUMN = #{checkRuleColumn}
            </if>
            <if test="checkRuleFatherType != null and checkRuleFatherType != ''">
                AND CHECK_RULE_FATHER_TYPE = #{checkRuleFatherType}
            </if>
            <if test="checkRuleType != null and checkRuleType != ''">
                AND CHECK_RULE_TYPE = #{checkRuleType}
            </if>
            <if test="_databaseId == 'mysql' and checkRuleName != null and checkRuleName != ''">
                AND CHECK_RULE_NAME like CONCAT('%', #{checkRuleName}, '%')
            </if>
            <if test="_databaseId == 'oracle' and checkRuleName != null and checkRuleName != ''">
                AND CHECK_RULE_NAME like '%'|| #{checkRuleName}||'%'
            </if>
        </where>
        ORDER BY CHECK_RULE_ID
    </select>

    <select id="getTable" resultType="java.lang.String">
        SELECT DISTINCT CHECK_RULE_TABLE_OR_VIEW
        FROM DQM_DATAQUALITY_CHECK_RULE WHERE SYS_CODE = #{sysCode}
        AND DB_NM = #{dbNm}
        <if test="_databaseId == 'mysql' and tableName != null and tableName != ''">
            AND CHECK_RULE_TABLE_OR_VIEW like CONCAT('%', #{tableName}, '%')
        </if>
        <if test="_databaseId == 'oracle' and tableName != null and tableName != ''">
            AND CHECK_RULE_TABLE_OR_VIEW like '%'|| #{tableName}|| '%'
        </if>
    </select>

    <select id="getTableField" resultType="java.lang.String">
        SELECT DISTINCT CHECK_RULE_COLUMN
        FROM DQM_DATAQUALITY_CHECK_RULE WHERE SYS_CODE = #{sysCode}
        AND DB_NM = #{dbNm}
        AND CHECK_RULE_TABLE_OR_VIEW = #{tableName}
        <if test="_databaseId == 'mysql' and fieldName != null and fieldName != ''">
            AND CHECK_RULE_COLUMN like CONCAT('%', #{fieldName}, '%')
        </if>
        <if test="_databaseId == 'oracle' and fieldName != null and fieldName != ''">
            AND CHECK_RULE_COLUMN like '%'|| #{fieldName} ||'%'
        </if>
    </select>

    <select id="getScheduleJobTaskByParams" resultType="com.jykj.dqm.quartz.entity.ScheduleJobInfo">
        SELECT JOB_ID, BIZ_DATA_ID
        FROM DQM_SCHEDULE_JOB_TASK
        <where>
            <if test="taskType != null and taskType != ''">
                and TASK_TYPE = #{taskType}
            </if>
            <if test="jobGroup != null and jobGroup != ''">
                and JOB_GROUP = #{jobGroup}
            </if>
        </where>
    </select>
</mapper>
