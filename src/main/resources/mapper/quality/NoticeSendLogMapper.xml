<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.quality.dao.NoticeSendLogMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.quality.entity.NoticeSendLog">
        <!--@mbg.generated-->
        <!--@Table DQM_NOTICE_SEND_LOG-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="DATA_QLTY_QSTN_ID" jdbcType="VARCHAR" property="dataQltyQstnId"/>
        <result column="ACCOUNT" jdbcType="VARCHAR" property="account"/>
        <result column="ACCOUNT_TYPE" jdbcType="VARCHAR" property="accountType"/>
        <result column="NOTICE_MSG" jdbcType="VARCHAR" property="noticeMsg"/>
        <result column="NOTICE_TIME" jdbcType="TIMESTAMP" property="noticeTime"/>
        <result column="NOTICE_RESULT" jdbcType="LONGVARCHAR" property="noticeResult"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        DATA_QLTY_QSTN_ID,
        ACCOUNT,
        ACCOUNT_TYPE,
        NOTICE_MSG,
        NOTICE_TIME,
        NOTICE_RESULT
    </sql>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.jykj.dqm.quality.entity.NoticeSendLog" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into DQM_NOTICE_SEND_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataQltyQstnId != null">
                DATA_QLTY_QSTN_ID,
            </if>
            <if test="account != null">
                ACCOUNT,
            </if>
            <if test="accountType != null">
                ACCOUNT_TYPE,
            </if>
            <if test="noticeMsg != null">
                NOTICE_MSG,
            </if>
            <if test="noticeTime != null">
                NOTICE_TIME,
            </if>
            <if test="noticeResult != null">
                NOTICE_RESULT,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataQltyQstnId != null">
                #{dataQltyQstnId,jdbcType=VARCHAR},
            </if>
            <if test="account != null">
                #{account,jdbcType=VARCHAR},
            </if>
            <if test="accountType != null">
                #{accountType,jdbcType=VARCHAR},
            </if>
            <if test="noticeMsg != null">
                #{noticeMsg,jdbcType=VARCHAR},
            </if>
            <if test="noticeTime != null">
                #{noticeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="noticeResult != null">
                #{noticeResult,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jykj.dqm.quality.entity.NoticeSendLog">
        <!--@mbg.generated-->
        update DQM_NOTICE_SEND_LOG
        <set>
            <if test="dataQltyQstnId != null">
                DATA_QLTY_QSTN_ID = #{dataQltyQstnId,jdbcType=VARCHAR},
            </if>
            <if test="account != null">
                ACCOUNT = #{account,jdbcType=VARCHAR},
            </if>
            <if test="accountType != null">
                ACCOUNT_TYPE = #{accountType,jdbcType=VARCHAR},
            </if>
            <if test="noticeMsg != null">
                NOTICE_MSG = #{noticeMsg,jdbcType=VARCHAR},
            </if>
            <if test="noticeTime != null">
                NOTICE_TIME = #{noticeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="noticeResult != null">
                NOTICE_RESULT = #{noticeResult,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>
</mapper>