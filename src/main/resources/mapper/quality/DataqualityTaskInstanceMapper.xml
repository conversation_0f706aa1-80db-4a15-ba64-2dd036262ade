<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.quality.dao.DataqualityTaskInstanceMapper">
    <select id="queryRecord" resultType="com.jykj.dqm.quality.entity.DataqualityTaskInstanceVO">
        SELECT t2.SYS_CODE sysCode,
        t2.SYS_NAME sysName,
        t2.DB_NM dbNm,
        t2.CHECK_RULE_NAME checkRuleName,
        t2.CHECK_RULE_DESC checkRuleDesc,
        t2.CHECK_RULE_TABLE_OR_VIEW checkRuleTableOrView,
        t2.CHECK_RULE_COLUMN checkRuleColumn,
        t1.TASK_STATE taskState,
        t1.TASK_RESULT taskResult,
        t1.TASK_START_DT startDt,
        t1.TASK_END_DT endDt,
        t2.CHECK_RULE_ID checkRuleId,
        t2.QST_TYPE qstType,
        t2.PB_SUBSIDIARY_SQL pbSubsidiarySql,
        t2.CHECK_RULE_FATHER_TYPE checkRuleFatherType,
        <if test="_databaseId == 'mysql'">
            IFNULL(t1.DATA_TOTAL_NUM,0) dataTotalNum,
            IFNULL(t1.DATA_QSTN_NUM,0) dataQstnNum,
        </if>
        <if test="_databaseId == 'oracle'">
            nvl(t1.DATA_TOTAL_NUM,0) dataTotalNum,
            nvl(t1.DATA_QSTN_NUM,0) dataQstnNum,
        </if>
        t1.TASK_GROUP_ID taskGroupId,
        t1.DATA_QLTY_QSTN_ID dataQltyQstnId
        FROM DQM_DATAQUALITY_TASK_INSTANCE t1
        LEFT JOIN DQM_DATAQUALITY_CHECK_RULE t2 ON t1.CHECK_RULE_ID = t2.CHECK_RULE_ID
        <where>
            <if test="sysCode != null and sysCode != ''">
                t2.SYS_CODE = #{sysCode}
            </if>
            <if test="dbNm != null and dbNm != ''">
                and t2.DB_NM = #{dbNm}
            </if>
            <if test="checkRuleTableOrView != null and checkRuleTableOrView != ''">
                and t2.CHECK_RULE_TABLE_OR_VIEW = #{checkRuleTableOrView}
            </if>
            <if test="checkRuleColumn != null and checkRuleColumn != ''">
                and t2.CHECK_RULE_COLUMN = #{checkRuleColumn}
            </if>
            <if test="_databaseId == 'mysql' and checkRuleName != null and checkRuleName != ''">
                and t2.CHECK_RULE_NAME like CONCAT('%', #{checkRuleName}, '%')
            </if>
            <if test="_databaseId == 'oracle' and checkRuleName != null and checkRuleName != ''">
                and t2.CHECK_RULE_NAME like'%'|| #{checkRuleName}|| '%'
            </if>
            <if test="startDt != null">
                and t1.TASK_START_DT >= #{startDt,jdbcType=TIMESTAMP}
            </if>
            <if test="endDt != null">
                and t1.TASK_START_DT &lt; #{endDt,jdbcType=TIMESTAMP}
            </if>
            <if test="ids != null and ids != ''">
                and t1.TASK_INSTANCE_ID in ${ids}
            </if>
        </where>
        ORDER BY t1.TASK_START_DT DESC
    </select>

    <update id="updateTaskState">
        UPDATE DQM_DATAQUALITY_TASK_INSTANCE
        SET TASK_STATE='99'
        WHERE TASK_STATE = '00'
        <if test="_databaseId == 'mysql'">
            AND TASK_START_DT &lt; DATE_SUB(SYSDATE(), INTERVAL 1 hour)
        </if>
        <if test="_databaseId == 'oracle'">
            AND TASK_START_DT &lt; SYSDATE - INTERVAL '1' HOUR
        </if>
    </update>
</mapper>