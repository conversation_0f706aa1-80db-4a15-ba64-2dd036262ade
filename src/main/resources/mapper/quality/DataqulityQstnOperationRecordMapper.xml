<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.quality.dao.DataqulityQstnOperationRecordMapper">
    <resultMap id="BaseResultMap" type="com.jykj.dqm.quality.entity.DataqulityQstnOperationRecord">
        <!--@mbg.generated-->
        <!--@Table DQM_DATAQULITY_QSTN_OPERATION_RECORD-->
        <id column="PK_ID" jdbcType="INTEGER" property="pkId"/>
        <result column="DATA_QLTY_QSTN_ID" jdbcType="VARCHAR" property="dataQltyQstnId"/>
        <result column="OPERATION_CONTENT" jdbcType="VARCHAR" property="operationContent"/>
        <result column="OPERATION_TIME" jdbcType="TIMESTAMP" property="operationTime"/>
        <result column="OPERATION_PERSON" jdbcType="VARCHAR" property="operationPerson"/>
        <result column="QSTN_RCTFCTN_STUS_CD" jdbcType="VARCHAR" property="qstnRctfctnStusCd"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        PK_ID,
        DATA_QLTY_QSTN_ID,
        OPERATION_CONTENT,
        OPERATION_TIME,
        OPERATION_PERSON,
        QSTN_RCTFCTN_STUS_CD
    </sql>
</mapper>