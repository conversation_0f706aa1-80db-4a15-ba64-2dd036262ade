<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jykj.dqm.quartz.dao.ScheduleJobTaskDao">
    <resultMap type="com.jykj.dqm.quartz.entity.ScheduleJobInfo" id="scheduleJobInfo">
    </resultMap>
    <select id="queryScheduleJobTask" resultMap="scheduleJobInfo">
        select T.JOB_ID            jobId,
               T.TASK_TYPE         taskType,
               T.JOB_NAME          jobName,
               T.JOB_GROUP         jobGroup,
               T.JOB_STATUS        jobStatus,
               T.CRON_EXPRESSION   cronExpression,
               T.JOB_DESC          jobDesc,
               T.BIZ_DATA_ID       bizDataId,
               T.CRON_SECOND       cronSecond,
               T.CRON_MINUTE       cronMinute,
               T.CRON_HOUR         cronHour,
               T.CRON_DAY_OF_MONTH cronDayOfMonth,
               T.CRON_DAY_OF_WEEK  cronDayOfWeek,
               T.CRON_MONTH        cronMonth,
               T.JOB_EXE_TYPE      jobExeType,
               T.JOB_EXE_RATE      jobExeRate,
               T.JOB_START_DATE    jobStartDate,
               T.JOB_END_DATE      jobEndDate,
               T.JOB_START_TIME    jobStartTime,
               T.JOB_SPAN          jobSpan,
               T.DAY_OF_WEEK       dayOfWeek,
               T.CHK_MONTH         chkMonth,
               T.CHK_TYPE          chkType,
               T.DAY_OF_MONTH      dayOfMonth,
               T.WEEK_TIME         weekTime,
               T.DAY_OF_WEEK2      dayOfWeek2,
               T.CREATE_DATE       createDate,
               T.JOB_PARAMS        jobParams,
               T.UPDATE_DATE       upDateDate
        from DQM_SCHEDULE_JOB_TASK T
        <where>
            <if test="jobId != null">
                and T.JOB_ID = #{jobId}
            </if>
            <if test="taskType != null">
                and T.TASK_TYPE = #{taskType}
            </if>
        </where>
    </select>

    <insert id="addScheduleJobTaskLog" databaseId="mysql">
        insert into DQM_SCHEDULE_JOB_TASK_LOG
                (LOG_ID,
                 JOB_ID,
                 TASK_TYPE,
                 JOB_NAME,
                 JOB_GROUP,
                 DESC_,
                 START_DATE)
        values (#{logId},
                #{jobId},
                #{taskType},
                #{jobName},
                #{jobGroup},
                #{desc_},
                now())
    </insert>

    <insert id="addScheduleJobTaskLog" databaseId="oracle">
        insert into DQM_SCHEDULE_JOB_TASK_LOG
        (LOG_ID,
         JOB_ID,
         TASK_TYPE,
         JOB_NAME,
         JOB_GROUP,
         DESC_,
         START_DATE)
        values (#{logId},
                #{jobId},
                #{taskType},
                #{jobName},
                #{jobGroup},
                #{desc_},
                sysdate)
    </insert>

    <update id="updateScheduleJobTaskLog">
        update DQM_SCHEDULE_JOB_TASK_LOG
                set DESC_ =#{desc_},
        <if test="_databaseId == 'oracle'">
            END_DATE = sysdate
        </if>
        <if test="_databaseId == 'mysql'">
            END_DATE =  now()
        </if>
        where LOG_ID = #{logId}
    </update>

    <insert id="addScheduleJobTask">
        insert into DQM_SCHEDULE_JOB_TASK
                (JOB_ID,
                 TASK_TYPE,
                 JOB_NAME,
                 JOB_GROUP,
                 JOB_STATUS,
                 CRON_EXPRESSION,
                 JOB_DESC,
                 BIZ_DATA_ID,
                 CRON_SECOND,
                 CRON_MINUTE,
                 CRON_HOUR,
                 CRON_DAY_OF_MONTH,
                 CRON_DAY_OF_WEEK,
                 CRON_MONTH,
                 JOB_EXE_TYPE,
                 JOB_EXE_RATE,
                 JOB_START_DATE,
                 JOB_END_DATE,
                 JOB_START_TIME,
                 JOB_SPAN,
                 DAY_OF_WEEK,
                 CHK_MONTH,
                 CHK_TYPE,
                 DAY_OF_MONTH,
                 WEEK_TIME,
                 DAY_OF_WEEK2,
                 JOB_PARAMS,
                 CREATE_DATE,
                 UPDATE_DATE)
                values
                (#{jobId},
                 #{taskType},
                 #{jobName},
                 #{jobGroup},
                 #{jobStatus},
                 #{cronExpression},
                 #{jobDesc},
                 #{bizDataId},
                 #{cronSecond},
                 #{cronMinute},
                 #{cronHour},
                 #{cronDayOfMonth},
                 #{cronDayOfWeek},
                 #{cronMonth},
                 #{jobExeType},
                 #{jobExeRate},
                 #{jobStartDate},
                 #{jobEndDate},
                 #{jobStartTime},
                 #{jobSpan},
                 #{dayOfWeek},
                 #{chkMonth},
                 #{chkType},
                 #{dayOfMonth},
                 #{weekTime},
                 #{dayOfWeek2},
                 #{jobParams},
        <if test="_databaseId == 'oracle'">
                sysdate,
                sysdate
        </if>
        <if test="_databaseId == 'mysql'">
                now(),
                now()
        </if>
        )
    </insert>

    <update id="updateScheduleJobTask">
        update DQM_SCHEDULE_JOB_TASK
                set JOB_NAME          = #{jobName},
                    JOB_GROUP         = #{jobGroup},
                    JOB_STATUS        = #{jobStatus},
                    CRON_EXPRESSION   = #{cronExpression},
                    JOB_DESC          = #{jobDesc},
                    BIZ_DATA_ID       = #{bizDataId},
                    CRON_SECOND       = #{cronSecond},
                    CRON_MINUTE       = #{cronMinute},
                    CRON_HOUR         = #{cronHour},
                    CRON_DAY_OF_MONTH = #{cronDayOfMonth},
                    CRON_DAY_OF_WEEK  = #{cronDayOfWeek},
                    CRON_MONTH        = #{cronMonth},
                    JOB_EXE_TYPE      = #{jobExeType},
                    JOB_EXE_RATE      = #{jobExeRate},
                    JOB_START_DATE    = #{jobStartDate},
                    JOB_END_DATE      = #{jobEndDate},
                    JOB_START_TIME    = #{jobStartTime},
                    JOB_SPAN          = #{jobSpan},
                    DAY_OF_WEEK       = #{dayOfWeek},
                    CHK_MONTH         = #{chkMonth},
                    CHK_TYPE          = #{chkType},
                    DAY_OF_MONTH      = #{dayOfMonth},
                    WEEK_TIME         = #{weekTime},
                    DAY_OF_WEEK2      = #{dayOfWeek2},
                    JOB_PARAMS        = #{jobParams},
        <if test="_databaseId == 'oracle'">
            UPDATE_DATE = sysdate
        </if>
        <if test="_databaseId == 'mysql'">
            UPDATE_DATE =  now()
        </if>
        where JOB_ID = #{jobId}
          and TASK_TYPE = #{taskType}
    </update>

    <delete id="deleteScheduleJobTaskLog">
        DELETE FROM DQM_SCHEDULE_JOB_TASK_LOG
        WHERE JOB_ID=#{jobId}
    </delete>

    <select id="queryScheduleJobTaskByJobGroupTaskType" resultMap="scheduleJobInfo">
        SELECT T.JOB_ID            jobId,
        T.TASK_TYPE         taskType,
        T.JOB_NAME          jobName,
        T.JOB_GROUP         jobGroup,
        T.JOB_STATUS        jobStatus,
        T.CRON_EXPRESSION   cronExpression,
        T.JOB_DESC          jobDesc,
        T.BIZ_DATA_ID       bizDataId,
        T.CRON_SECOND       cronSecond,
        T.CRON_MINUTE       cronMinute,
        T.CRON_HOUR         cronHour,
        T.CRON_DAY_OF_MONTH cronDayOfMonth,
        T.CRON_DAY_OF_WEEK  cronDayOfWeek,
        T.CRON_MONTH        cronMonth,
        T.JOB_EXE_TYPE      jobExeType,
        T.JOB_EXE_RATE      jobExeRate,
        T.JOB_START_DATE    jobStartDate,
        T.JOB_END_DATE      jobEndDate,
        T.JOB_START_TIME    jobStartTime,
        T.JOB_SPAN          jobSpan,
        T.DAY_OF_WEEK       dayOfWeek,
        T.CHK_MONTH         chkMonth,
        T.CHK_TYPE          chkType,
        T.DAY_OF_MONTH      dayOfMonth,
        T.WEEK_TIME         weekTime,
        T.DAY_OF_WEEK2      dayOfWeek2,
        T.CREATE_DATE       createDate,
        T.JOB_PARAMS        jobParams,
        T.UPDATE_DATE       upDateDate
        from DQM_SCHEDULE_JOB_TASK T
        <where>
            <if test="bizDataId != null">
                and T.BIZ_DATA_ID = #{bizDataId}
            </if>
            <if test="taskType != null">
                and T.TASK_TYPE = #{taskType}
            </if>
        </where>
    </select>
</mapper>
