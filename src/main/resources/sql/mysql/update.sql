-- 新增电子病历评级文档导出
INSERT INTO dqm.sys_config
(module_id, config_code, config_name, description, config_value, config_type, sort_code, deleted_flag, create_by, create_time, update_by, update_time, sys_id, web_show, module_name)
VALUES('3', 'hospital.code', '医院代码', '医院代码', '452183688', '1', 1, '0', '52', '2022-09-22 03:10:08', '52', NULL, 'DQM', 'Y', '系统');
INSERT INTO dqm.sys_config
(module_id, config_code, config_name, description, config_value, config_type, sort_code, deleted_flag, create_by, create_time, update_by, update_time, sys_id, web_show, module_name)
VALUES('3', 'hospital.name', '医院名称', '医院名称', '南充市中心医院', '1', 1, '0', '52', '2022-08-03 03:10:08', '52', NULL, 'DQM', 'Y', '系统');
INSERT INTO dqm.sys_permission
(group_code, organization_code, permission_id, permission_name, sys_id, description, parent_id)
VALUES('01', '001', 67, '电子病历评级文档管理', 'DQM', '/document-management', NULL);
INSERT INTO dqm.sys_permission
(group_code, organization_code, permission_id, permission_name, sys_id, description, parent_id)
VALUES('01', '001', 68, '等级字典配置', 'DQM', '/document-management/dictionary-configuration', 67);
INSERT INTO dqm.sys_permission
(group_code, organization_code, permission_id, permission_name, sys_id, description, parent_id)
VALUES('01', '001', 69, '文档目录配置', 'DQM', '/document-management/catalogue-configuration', 67);
INSERT INTO dqm.sys_permission
(group_code, organization_code, permission_id, permission_name, sys_id, description, parent_id)
VALUES('01', '001', 71, '要求项目名称字典', 'DQM', '/document-management/name-character', 67);
INSERT INTO dqm.sys_permission
(group_code, organization_code, permission_id, permission_name, sys_id, description, parent_id)
VALUES('01', '001', 72, '文档规则配置', 'DQM', '/document-management/rule-configuration', 67);
INSERT INTO dqm.sys_permission
(group_code, organization_code, permission_id, permission_name, sys_id, description, parent_id)
VALUES('01', '001', 73, '文档预览导出', 'DQM', '/document-management/document-review', 67);
INSERT INTO dqm.sys_permission
(group_code, organization_code, permission_id, permission_name, sys_id, description, parent_id)
VALUES('01', '001', 74, '文档导出记录', 'DQM', '/document-management/export-record', 67);
INSERT INTO dqm.sys_permission
(group_code, organization_code, permission_id, permission_name, sys_id, description, parent_id)
VALUES('01', '001', 75, '测试工具', 'DQM', '/test-tool', NULL);
INSERT INTO dqm.sys_permission
(group_code, organization_code, permission_id, permission_name, sys_id, description, parent_id)
VALUES('01', '001', 76, 'SQL语句测试', 'DQM', '/test-tool/sql-statement-test', 75);
INSERT INTO dqm.sys_codetab_type
(ID, TYPE_CODE, TYPE_NAME, TYPE_DESC, SYS_ID, CREATE_TIME, MODIFY_TIME)
VALUES(26, 'EMR_RULE_TYPE', '规则类型', '', 'DQM', '2023-04-19 09:23:37', NULL);
INSERT INTO dqm.sys_codetab_content
(ID, TYPE_CODE, CONTENT_KEY, CONTENT_VALUE, CONTENT_DESC, CONTENT_SEQ, SYS_ID, DATA1, DATA2, DATA3, DATA4, DATA5)
VALUES(89, 'EMR_RULE_TYPE', 'YZX', '一致性', '备用1表示默认告警系数', 0, 'DQM', '1', '', '', '', '');
INSERT INTO dqm.sys_codetab_content
(ID, TYPE_CODE, CONTENT_KEY, CONTENT_VALUE, CONTENT_DESC, CONTENT_SEQ, SYS_ID, DATA1, DATA2, DATA3, DATA4, DATA5)
VALUES(90, 'EMR_RULE_TYPE', 'WZX', '完整性', '备用1表示默认告警系数', 0, 'DQM', '1', '', '', '', '');
INSERT INTO dqm.sys_codetab_content
(ID, TYPE_CODE, CONTENT_KEY, CONTENT_VALUE, CONTENT_DESC, CONTENT_SEQ, SYS_ID, DATA1, DATA2, DATA3, DATA4, DATA5)
VALUES(91, 'EMR_RULE_TYPE', 'ZHX', '整合性', '备用1表示默认告警系数', 0, 'DQM', '1', '', '', '', '');
INSERT INTO dqm.sys_codetab_content
(ID, TYPE_CODE, CONTENT_KEY, CONTENT_VALUE, CONTENT_DESC, CONTENT_SEQ, SYS_ID, DATA1, DATA2, DATA3, DATA4, DATA5)
VALUES(92, 'EMR_RULE_TYPE', 'JSX', '及时性', '备用1表示默认告警系数', 0, 'DQM', '1', '', '', '', '');
INSERT INTO dqm.sys_codetab_content
(ID, TYPE_CODE, CONTENT_KEY, CONTENT_VALUE, CONTENT_DESC, CONTENT_SEQ, SYS_ID, DATA1, DATA2, DATA3, DATA4, DATA5)
VALUES(93, 'DBType', 'PRESTO', 'PRESTO', '', NULL, 'DQM', '', '', '', '', '');

ALTER TABLE DQM_EMR_DOCUMENT_RULE_CONFIGURATION ADD STRUCTURE_NAME1 varchar(100) NULL COMMENT '选择的字段对应表名/视图名1';
ALTER TABLE DQM_EMR_DOCUMENT_RULE_CONFIGURATION CHANGE STRUCTURE_NAME STRUCTURE_NAME2 varchar(100) COMMENT '选择的字段对应表名/视图名2（保存选择，更新时直接展示不用选了）';

-- 20230524 跨库查询单独处理修改
ALTER TABLE DQM_EMR_DOCUMENT_RULE_CONFIGURATION ADD WHETHER_CROSS_DB_QUERY char(1) default '0' COMMENT '是否跨库查询，0：否 ，1：是';
ALTER TABLE DQM_EMR_DOCUMENT_RULE_CONFIGURATION ADD CROSS_DB_QUERY_DATA_SOURCE_ID varchar(10)  COMMENT '跨库查询数据源ID';

ALTER TABLE DQM_METADATA_STRUCTURE_DETAIL MODIFY COLUMN REMARKS varchar(2000) NULL COMMENT '字段名注释';

INSERT INTO SYS_CODETAB_TYPE
(TYPE_CODE, TYPE_NAME, TYPE_DESC, SYS_ID, CREATE_TIME, MODIFY_TIME)
VALUES('ruleExeType', '执行类别', '规则执行类别', 'DQM', sysdate(), NULL);

INSERT INTO SYS_CODETAB_CONTENT
( TYPE_CODE, CONTENT_KEY, CONTENT_VALUE, CONTENT_DESC, CONTENT_SEQ, SYS_ID, DATA1, DATA2, DATA3, DATA4, DATA5)
VALUES('ruleExeType', '0', '手动执行', '', NULL, 'DQM', '', '', '', '', '');

INSERT INTO SYS_CODETAB_CONTENT
(TYPE_CODE, CONTENT_KEY, CONTENT_VALUE, CONTENT_DESC, CONTENT_SEQ, SYS_ID, DATA1, DATA2, DATA3, DATA4, DATA5)
VALUES('ruleExeType', '1', '自动执行', NULL, NULL, 'DQM', NULL, NULL, NULL, NULL, NULL);

UPDATE SYS_CODETAB_CONTENT
SET TYPE_CODE='EMR_RULE_TYPE', CONTENT_KEY='YZX', CONTENT_VALUE='一致性', CONTENT_DESC='备用1表示默认告警系数', CONTENT_SEQ=0, SYS_ID='DQM', DATA1='1', DATA2='', DATA3='', DATA4='', DATA5=''
WHERE TYPE_CODE='EMR_RULE_TYPE' and CONTENT_KEY='YZX';

UPDATE SYS_CODETAB_CONTENT
SET TYPE_CODE='EMR_RULE_TYPE', CONTENT_KEY='WZX', CONTENT_VALUE='完整性', CONTENT_DESC='备用1表示默认告警系数', CONTENT_SEQ=0, SYS_ID='DQM', DATA1='1', DATA2='', DATA3='', DATA4='', DATA5=''
WHERE TYPE_CODE='EMR_RULE_TYPE' and CONTENT_KEY='WZX';

UPDATE SYS_CODETAB_CONTENT
SET TYPE_CODE='EMR_RULE_TYPE', CONTENT_KEY='ZHX', CONTENT_VALUE='整合性', CONTENT_DESC='备用1表示默认告警系数', CONTENT_SEQ=0, SYS_ID='DQM', DATA1='1', DATA2='', DATA3='', DATA4='', DATA5=''
WHERE TYPE_CODE='EMR_RULE_TYPE' and CONTENT_KEY='ZHX';

UPDATE SYS_CODETAB_CONTENT
SET TYPE_CODE='EMR_RULE_TYPE', CONTENT_KEY='JSX', CONTENT_VALUE='及时性', CONTENT_DESC='备用1表示默认告警系数', CONTENT_SEQ=0, SYS_ID='DQM', DATA1='1', DATA2='', DATA3='', DATA4='', DATA5=''
WHERE TYPE_CODE='EMR_RULE_TYPE' and CONTENT_KEY='JSX';


INSERT INTO SYS_CONFIG
(module_id, config_code, config_name, description, config_value, config_type, sort_code, deleted_flag, create_by, create_time, update_by, update_time, sys_id, web_show, module_name)
VALUES('1', 'emr.admin.role', '电子病历管理员角色', '这些角色的用户，可以配置所有的电子病历规则', 'admin', '1', NULL, '0', NULL, '2023-02-08 02:20:07', NULL, NULL, 'DQM', 'Y', '设置');

ALTER TABLE DQM_EMR_DOCUMENT_RULE_CONFIGURATION ADD STRUCTURE_NAME1 varchar(100) NULL COMMENT '选择的字段对应表名/视图名1';
ALTER TABLE DQM_EMR_DOCUMENT_RULE_CONFIGURATION CHANGE STRUCTURE_NAME STRUCTURE_NAME2 varchar(100) COMMENT '选择的字段对应表名/视图名2（保存选择，更新时直接展示不用选了）';

-- 20230524 跨库查询单独处理修改
ALTER TABLE DQM_EMR_DOCUMENT_RULE_CONFIGURATION ADD WHETHER_CROSS_DB_QUERY char(1) default '0' COMMENT '是否跨库查询，0：否 ，1：是';
ALTER TABLE DQM_EMR_DOCUMENT_RULE_CONFIGURATION ADD CROSS_DB_QUERY_DATA_SOURCE_ID varchar(10)  COMMENT '跨库查询数据源ID';

ALTER TABLE DQM_METADATA_STRUCTURE_DETAIL MODIFY COLUMN REMARKS varchar(2000) NULL COMMENT '字段名注释';

INSERT INTO SYS_CODETAB_TYPE
(TYPE_CODE, TYPE_NAME, TYPE_DESC, SYS_ID, CREATE_TIME, MODIFY_TIME)
VALUES('ruleExeType', '执行类别', '规则执行类别', 'DQM', sysdate(), NULL);

INSERT INTO SYS_CODETAB_CONTENT
( TYPE_CODE, CONTENT_KEY, CONTENT_VALUE, CONTENT_DESC, CONTENT_SEQ, SYS_ID, DATA1, DATA2, DATA3, DATA4, DATA5)
VALUES('ruleExeType', '0', '手动执行', '', NULL, 'DQM', '', '', '', '', '');

INSERT INTO SYS_CODETAB_CONTENT
(TYPE_CODE, CONTENT_KEY, CONTENT_VALUE, CONTENT_DESC, CONTENT_SEQ, SYS_ID, DATA1, DATA2, DATA3, DATA4, DATA5)
VALUES('ruleExeType', '1', '自动执行', NULL, NULL, 'DQM', NULL, NULL, NULL, NULL, NULL);

UPDATE SYS_CODETAB_CONTENT
SET TYPE_CODE='EMR_RULE_TYPE', CONTENT_KEY='YZX', CONTENT_VALUE='一致性', CONTENT_DESC='备用1表示默认告警系数', CONTENT_SEQ=0, SYS_ID='DQM', DATA1='1', DATA2='', DATA3='', DATA4='', DATA5=''
WHERE TYPE_CODE='EMR_RULE_TYPE' and CONTENT_KEY='YZX';

UPDATE SYS_CODETAB_CONTENT
SET TYPE_CODE='EMR_RULE_TYPE', CONTENT_KEY='WZX', CONTENT_VALUE='完整性', CONTENT_DESC='备用1表示默认告警系数', CONTENT_SEQ=0, SYS_ID='DQM', DATA1='1', DATA2='', DATA3='', DATA4='', DATA5=''
WHERE TYPE_CODE='EMR_RULE_TYPE' and CONTENT_KEY='WZX';

UPDATE SYS_CODETAB_CONTENT
SET TYPE_CODE='EMR_RULE_TYPE', CONTENT_KEY='ZHX', CONTENT_VALUE='整合性', CONTENT_DESC='备用1表示默认告警系数', CONTENT_SEQ=0, SYS_ID='DQM', DATA1='1', DATA2='', DATA3='', DATA4='', DATA5=''
WHERE TYPE_CODE='EMR_RULE_TYPE' and CONTENT_KEY='ZHX';

UPDATE SYS_CODETAB_CONTENT
SET TYPE_CODE='EMR_RULE_TYPE', CONTENT_KEY='JSX', CONTENT_VALUE='及时性', CONTENT_DESC='备用1表示默认告警系数', CONTENT_SEQ=0, SYS_ID='DQM', DATA1='1', DATA2='', DATA3='', DATA4='', DATA5=''
WHERE TYPE_CODE='EMR_RULE_TYPE' and CONTENT_KEY='JSX';


-- 2023-07-06 新功能
INSERT INTO SYS_CONFIG
(module_id, config_code, config_name, description, config_value, config_type, sort_code, deleted_flag, create_by, create_time, update_by, update_time, sys_id, web_show, module_name)
VALUES('1', 'emr.admin.role', '电子病历管理员角色', '这些角色的用户，可以配置所有的电子病历规则', 'admin', '1', NULL, '0', NULL, '2023-02-08 02:20:07', NULL, NULL, 'DQM', 'Y', '设置');

CREATE TABLE `DQM_EMR_RULE_PERMISSION_CONFIGURATION`
(
    `ID`             int                                                          NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `USER_ACCOUNT`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
    `DIRECTORY_CODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '目录编码',
    `DIRECTORY_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '目录名称',
    `EMR_RULE_TYPE`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '规则类型',
    `CREATE_BY`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '创建人',
    `CREATE_TIME`    datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `UPDATE_BY`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '更新人',
    `UPDATE_TIME`    datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`ID`),
    KEY `DQM_RULE_PERMISSION_CONFIGURATION_USER_ACCOUNT_IDX` (`USER_ACCOUNT`, `DIRECTORY_CODE`, `DIRECTORY_NAME`, `EMR_RULE_TYPE`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 126
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='规则配置权限';

ALTER TABLE DQM_EMR_DOCUMENT_EXPORT_RECORD ADD TIMED_EXPORT_TIME DATETIME NULL COMMENT '定时导出时间';

UPDATE DQM_EMR_DOCUMENT_EXPORT_RECORD SET EXPORT_STATUS='3';

CREATE INDEX DQM_EMR_DOCUMENT_EXPORT_RECORD_EXPORT_STATUS_IDX USING BTREE ON DQM_EMR_DOCUMENT_EXPORT_RECORD (EXPORT_STATUS);

INSERT INTO SYS_PERMISSION
(group_code, organization_code,  permission_name, sys_id, description, parent_id)
VALUES('01', '001', '规则权限配置', 'DQM', '/document-management/rule-permission-configuration', 67);
-- 此处parent_id为上一个的permission_id
INSERT INTO SYS_PERMISSION
(group_code, organization_code,  permission_name, sys_id, description, parent_id)
VALUES('01', '001',  '查看页面', 'DQM', '/document-management/rule-permission-configuration/:view', 84);

-- 为DQM_METADATA_DATASOURCE表添加NEED_CROSS_QUERY字段
-- MySQL版本
ALTER TABLE DQM_METADATA_DATASOURCE ADD COLUMN NEED_CROSS_QUERY TINYINT(1) DEFAULT 0 COMMENT '是否需要跨库关联查询(0:否,1:是)';

-- 为DQM_EMR_DOCUMENT_RULE_CONFIGURATION表添加SQL说明字段
ALTER TABLE DQM_EMR_DOCUMENT_RULE_CONFIGURATION ADD COLUMN RECORDS_SQL_DESC TEXT COMMENT '记录数SQL说明' after RECORDS_SQL;
ALTER TABLE DQM_EMR_DOCUMENT_RULE_CONFIGURATION ADD COLUMN CONDITIONAL_RECORDS_SQL_DESC TEXT COMMENT '满足条件记录数SQL（完整记录/符合逻辑关系时间项/对照项可匹配数/与字典内容一致）说明' after CONDITIONAL_RECORDS_SQL;
